#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体管理器窗口控件识别问题调试脚本
专门分析坐标(349,268)处的控件识别问题
"""

import sys
import os
import time
import pyatspi

# 添加UNI模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from UNI import UNI

class FontManagerDebugger:
    """字体管理器控件识别调试器"""
    
    def __init__(self):
        self.uni = UNI()
        self.target_x = 349
        self.target_y = 268
        
    def debug_window_detection(self):
        """调试窗口检测过程"""
        print("=" * 80)
        print(f"🔍 调试字体管理器窗口控件识别问题")
        print(f"📍 目标坐标: ({self.target_x}, {self.target_y})")
        print("=" * 80)
        
        # 1. 检查X11窗口堆栈
        print("\n1️⃣ 检查X11窗口堆栈:")
        self._debug_x11_window_stack()
        
        # 2. 检查AT-SPI窗口列表
        print("\n2️⃣ 检查AT-SPI窗口列表:")
        self._debug_atspi_windows()
        
        # 3. 测试UNI控件识别
        print("\n3️⃣ 测试UNI控件识别:")
        self._debug_uni_detection()
        
        # 4. 测试重叠窗口检测
        print("\n4️⃣ 测试重叠窗口检测:")
        self._debug_overlapping_detection()
        
    def _debug_x11_window_stack(self):
        """调试X11窗口堆栈"""
        try:
            window_stack = self.uni.get_window_stack_x11()
            print(f"   📊 X11窗口堆栈包含 {len(window_stack)} 个窗口")
            
            matching_windows = []
            for i, xwin in enumerate(window_stack):
                try:
                    from gi.repository import Wnck
                    wnck_window = Wnck.Window.get(xwin.id)
                    if wnck_window:
                        geometry = wnck_window.get_geometry()
                        window_name = wnck_window.get_name()
                        window_pid = wnck_window.get_pid()
                        
                        # 检查坐标是否在窗口内
                        if (geometry.xp <= self.target_x < geometry.xp + geometry.widthp and
                            geometry.yp <= self.target_y < geometry.yp + geometry.heightp):
                            
                            matching_windows.append({
                                'index': i,
                                'name': window_name,
                                'pid': window_pid,
                                'geometry': (geometry.xp, geometry.yp, geometry.widthp, geometry.heightp),
                                'minimized': wnck_window.is_minimized(),
                                'visible': wnck_window.is_visible_on_workspace(Wnck.Screen.get_default().get_active_workspace())
                            })
                            
                            print(f"   ✅ 匹配窗口 #{i}: {window_name}")
                            print(f"      PID: {window_pid}")
                            print(f"      位置: {geometry.xp}, {geometry.yp}")
                            print(f"      大小: {geometry.widthp} x {geometry.heightp}")
                            print(f"      最小化: {wnck_window.is_minimized()}")
                            print(f"      可见: {wnck_window.is_visible_on_workspace(Wnck.Screen.get_default().get_active_workspace())}")
                            
                except Exception as e:
                    continue
                    
            if not matching_windows:
                print("   ❌ 在X11窗口堆栈中未找到包含目标坐标的窗口")
            else:
                print(f"   📈 找到 {len(matching_windows)} 个匹配窗口")
                # 显示最上层窗口
                top_window = matching_windows[0]
                print(f"   🔝 最上层窗口: {top_window['name']} (PID: {top_window['pid']})")
                
        except Exception as e:
            print(f"   ❌ X11窗口堆栈检查失败: {e}")
            
    def _debug_atspi_windows(self):
        """调试AT-SPI窗口列表"""
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            print(f"   📊 AT-SPI桌面包含 {desktop.childCount} 个应用")
            
            matching_windows = []
            
            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)
                    app_name = getattr(app, 'name', 'Unknown')
                    
                    for j in range(app.childCount):
                        try:
                            window = app.getChildAtIndex(j)
                            window_name = getattr(window, 'name', 'Unknown')
                            
                            # 获取窗口坐标
                            component = window.queryComponent()
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            
                            # 检查坐标是否在窗口内
                            if (extents.x <= self.target_x < extents.x + extents.width and
                                extents.y <= self.target_y < extents.y + extents.height):
                                
                                # 检查窗口状态
                                states = window.getState().getStates()
                                state_names = [pyatspi.stateToString(state) for state in states]
                                
                                window_info = {
                                    'app_name': app_name,
                                    'window_name': window_name,
                                    'app_index': i,
                                    'window_index': j,
                                    'coords': (extents.x, extents.y, extents.width, extents.height),
                                    'states': state_names,
                                    'process_id': window.get_process_id() if hasattr(window, 'get_process_id') else None,
                                    'role': window.getRoleName() if hasattr(window, 'getRoleName') else 'Unknown'
                                }
                                matching_windows.append(window_info)
                                
                                print(f"   ✅ 匹配窗口: {app_name} -> {window_name}")
                                print(f"      应用索引: {i}, 窗口索引: {j}")
                                print(f"      位置: {extents.x}, {extents.y}")
                                print(f"      大小: {extents.width} x {extents.height}")
                                print(f"      角色: {window_info['role']}")
                                print(f"      进程ID: {window_info['process_id']}")
                                print(f"      状态: {', '.join(state_names[:5])}...")  # 只显示前5个状态
                                
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    continue
                    
            if not matching_windows:
                print("   ❌ 在AT-SPI窗口列表中未找到包含目标坐标的窗口")
            else:
                print(f"   📈 找到 {len(matching_windows)} 个匹配窗口")
                
                # 分析窗口优先级
                print("\n   🔍 窗口优先级分析:")
                for i, win in enumerate(matching_windows):
                    priority_score = 0
                    priority_reasons = []
                    
                    # 检查是否是模态窗口
                    if 'modal' in win['states']:
                        priority_score += 100
                        priority_reasons.append("模态窗口")
                        
                    # 检查是否是活动窗口
                    if 'active' in win['states']:
                        priority_score += 50
                        priority_reasons.append("活动窗口")
                        
                    # 检查窗口大小（小窗口通常是对话框）
                    area = win['coords'][2] * win['coords'][3]
                    if area < 500000:  # 小于500k像素
                        priority_score += 30
                        priority_reasons.append("小窗口")
                        
                    # 检查窗口索引（后面的通常更上层）
                    priority_score += win['window_index'] * 10
                    priority_reasons.append(f"窗口索引{win['window_index']}")
                    
                    print(f"      窗口 #{i+1}: {win['window_name']}")
                    print(f"         优先级得分: {priority_score}")
                    print(f"         优先级原因: {', '.join(priority_reasons)}")
                    
        except Exception as e:
            print(f"   ❌ AT-SPI窗口列表检查失败: {e}")
            
    def _debug_uni_detection(self):
        """调试UNI控件识别"""
        try:
            print(f"   🔍 使用UNI.kdk_getElement_Uni检测坐标({self.target_x}, {self.target_y})")
            
            start_time = time.time()
            result, info_text = self.uni.kdk_getElement_Uni(self.target_x, self.target_y)
            detection_time = time.time() - start_time
            
            if result and not result.get('error'):
                print("   ✅ UNI检测成功:")
                print(f"      控件名称: {result.get('Name', 'Unknown')}")
                print(f"      控件角色: {result.get('Rolename', 'Unknown')}")
                print(f"      所属应用: {result.get('ApplicationName', 'Unknown')}")
                print(f"      所属进程: {result.get('ProcessName', 'Unknown')}")
                print(f"      窗口名称: {result.get('WindowName', 'Unknown')}")
                print(f"      控件坐标: {result.get('Coords', 'Unknown')}")
                print(f"      检测耗时: {detection_time:.3f}秒")
                
                # 分析是否识别正确
                process_name = result.get('ProcessName', '').lower()
                window_name = result.get('WindowName', '').lower()
                
                if 'font' in process_name or 'font' in window_name or '字体' in window_name:
                    print("   ✅ 正确识别为字体管理器相关控件")
                else:
                    print(f"   ❌ 可能识别错误，进程: {process_name}, 窗口: {window_name}")
                    
            else:
                print("   ❌ UNI检测失败")
                if result and result.get('error'):
                    print(f"      错误信息: {result['error']}")
                    
        except Exception as e:
            print(f"   ❌ UNI检测异常: {e}")
            import traceback
            traceback.print_exc()
            
    def _debug_overlapping_detection(self):
        """调试重叠窗口检测"""
        try:
            print(f"   🔍 使用重叠窗口检测方法")
            
            start_time = time.time()
            result = self.uni._find_topmost_overlapping_window(self.target_x, self.target_y)
            detection_time = time.time() - start_time
            
            if result[0] is not None:  # 如果找到窗口
                window, process_id, window_region, role_name, child_count = result
                
                print("   ✅ 重叠窗口检测成功:")
                print(f"      窗口名称: {getattr(window, 'name', 'Unknown')}")
                print(f"      窗口角色: {role_name}")
                print(f"      进程ID: {process_id}")
                print(f"      窗口区域: {window_region}")
                print(f"      子控件数: {child_count}")
                print(f"      检测耗时: {detection_time:.3f}秒")
                
                # 在该窗口中查找控件
                print("\n   🔍 在检测到的窗口中查找控件:")
                self.uni.childEle = None
                self.uni._find_accessible_at_point(window, self.target_x, self.target_y, window_region)
                
                if self.uni.childEle:
                    widget_info = self.uni._extract_element_info(self.uni.childEle)
                    print("   ✅ 在窗口中找到控件:")
                    print(f"      控件名称: {widget_info.get('Name', 'Unknown')}")
                    print(f"      控件角色: {widget_info.get('Rolename', 'Unknown')}")
                    print(f"      控件坐标: {widget_info.get('Coords', 'Unknown')}")
                else:
                    print("   ❌ 在窗口中未找到控件")
                    
            else:
                print("   ❌ 重叠窗口检测失败")
                
        except Exception as e:
            print(f"   ❌ 重叠窗口检测异常: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("字体管理器控件识别问题调试工具")
    print("请确保字体管理器窗口已打开，并且坐标(349,268)处有确认按钮")
    
    input("按回车键开始调试...")
    
    debugger = FontManagerDebugger()
    debugger.debug_window_detection()
    
    print("\n" + "=" * 80)
    print("🎯 调试完成！请查看上述输出分析问题原因。")
    print("=" * 80)

if __name__ == "__main__":
    main()
