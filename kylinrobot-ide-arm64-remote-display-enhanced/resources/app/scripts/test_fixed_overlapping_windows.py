#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的重叠窗口识别功能
验证坐标(552, 224)是否能正确识别到最上层的"打开文件"对话框
"""

import sys
import time
from pathlib import Path

# 添加scripts目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_coordinate_552_224():
    """测试修复后的坐标(552, 224)识别"""
    try:
        from UNI import UNI
        from auto_recording_manager import WidgetAnalyzer
        
        print("=" * 80)
        print("🔍 修复后的重叠窗口识别测试")
        print("=" * 80)
        print(f"测试坐标: (552, 224)")
        print("=" * 80)
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试1: 直接调用新的重叠窗口检测方法
        print("\n📍 测试1: 直接调用重叠窗口检测方法")
        print("-" * 50)
        
        start_time = time.time()
        window, process_id, window_region, role_name, child_count = uni._find_topmost_overlapping_window(552, 224)
        end_time = time.time()
        
        print(f"⏰ 耗时: {end_time - start_time:.3f}秒")
        if window:
            print(f"🎯 检测结果:")
            print(f"   窗口名称: {getattr(window, 'name', 'N/A')}")
            print(f"   窗口角色: {role_name}")
            print(f"   进程ID: {process_id}")
            print(f"   窗口区域: {window_region}")
            print(f"   子控件数: {child_count}")
        else:
            print("❌ 未找到窗口")
        
        # 测试2: 通过正常的UNI接口
        print("\n📍 测试2: 通过UNI.kdk_getElement_Uni接口")
        print("-" * 50)
        
        start_time = time.time()
        result, info = uni.kdk_getElement_Uni(552, 224, False, True)
        end_time = time.time()
        
        print(f"⏰ 耗时: {end_time - start_time:.3f}秒")
        if result and not result.get('error'):
            print(f"🎯 识别结果:")
            print(f"   控件名称: {result.get('Name', 'N/A')}")
            print(f"   控件类型: {result.get('ControlType', 'N/A')}")
            print(f"   进程名称: {result.get('ProcessName', 'N/A')}")
            print(f"   窗口名称: {result.get('WindowName', 'N/A')}")
            print(f"   父窗口路径: {result.get('ParentPath', 'N/A')}")
            
            # 检查是否识别到了对话框
            window_name = result.get('WindowName', '')
            control_name = result.get('Name', '')
            
            if '打开' in window_name or '文件' in window_name or 'Open' in window_name:
                print("✅ 成功识别到文件对话框！")
            elif '信任' in window_name or 'Trust' in window_name:
                print("⚠️  仍然识别到信任区对话框，可能需要进一步优化")
            else:
                print(f"📊 识别到其他窗口: {window_name}")
                
        else:
            print(f"❌ 识别失败: {info}")
        
        # 测试3: 通过WidgetAnalyzer
        print("\n📍 测试3: 通过WidgetAnalyzer接口")
        print("-" * 50)
        
        analyzer = WidgetAnalyzer(debug=True)
        start_time = time.time()
        widget_info, info_text = analyzer.analyze_widget_at(552, 224)
        end_time = time.time()
        
        print(f"⏰ 耗时: {end_time - start_time:.3f}秒")
        if widget_info and not widget_info.get('error'):
            print(f"🎯 识别结果:")
            print(f"   控件名称: {widget_info.get('Name', 'N/A')}")
            print(f"   控件类型: {widget_info.get('ControlType', 'N/A')}")
            print(f"   进程名称: {widget_info.get('ProcessName', 'N/A')}")
            print(f"   窗口名称: {widget_info.get('WindowName', 'N/A')}")
        else:
            print(f"❌ 识别失败: {info_text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def demonstrate_overlapping_window_logic():
    """演示重叠窗口识别逻辑"""
    try:
        from UNI import UNI
        
        print("\n" + "=" * 80)
        print("📚 重叠窗口识别逻辑演示")
        print("=" * 80)
        
        uni = UNI()
        
        # 获取桌面上所有窗口的信息
        print("🔍 分析桌面上所有可能的重叠窗口...")
        
        import pyatspi
        desktop = uni._get_fresh_desktop()
        if not desktop:
            print("❌ 无法获取桌面对象")
            return
            
        print(f"📱 桌面应用程序数量: {desktop.childCount}")
        
        all_windows = []
        x, y = 552, 224
        
        # 收集所有包含目标坐标的窗口
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if not app:
                    continue
                    
                app_name = getattr(app, 'name', 'N/A')
                print(f"\n🔍 检查应用程序: {app_name}")
                
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        if not window:
                            continue
                            
                        # 获取窗口信息
                        try:
                            component = window.queryComponent()
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            win_x, win_y, win_w, win_h = extents.x, extents.y, extents.width, extents.height
                            
                            # 检查点是否在窗口内
                            if win_x <= x <= win_x + win_w and win_y <= y <= win_y + win_h:
                                window_states = window.getState()
                                is_visible = not (window_states.contains(pyatspi.STATE_INVISIBLE) or 
                                                window_states.contains(pyatspi.STATE_ICONIFIED))
                                
                                if is_visible:
                                    window_info = {
                                        'app_name': app_name,
                                        'window_name': getattr(window, 'name', 'N/A'),
                                        'role': window.getRoleName() if hasattr(window, 'getRoleName') else 'N/A',
                                        'coords': (win_x, win_y, win_w, win_h),
                                        'area': win_w * win_h,
                                        'z_index': j,
                                        'is_modal': window_states.contains(pyatspi.STATE_MODAL),
                                        'is_active': window_states.contains(pyatspi.STATE_ACTIVE)
                                    }
                                    all_windows.append(window_info)
                                    
                                    print(f"   ✅ 匹配窗口: {window_info['window_name']}")
                                    print(f"      角色: {window_info['role']}")
                                    print(f"      坐标: ({win_x}, {win_y}, {win_w}, {win_h})")
                                    print(f"      面积: {window_info['area']:,} 像素")
                                    print(f"      Z-Index: {j}")
                                    print(f"      模态: {window_info['is_modal']}")
                                    print(f"      活动: {window_info['is_active']}")
                                    
                        except Exception as coord_e:
                            # 无法获取坐标，跳过
                            pass
                            
                    except Exception as window_e:
                        # 窗口访问失败，跳过
                        pass
                        
            except Exception as app_e:
                # 应用程序访问失败，跳过
                pass
        
        print(f"\n📊 分析结果:")
        print(f"   找到 {len(all_windows)} 个包含坐标({x}, {y})的窗口")
        
        if len(all_windows) > 1:
            print("   🚨 确认存在重叠窗口!")
            
            # 显示选择逻辑
            print("\n🤖 智能选择逻辑:")
            
            # 策略1: 模态对话框
            modal_windows = [w for w in all_windows if w['is_modal']]
            if modal_windows:
                print(f"   1️⃣ 模态窗口优先: 找到 {len(modal_windows)} 个模态窗口")
                selected = min(modal_windows, key=lambda w: w['area'])
                print(f"      选择: {selected['window_name']} (面积最小)")
            else:
                print("   1️⃣ 模态窗口优先: 未找到模态窗口")
                
                # 策略2: 小窗口（对话框）
                small_windows = [w for w in all_windows if w['area'] < 500000]
                if small_windows:
                    print(f"   2️⃣ 小窗口策略: 找到 {len(small_windows)} 个小窗口 (< 500k像素)")
                    selected = max(small_windows, key=lambda w: w['z_index'])
                    print(f"      选择: {selected['window_name']} (Z-Index最大: {selected['z_index']})")
                else:
                    print("   2️⃣ 小窗口策略: 未找到小窗口")
                    
                    # 策略3: Z-Index最大
                    selected = max(all_windows, key=lambda w: w['z_index'])
                    print(f"   3️⃣ Z-Index策略: 选择 {selected['window_name']} (Z-Index: {selected['z_index']})")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 重叠窗口识别修复效果测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print("\n请确保桌面上有重叠窗口的场景:")
    print("1. 打开信任区应用")
    print("2. 通过信任区打开文件对话框")
    print("3. 确保两个窗口都可见且重叠")
    
    print("\n开始自动测试...")
    
    # 基础识别测试
    test_coordinate_552_224()
    
    # 逻辑演示
    demonstrate_overlapping_window_logic()
    
    print("\n" + "=" * 80)
    print("✅ 测试完成!")
    print("=" * 80)
    print("\n📋 结果评估标准:")
    print("✅ 成功: 识别到'打开文件'或包含'文件'/'Open'的对话框窗口")
    print("⚠️  部分成功: 识别准确但可能需要微调")
    print("❌ 失败: 仍然识别到'信任区'等下层窗口")

if __name__ == "__main__":
    main()