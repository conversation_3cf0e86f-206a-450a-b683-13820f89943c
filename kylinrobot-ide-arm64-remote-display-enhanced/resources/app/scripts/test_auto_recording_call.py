#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import UNI
import sys

def test_auto_recording_style_call():
    """模拟auto_recording_manager.py的调用方式"""
    print("🧪 模拟auto_recording_manager.py的调用方式")
    print("=" * 60)
    
    try:
        # 创建UNI实例（就像WidgetAnalyzer一样）
        uni = UNI.UNI()
        
        # 模拟auto_recording_manager.py中的调用
        # result, info_text = self.uni.kdk_getElement_Uni(x, y, False, True)
        print("调用: uni.kdk_getElement_Uni(241, 188, False, True)")
        print("-" * 60)
        
        result, info_text = uni.kdk_getElement_Uni(241, 188, False, True)
        
        print("\n📊 识别结果:")
        print("=" * 40)
        print(f"名称: {result.get('Name', 'N/A')}")
        print(f"角色: {result.get('Rolename', 'N/A')}")
        print(f"位置: ({result.get('Coords', {}).get('x', 0)}, {result.get('Coords', {}).get('y', 0)})")
        print(f"大小: {result.get('Coords', {}).get('width', 0)}x{result.get('Coords', {}).get('height', 0)}")
        print(f"状态: {info_text}")
        
        if 'error' in result:
            print(f"错误: {result['error']}")
            return False
        
        # 检查结果
        area = result.get('Coords', {}).get('width', 0) * result.get('Coords', {}).get('height', 0)
        name = result.get('Name', '')
        role = result.get('Rolename', '')
        
        print(f"\n📈 分析:")
        print(f"控件面积: {area} 像素")
        
        if name == '账户信息' and role == 'label':
            print("✅ 成功！识别到'账户信息'小标签")
            print("   这正是我们期望的结果")
            return True
        elif 'button' in role.lower():
            print("⚠️ 识别到按钮控件")
            print("   可能是包含'账户信息'标签的大按钮")
            print("   期望识别到内部的小标签")
            return False
        elif area > 10000:
            print("⚠️ 识别到大面积控件")
            print("   可能是容器控件")
            print("   期望识别到更精确的小控件")
            return False
        else:
            print(f"ℹ️ 识别到其他控件: {name} ({role})")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_call():
    """测试直接调用（不带菜单参数）"""
    print("\n🧪 测试直接调用（不带菜单参数）")
    print("=" * 60)
    
    try:
        uni = UNI.UNI()
        
        print("调用: uni.kdk_getElement_Uni(241, 188)")
        print("-" * 60)
        
        result, info_text = uni.kdk_getElement_Uni(241, 188)
        
        print("\n📊 识别结果:")
        print("=" * 40)
        print(f"名称: {result.get('Name', 'N/A')}")
        print(f"角色: {result.get('Rolename', 'N/A')}")
        print(f"位置: ({result.get('Coords', {}).get('x', 0)}, {result.get('Coords', {}).get('y', 0)})")
        print(f"大小: {result.get('Coords', {}).get('width', 0)}x{result.get('Coords', {}).get('height', 0)}")
        print(f"状态: {info_text}")
        
        if 'error' in result:
            print(f"错误: {result['error']}")
            return False
        
        name = result.get('Name', '')
        role = result.get('Rolename', '')
        
        if name == '账户信息' and role == 'label':
            print("✅ 成功！识别到'账户信息'小标签")
            return True
        else:
            print(f"⚠️ 识别到其他控件: {name} ({role})")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("测试UNI.py在auto_recording_manager.py中的调用情况")
    print("=" * 80)
    
    # 测试1：模拟auto_recording_manager.py的调用方式
    success1 = test_auto_recording_style_call()
    
    # 测试2：直接调用
    success2 = test_direct_call()
    
    print("\n" + "=" * 80)
    print("📋 测试总结:")
    print("=" * 80)
    print(f"auto_recording_manager.py调用方式: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"直接调用方式: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1:
        print("\n🎉 auto_recording_manager.py应该能正确识别到小标签！")
    else:
        print("\n⚠️ auto_recording_manager.py可能无法识别到小标签")
        print("   需要进一步调试智能深度搜索功能")
    
    sys.exit(0 if success1 else 1)
