#!/usr/bin/env python3
"""
AutoRecordingManager生命周期管理器
当控件识别过慢（超过3秒）时自动清理并重启管理器
"""

import sys
import os
import time
import threading
import queue
import gc
import signal
import traceback
from typing import Optional, Callable, Dict, Any
from contextlib import redirect_stderr
from io import StringIO

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入AutoRecordingManager
try:
    from auto_recording_manager import AutoRecordingManager
except ImportError as e:
    print(f"[ERROR] 无法导入AutoRecordingManager: {e}", file=sys.stderr)
    sys.exit(1)


class PerformanceMonitor:
    """性能监控器，监控控件识别性能"""
    
    def __init__(self, slow_threshold: float = 3.0, max_slow_count: int = 1):
        self.slow_threshold = slow_threshold  # 慢识别阈值（秒）
        self.max_slow_count = max_slow_count  # 最大慢识别次数
        self.slow_count = 0  # 当前慢识别计数
        self.total_recognitions = 0  # 总识别次数
        self.slow_recognitions = 0  # 慢识别次数
        self.last_slow_time = 0  # 最后一次慢识别时间
        self.performance_callbacks = []  # 性能事件回调列表
        self.lock = threading.Lock()
        
    def add_performance_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """添加性能事件回调"""
        self.performance_callbacks.append(callback)
        
    def parse_performance_log(self, log_text: str):
        """解析性能日志文本"""
        try:
            with self.lock:
                self.total_recognitions += 1
                
                # 检查是否是严重性能警告
                if "🚨 严重性能警告 🚨" in log_text:
                    # 提取耗时信息
                    duration = None
                    for line in log_text.split('\n'):
                        if "⏰ 耗时:" in line:
                            try:
                                # 提取耗时数值 "⏰ 耗时: 3.037秒 (严重超时!)"
                                duration_str = line.split("⏰ 耗时:")[1].split("秒")[0].strip()
                                duration = float(duration_str)
                                break
                            except (IndexError, ValueError):
                                continue
                    
                    if duration and duration > self.slow_threshold:
                        self.slow_count += 1
                        self.slow_recognitions += 1
                        self.last_slow_time = time.time()
                        
                        print(f"[LIFECYCLE] 🐌 检测到慢识别: {duration:.3f}秒 (连续第{self.slow_count}次)", file=sys.stderr)
                        
                        # 触发性能事件回调
                        event_data = {
                            'type': 'slow_recognition',
                            'duration': duration,
                            'slow_count': self.slow_count,
                            'total_count': self.total_recognitions,
                            'threshold_exceeded': self.slow_count >= self.max_slow_count
                        }
                        
                        for callback in self.performance_callbacks:
                            try:
                                callback('slow_recognition', event_data)
                            except Exception as e:
                                print(f"[ERROR] 性能回调执行失败: {e}", file=sys.stderr)
                        
                        return True
                elif self.slow_count > 0:
                    # 正常识别且之前有慢识别，重置慢识别计数
                    print(f"[LIFECYCLE] ✅ 识别性能恢复，重置连续慢识别计数 (之前{self.slow_count}次)", file=sys.stderr) 
                    self.slow_count = 0
                    
        except Exception as e:
            print(f"[ERROR] 解析性能日志时发生错误: {e}", file=sys.stderr)
            
        return False
        
    def reset_counters(self):
        """重置计数器"""
        with self.lock:
            self.slow_count = 0
            
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self.lock:
            return {
                'total_recognitions': self.total_recognitions,
                'slow_recognitions': self.slow_recognitions,
                'current_slow_count': self.slow_count,
                'slow_threshold': self.slow_threshold,
                'max_slow_count': self.max_slow_count,
                'last_slow_time': self.last_slow_time
            }


class StderrCapture:
    """stderr捕获器，用于监控日志输出"""
    
    def __init__(self, performance_monitor: PerformanceMonitor):
        self.performance_monitor = performance_monitor
        self.original_stderr = sys.stderr
        self.buffer = StringIO()
        self.current_log_block = []
        self.in_performance_block = False
        
    def write(self, text):
        """写入文本"""
        # 同时写入原始stderr和缓冲区
        self.original_stderr.write(text)
        self.original_stderr.flush()
        
        # 检查是否是性能日志块开始
        if "🚨 严重性能警告 🚨" in text:
            self.in_performance_block = True
            self.current_log_block = [text]
        elif self.in_performance_block:
            self.current_log_block.append(text)
            # 检查是否是性能日志块结束（连续等号行且已收集足够内容）
            if ("=" * 80) in text and len(self.current_log_block) > 8:
                # 完整的性能日志块，进行解析
                full_log = ''.join(self.current_log_block)
                self.performance_monitor.parse_performance_log(full_log)
                self.current_log_block = []
                self.in_performance_block = False
        
        # 也检查单行中的性能信息（作为备用）
        if "⏰ 耗时:" in text and "严重超时" in text:
            self.performance_monitor.parse_performance_log(text)
                
    def flush(self):
        """刷新缓冲区"""
        self.original_stderr.flush()


class AutoRecordingLifecycleManager:
    """AutoRecordingManager生命周期管理器"""
    
    def __init__(self, 
                 slow_threshold: float = 3.0,
                 max_slow_count: int = 1,
                 restart_delay: float = 2.0,
                 max_restarts: int = 5,
                 restart_cooldown: float = 30.0):
        """
        初始化生命周期管理器
        
        Args:
            slow_threshold: 慢识别阈值（秒）
            max_slow_count: 最大慢识别次数
            restart_delay: 重启延迟时间（秒）
            max_restarts: 最大重启次数
            restart_cooldown: 重启冷却时间（秒）
        """
        self.slow_threshold = slow_threshold
        self.max_slow_count = max_slow_count
        self.restart_delay = restart_delay
        self.max_restarts = max_restarts
        self.restart_cooldown = restart_cooldown
        
        # 状态管理
        self.manager: Optional[AutoRecordingManager] = None
        self.running = False
        self.restart_count = 0
        self.last_restart_time = 0
        self.shutdown_requested = False
        
        # 性能监控
        self.performance_monitor = PerformanceMonitor(slow_threshold, max_slow_count)
        self.performance_monitor.add_performance_callback(self._on_performance_event)
        
        # stderr捕获
        self.stderr_capture = StderrCapture(self.performance_monitor)
        self.stderr_redirected = False
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 管理器配置
        self.manager_config = {
            'debug': False,
            'json_output': False,
            'storage_path': 'recordings'
        }
        
        # 扩展配置（用于录制控制）
        self.extended_config = {}
        
        print(f"[LIFECYCLE] 🚀 生命周期管理器初始化完成", file=sys.stderr)
        print(f"[LIFECYCLE] 📊 慢识别阈值: {slow_threshold}秒", file=sys.stderr)
        max_text = "第一次就重启" if max_slow_count == 1 else f"连续{max_slow_count}次重启"
        print(f"[LIFECYCLE] 🔄 重启策略: {max_text}", file=sys.stderr)
        
    def configure_manager(self, **kwargs):
        """配置管理器参数"""
        self.manager_config.update(kwargs)
        
    def set_extended_config(self, config_dict):
        """设置扩展配置（用于录制控制）"""
        self.extended_config.update(config_dict)
        
    def _on_performance_event(self, event_type: str, event_data: Dict[str, Any]):
        """性能事件回调"""
        if event_type == 'slow_recognition' and event_data.get('threshold_exceeded', False):
            print(f"[LIFECYCLE] 🚨 触发重启条件: 连续{event_data['slow_count']}次慢识别", file=sys.stderr)
            self._schedule_restart()
            
    def _schedule_restart(self):
        """安排重启"""
        def restart_worker():
            time.sleep(self.restart_delay)
            if not self.shutdown_requested:
                self._perform_restart()
                
        restart_thread = threading.Thread(target=restart_worker, daemon=True)
        restart_thread.start()
        
    def _perform_restart(self):
        """执行重启"""
        try:
            with self.lock:
                current_time = time.time()
                
                # 检查重启冷却时间
                if current_time - self.last_restart_time < self.restart_cooldown:
                    print(f"[LIFECYCLE] ⏳ 重启冷却中，跳过本次重启", file=sys.stderr)
                    return
                    
                # 检查最大重启次数
                if self.restart_count >= self.max_restarts:
                    print(f"[LIFECYCLE] 🛑 已达到最大重启次数({self.max_restarts})，停止重启", file=sys.stderr)
                    return
                    
                self.restart_count += 1
                self.last_restart_time = current_time
                
                print(f"[LIFECYCLE] 🔄 开始第{self.restart_count}次重启...", file=sys.stderr)
                
                # 停止当前管理器
                self._stop_manager()
                
                # 等待一段时间确保资源清理
                time.sleep(1.0)
                
                # 启动新管理器
                self._start_manager()
                
                # 重置性能计数器
                self.performance_monitor.reset_counters()
                
                print(f"[LIFECYCLE] ✅ 第{self.restart_count}次重启完成", file=sys.stderr)
                
        except Exception as e:
            print(f"[ERROR] 重启过程中发生错误: {e}", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            
    def _start_manager(self):
        """启动管理器"""
        try:
            # 确保DISPLAY环境变量设置正确
            if 'DISPLAY' not in os.environ or not os.environ['DISPLAY']:
                os.environ['DISPLAY'] = ':0'
                print(f"[LIFECYCLE] 设置DISPLAY环境变量为: {os.environ['DISPLAY']}", file=sys.stderr)
            
            print(f"[LIFECYCLE] 🚀 启动AutoRecordingManager...", file=sys.stderr)
            
            # 创建新的管理器实例
            self.manager = AutoRecordingManager(**self.manager_config)
            
            # 处理扩展配置
            if self.extended_config.get('testcase_path'):
                print(f"[LIFECYCLE] 设置testcase路径: {self.extended_config['testcase_path']}", file=sys.stderr)
                self.manager.setup_locator_generator(self.extended_config['testcase_path'])
            
            # 启动录制
            test_case_id = self.extended_config.get('test_case_id')
            testcase_path = self.extended_config.get('testcase_path')
            
            if self.manager.start_recording(test_case_id, testcase_path):
                print(f"[LIFECYCLE] ✅ 录制已启动", file=sys.stderr)
                
                # 如果有duration参数，启动定时器自动停止录制
                if 'duration' in self.extended_config:
                    duration = self.extended_config['duration']
                    print(f"[LIFECYCLE] 设置录制时长: {duration}秒，将自动停止", file=sys.stderr)
                    
                    def auto_stop_recording():
                        time.sleep(duration)
                        if self.manager and self.manager.is_recording:
                            print(f"[LIFECYCLE] ⏰ 录制时长已到，自动停止录制", file=sys.stderr)
                            session_id = self.manager.stop_recording()
                            if session_id:
                                print(f"[LIFECYCLE] 📁 录制已保存，会话ID: {session_id}", file=sys.stderr)
                    
                    # 启动自动停止定时器
                    auto_stop_thread = threading.Thread(target=auto_stop_recording, daemon=True)
                    auto_stop_thread.start()
            else:
                print(f"[LIFECYCLE] ❌ 录制启动失败", file=sys.stderr)
            
            print(f"[LIFECYCLE] ✅ AutoRecordingManager启动成功", file=sys.stderr)
            
        except Exception as e:
            print(f"[ERROR] 启动管理器时发生错误: {e}", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            self.manager = None
            
    def _stop_manager(self):
        """停止管理器"""
        try:
            if self.manager:
                print(f"[LIFECYCLE] 🛑 停止AutoRecordingManager...", file=sys.stderr)
                
                # 停止录制
                self.manager.stop_recording()
                
                # 清理资源
                if hasattr(self.manager, 'cleanup'):
                    self.manager.cleanup()
                    
                self.manager = None
                
                # 强制垃圾回收
                gc.collect()
                
                print(f"[LIFECYCLE] ✅ AutoRecordingManager已停止", file=sys.stderr)
                
        except Exception as e:
            print(f"[ERROR] 停止管理器时发生错误: {e}", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            
    def _redirect_stderr(self):
        """重定向stderr以监控性能日志"""
        if not self.stderr_redirected:
            sys.stderr = self.stderr_capture
            self.stderr_redirected = True
            
    def _restore_stderr(self):
        """恢复原始stderr"""
        if self.stderr_redirected:
            sys.stderr = self.stderr_capture.original_stderr
            self.stderr_redirected = False
            
    def start(self):
        """启动生命周期管理器"""
        try:
            with self.lock:
                if self.running:
                    print(f"[LIFECYCLE] ⚠️ 生命周期管理器已在运行", file=sys.stderr)
                    return
                    
                self.running = True
                self.shutdown_requested = False
                
            print(f"[LIFECYCLE] 🎯 启动生命周期管理器", file=sys.stderr)
            
            # 重定向stderr以监控性能
            self._redirect_stderr()
            
            # 启动管理器
            self._start_manager()
            
            print(f"[LIFECYCLE] ✅ 生命周期管理器启动完成", file=sys.stderr)
            
        except Exception as e:
            print(f"[ERROR] 启动生命周期管理器时发生错误: {e}", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            self.running = False
            
    def stop(self):
        """停止生命周期管理器"""
        try:
            with self.lock:
                if not self.running:
                    return
                    
                self.running = False  
                self.shutdown_requested = True
                
            print(f"[LIFECYCLE] 🛑 停止生命周期管理器", file=sys.stderr)
            
            # 停止管理器
            self._stop_manager()
            
            # 恢复stderr
            self._restore_stderr()
            
            print(f"[LIFECYCLE] ✅ 生命周期管理器已停止", file=sys.stderr)
            
        except Exception as e:
            print(f"[ERROR] 停止生命周期管理器时发生错误: {e}", file=sys.stderr) 
            traceback.print_exc(file=sys.stderr)
            
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self.running
        
    def get_status(self) -> Dict[str, Any]:
        """获取状态信息"""
        stats = self.performance_monitor.get_statistics()
        return {
            'running': self.running,
            'manager_active': self.manager is not None,
            'restart_count': self.restart_count,
            'last_restart_time': self.last_restart_time,
            'performance_stats': stats,
            'config': {
                'slow_threshold': self.slow_threshold,
                'max_slow_count': self.max_slow_count,
                'max_restarts': self.max_restarts
            }
        }
        
    def reset_restart_count(self):
        """重置重启计数"""
        with self.lock:
            self.restart_count = 0
            self.last_restart_time = 0
            print(f"[LIFECYCLE] 🔄 重置重启计数", file=sys.stderr)


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n[LIFECYCLE] 🛑 收到信号 {signum}，正在优雅退出...", file=sys.stderr)
    global lifecycle_manager
    if lifecycle_manager:
        lifecycle_manager.stop()
    sys.exit(0)


# 全局变量
lifecycle_manager: Optional[AutoRecordingLifecycleManager] = None


def main():
    """主函数"""
    global lifecycle_manager
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        print("=" * 60, file=sys.stderr)
        print("🚀 AutoRecording生命周期管理器启动", file=sys.stderr)
        print("=" * 60, file=sys.stderr)
        
        # 创建生命周期管理器
        lifecycle_manager = AutoRecordingLifecycleManager(
            slow_threshold=3.0,    # 3秒慢识别阈值
            max_slow_count=3,      # 最多3次慢识别触发重启
            restart_delay=2.0,     # 重启延迟2秒
            max_restarts=5,        # 最多重启5次
            restart_cooldown=30.0  # 重启冷却30秒
        )
        
        # 配置管理器参数
        lifecycle_manager.configure_manager(debug=True, test_mode=False)
        
        # 启动生命周期管理器
        lifecycle_manager.start()
        
        print(f"[LIFECYCLE] 📊 按Ctrl+C退出程序", file=sys.stderr)
        
        # 主循环
        while lifecycle_manager.is_running():
            time.sleep(5)
            
            # 定期输出状态信息
            status = lifecycle_manager.get_status()
            print(f"[LIFECYCLE] 📈 状态: 运行中 | 重启次数: {status['restart_count']} | "
                  f"总识别: {status['performance_stats']['total_recognitions']} | "
                  f"慢识别: {status['performance_stats']['slow_recognitions']}", file=sys.stderr)
            
    except KeyboardInterrupt:
        print(f"\n[LIFECYCLE] 🛑 用户中断，正在退出...", file=sys.stderr)
    except Exception as e:
        print(f"[ERROR] 主程序发生错误: {e}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
    finally:
        if lifecycle_manager:
            lifecycle_manager.stop()
        print(f"[LIFECYCLE] 👋 程序已退出", file=sys.stderr)


if __name__ == "__main__":
    main()