#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import sys
import time
import threading
from typing import Dict, Any, Optional, Tuple

# 导入UNI模块
try:
    import UNI
    UNI_AVAILABLE = True
except ImportError:
    UNI_AVAILABLE = False
    print("UNI模块不可用")

class TestWidgetAnalyzer:
    """
    测试版本的WidgetAnalyzer，模拟auto_recording_manager.py的完整流程
    """
    
    def __init__(self, debug: bool = True):
        self.debug = debug
        self.uni = None
        
        if UNI_AVAILABLE:
            try:
                self.uni = UNI.UNI()
                if self.debug:
                    print("[DEBUG] 测试版UNI控件分析器初始化成功", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] UNI控件分析器初始化失败: {e}", file=sys.stderr)
        else:
            print("[ERROR] UNI模块不可用，控件分析功能将被禁用", file=sys.stderr)

    def _should_skip_widget_analysis(self, x: int, y: int) -> bool:
        """
        检查是否应该跳过控件识别以避免卡顿
        """
        try:
            # 简化版本，只检查基本边缘
            if x < 10 or y < 10:
                if self.debug:
                    print(f"[DEBUG] ⚡ 跳过屏幕边缘区域的控件识别: ({x}, {y})", file=sys.stderr)
                return True
            return False
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 快速检查失败，继续正常识别: {e}", file=sys.stderr)
            return False

    def analyze_widget_at(self, x: int, y: int, interrupt_flag: threading.Event = None) -> Tuple[Optional[Dict[str, Any]], str]:
        """
        完全模拟auto_recording_manager.py的analyze_widget_at方法
        """
        if not self.uni:
            return None, "UNI模块不可用"

        try:
            if self.debug:
                print(f"[DEBUG] 使用优化后的UNI模块进行控件识别: 坐标({x}, {y})", file=sys.stderr)

            # 🚀 性能优化：快速检查是否为可能导致卡顿的区域
            if self._should_skip_widget_analysis(x, y):
                return None, "跳过可能导致卡顿的控件识别"

            # 添加超时机制，防止长时间阻塞
            start_time = time.time()
            timeout = 3.0  # 3秒超时

            # 🚀 优化：智能缓存清理，只在必要时清除
            if hasattr(self.uni, 'desktop_cache') and self.uni.desktop_cache is not None and isinstance(self.uni.desktop_cache, dict) and 'desktop_object' in self.uni.desktop_cache:
                cache_entry = self.uni.desktop_cache['desktop_object']
                if time.time() - cache_entry['time'] > 0.3:  # 300ms超时
                    del self.uni.desktop_cache['desktop_object']
                    if self.debug:
                        print(f"[DEBUG] 已清除过期的桌面缓存", file=sys.stderr)

            # 如果UNI模块有_get_fresh_desktop方法，先触发桌面刷新
            if hasattr(self.uni, '_get_fresh_desktop'):
                if time.time() - start_time > timeout:
                    print(f"[WARNING] 控件识别超时，跳过桌面刷新", file=sys.stderr)
                    return None, "控件识别超时"

                if interrupt_flag and interrupt_flag.is_set():
                    print(f"[INFO] 控件识别被中断（桌面刷新前）", file=sys.stderr)
                    return None, "控件识别被中断"

                desktop = self.uni._get_fresh_desktop()
                if self.debug:
                    app_count = desktop.childCount if desktop else 0
                    print(f"[DEBUG] 已触发桌面刷新，应用数: {app_count}", file=sys.stderr)

            # 再次检查超时
            if time.time() - start_time > timeout:
                print(f"[WARNING] 控件识别超时，跳过UNI识别", file=sys.stderr)
                return None, "控件识别超时"

            # 检查是否被中断
            if interrupt_flag and interrupt_flag.is_set():
                print(f"[INFO] 控件识别被中断（UNI识别前）", file=sys.stderr)
                return None, "控件识别被中断"

            # 🎯 关键调用：直接使用优化后的UNI模块，启用菜单控件识别
            print(f"[DEBUG] 🎯 调用 uni.kdk_getElement_Uni({x}, {y}, False, True)", file=sys.stderr)
            result, info_text = self.uni.kdk_getElement_Uni(x, y, False, True)

            # 检查是否被中断
            if interrupt_flag and interrupt_flag.is_set():
                print(f"[INFO] 控件识别被中断（UNI识别后）", file=sys.stderr)
                return None, "控件识别被中断"

            if result and not result.get('error'):
                if self.debug:
                    process_name = result.get('ProcessName', 'Unknown')
                    control_name = result.get('Name', 'N/A')
                    control_role = result.get('Rolename', 'N/A')
                    coords = result.get('Coords', {})
                    width = coords.get('width', 0)
                    height = coords.get('height', 0)
                    area = width * height
                    print(f"[DEBUG] ✅ UNI模块识别成功: {control_name} ({control_role}) "
                          f"面积:{area} 进程:{process_name}", file=sys.stderr)
                return result, "成功"
            else:
                error_msg = result.get('error', '未知错误') if result else '无返回结果'
                if self.debug:
                    print(f"[DEBUG] ❌ UNI模块识别失败: {error_msg}", file=sys.stderr)
                return None, error_msg

        except Exception as e:
            error_msg = f"UNI模块控件识别时发生错误: {e}"
            print(f"[ERROR] {error_msg}", file=sys.stderr)
            if self.debug:
                import traceback
                traceback.print_exc(file=sys.stderr)
            return None, error_msg

def test_auto_recording_flow():
    """测试auto_recording_manager.py的完整流程"""
    print("🧪 测试auto_recording_manager.py的完整控件识别流程")
    print("=" * 70)
    
    # 创建测试分析器
    analyzer = TestWidgetAnalyzer(debug=True)
    
    if not analyzer.uni:
        print("❌ UNI模块初始化失败，无法进行测试")
        return False
    
    # 测试坐标 (241, 188) - 账户信息标签
    test_x, test_y = 241, 188
    
    print(f"\n🎯 测试坐标: ({test_x}, {test_y})")
    print("-" * 50)
    
    try:
        # 模拟完整的控件识别流程
        result, status = analyzer.analyze_widget_at(test_x, test_y)
        
        print(f"\n📊 识别结果:")
        print("=" * 40)
        
        if result:
            print(f"名称: {result.get('Name', 'N/A')}")
            print(f"角色: {result.get('Rolename', 'N/A')}")
            print(f"位置: ({result.get('Coords', {}).get('x', 0)}, {result.get('Coords', {}).get('y', 0)})")
            print(f"大小: {result.get('Coords', {}).get('width', 0)}x{result.get('Coords', {}).get('height', 0)}")
            print(f"进程: {result.get('ProcessName', 'N/A')}")
            print(f"状态: {status}")
            
            # 检查是否识别到了期望的小标签
            name = result.get('Name', '')
            role = result.get('Rolename', '')
            area = result.get('Coords', {}).get('width', 0) * result.get('Coords', {}).get('height', 0)
            
            if name == '账户信息' and role == 'label':
                print(f"\n🎉 成功！auto_recording_manager.py流程正确识别到'账户信息'小标签")
                print(f"   面积: {area} 像素（小控件）")
                return True
            elif area > 10000:
                print(f"\n⚠️ 识别到大控件（面积: {area}），期望识别到小标签")
                return False
            else:
                print(f"\n ℹ️ 识别到其他控件: {name} ({role})")
                return True
        else:
            print(f"❌ 识别失败: {status}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_auto_recording_flow()
    
    print(f"\n{'='*70}")
    print(f"📋 测试结果: {'✅ 通过' if success else '❌ 失败'}")
    print(f"{'='*70}")
    
    if success:
        print("🎉 auto_recording_manager.py的控件识别流程工作正常！")
        print("   如果您在实际使用中仍然无法感知到小标签，")
        print("   可能是界面状态或坐标位置的问题。")
    else:
        print("⚠️ auto_recording_manager.py的控件识别流程存在问题")
        print("   需要进一步调试。")
    
    sys.exit(0 if success else 1)
