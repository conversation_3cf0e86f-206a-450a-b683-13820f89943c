#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试控件类别文字显示功能
"""

import sys
import time
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def test_highlight_with_label():
    """测试带文字标签的高亮显示功能"""
    print("\n🧪 测试控件类别文字显示功能")
    print("=" * 50)

    try:
        # 导入模块
        from widget_capture_module import HighlightRenderer

        # 创建高亮渲染器
        print("1. 创建高亮渲染器...")
        renderer = HighlightRenderer(debug=True)

        # 检查是否成功初始化
        if not renderer.display:
            print("⚠️ 高亮渲染器初始化失败（可能是在无图形环境中运行）")
            print("   这是正常的，功能在有图形界面的环境中可以正常工作")
            return True

        print("✅ 高亮渲染器创建成功")

        # 模拟控件信息
        test_widgets = [
            {
                "Name": "登录按钮",
                "Rolename": "push button",
                "Description": "点击进行登录",
                "Coords": {"x": 200, "y": 200, "width": 100, "height": 40}
            },
            {
                "Name": "用户名",
                "Rolename": "entry",
                "Description": "输入用户名",
                "Coords": {"x": 350, "y": 250, "width": 150, "height": 30}
            },
            {
                "Name": "",
                "Rolename": "text",
                "Description": "文本标签",
                "Coords": {"x": 150, "y": 300, "width": 80, "height": 20}
            },
            {
                "Name": "文件菜单",
                "Rolename": "menu",
                "Description": "文件操作菜单",
                "Coords": {"x": 400, "y": 150, "width": 60, "height": 25}
            }
        ]

        print("\n2. 测试不同类型控件的高亮显示...")

        for i, widget_info in enumerate(test_widgets):
            print(f"\n测试控件 {i+1}: {widget_info['Name'] or widget_info['Rolename']}")

            coords = widget_info["Coords"]
            success = renderer.highlight_widget(
                coords["x"],
                coords["y"],
                coords["width"],
                coords["height"],
                widget_info
            )

            if success:
                print(f"✅ 控件高亮显示成功")
                print(f"   位置: ({coords['x']}, {coords['y']})")
                print(f"   大小: {coords['width']}x{coords['height']}")
                print(f"   类型: {widget_info['Rolename']}")
                print("   请查看屏幕上的红色边框和文字标签")

                # 显示3秒
                time.sleep(3)

                # 清除高亮
                renderer.clear_highlight()
                print("   高亮已清除")

                # 间隔1秒
                time.sleep(1)
            else:
                print(f"❌ 控件高亮显示失败")

        print("\n3. 测试边界情况...")

        # 测试屏幕边缘位置
        edge_widget = {
            "Name": "边缘按钮",
            "Rolename": "push button",
            "Coords": {"x": 1800, "y": 50, "width": 100, "height": 30}  # 接近屏幕右边缘
        }

        print("测试屏幕边缘位置的标签显示...")
        coords = edge_widget["Coords"]
        success = renderer.highlight_widget(
            coords["x"],
            coords["y"],
            coords["width"],
            coords["height"],
            edge_widget
        )

        if success:
            print("✅ 边缘位置高亮显示成功")
            time.sleep(3)
            renderer.clear_highlight()

        print("\n4. 清理资源...")
        renderer.cleanup()
        print("✅ 资源清理完成")

        print("\n🎉 测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_label_text_generation():
    """测试标签文字生成功能"""
    print("\n🧪 测试标签文字生成功能")
    print("=" * 50)

    try:
        from widget_capture_module import HighlightRenderer

        renderer = HighlightRenderer(debug=True)

        test_cases = [
            {"Rolename": "push button", "Name": "确定"},
            {"Rolename": "entry", "Name": ""},
            {"Rolename": "unknown", "Name": "很长的控件名称超过十个字符"},
            {"Role": "text", "Name": "标签"},
            {},  # 空信息
        ]

        print("测试不同控件信息的标签文字生成:")
        for i, widget_info in enumerate(test_cases):
            label_text = renderer._generate_label_text(widget_info)
            ascii_text = renderer._to_ascii_text(label_text).decode('ascii')
            print(f"  案例 {i+1}: {widget_info}")
            print(f"    -> 中文: '{label_text}'")
            print(f"    -> ASCII: '{ascii_text}'")

        print("\n测试ASCII转换功能:")
        test_texts = ["按钮:确定", "输入框", "菜单:文件", "未知控件"]
        for text in test_texts:
            ascii_result = renderer._to_ascii_text(text).decode('ascii')
            print(f"  '{text}' -> '{ascii_result}'")

        print("✅ 标签文字生成测试完成")
        return True

    except Exception as e:
        print(f"❌ 标签文字生成测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试控件类别文字显示功能")

    # 测试标签文字生成
    test_label_text_generation()

    # 测试高亮显示
    test_highlight_with_label()

    print("\n✨ 所有测试完成")
