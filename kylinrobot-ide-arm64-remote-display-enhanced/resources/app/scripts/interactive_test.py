#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import UNI
import sys

def test_coordinate(x, y):
    """测试指定坐标的控件识别"""
    print(f"\n🔍 测试坐标: ({x}, {y})")
    print("-" * 50)
    
    try:
        a = UNI.UNI()
        
        # 捕获调试信息
        import sys
        from io import StringIO
        old_stderr = sys.stderr
        sys.stderr = StringIO()
        
        data, text = a.kdk_getElement_Uni(x, y)
        
        # 获取调试信息
        debug_info = sys.stderr.getvalue()
        sys.stderr = old_stderr
        
        # 显示结果
        print(f"名称: {data.get('Name', 'N/A')}")
        print(f"角色: {data.get('Rolename', 'N/A')}")
        print(f"位置: ({data.get('Coords', {}).get('x', 0)}, {data.get('Coords', {}).get('y', 0)})")
        print(f"大小: {data.get('Coords', {}).get('width', 0)}x{data.get('Coords', {}).get('height', 0)}")
        print(f"状态: {text}")
        
        # 分析结果
        area = data.get('Coords', {}).get('width', 0) * data.get('Coords', {}).get('height', 0)
        if area < 2000:
            print(f"✅ 小控件 (面积: {area})")
        elif area < 10000:
            print(f"⚠️ 中等控件 (面积: {area})")
        else:
            print(f"❌ 大控件 (面积: {area})")
        
        # 检查是否使用了智能搜索
        if "智能深度搜索" in debug_info:
            print("✅ 使用了智能深度搜索")
        else:
            print("❌ 未使用智能深度搜索")
            
        if 'error' in data:
            print(f"错误: {data['error']}")
            
        return data, text
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None, None

def interactive_test():
    """交互式测试"""
    print("🧪 UNI.py 交互式控件识别测试")
    print("=" * 60)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 测试坐标 (241, 188) - 账户信息标签")
        print("2. 测试自定义坐标")
        print("3. 测试多个坐标")
        print("4. 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == '1':
            test_coordinate(241, 188)
            
        elif choice == '2':
            try:
                x = int(input("请输入X坐标: "))
                y = int(input("请输入Y坐标: "))
                test_coordinate(x, y)
            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == '3':
            # 测试账户信息周围的坐标
            print("\n🔍 测试账户信息周围的坐标:")
            test_coords = [
                (241, 188),  # 原始坐标
                (240, 193),  # 标签中心
                (214, 182),  # 标签左上角
                (267, 204),  # 标签右下角
                (230, 190),  # 标签内部
            ]
            
            for x, y in test_coords:
                test_coordinate(x, y)
                
        elif choice == '4':
            print("👋 退出测试")
            break
            
        else:
            print("❌ 无效选项，请重新选择")

def quick_test():
    """快速测试当前状态"""
    print("🚀 快速测试当前控件识别状态")
    print("=" * 50)
    
    # 测试原始坐标
    data, text = test_coordinate(241, 188)
    
    if data and data.get('Name') == '账户信息' and data.get('Rolename') == 'label':
        print("\n🎉 成功！正确识别到'账户信息'小标签")
        return True
    else:
        print("\n⚠️ 未能识别到期望的'账户信息'标签")
        print("可能的原因:")
        print("- 界面已切换到其他页面")
        print("- 坐标位置发生变化")
        print("- 控件结构发生变化")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        # 快速测试模式
        success = quick_test()
        sys.exit(0 if success else 1)
    else:
        # 交互式测试模式
        interactive_test()
