#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import UNI
import sys
import time

def test_current_mouse_position():
    """测试当前鼠标位置的控件识别"""
    try:
        # 尝试获取鼠标位置
        try:
            import pynput.mouse
            mouse = pynput.mouse.Listener.canonical(pynput.mouse.Listener._current_listener())
            if hasattr(mouse, 'position'):
                x, y = mouse.position
            else:
                # 回退到固定坐标
                x, y = 241, 188
        except:
            # 如果无法获取鼠标位置，使用固定坐标
            x, y = 241, 188
        
        print(f"🔍 测试坐标: ({x}, {y})")
        
        # 创建UNI实例
        uni = UNI.UNI()
        
        # 模拟auto_recording_manager.py的调用
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"\n📊 识别结果:")
        print(f"名称: {result.get('Name', 'N/A')}")
        print(f"角色: {result.get('Rolename', 'N/A')}")
        print(f"位置: ({result.get('Coords', {}).get('x', 0)}, {result.get('Coords', {}).get('y', 0)})")
        print(f"大小: {result.get('Coords', {}).get('width', 0)}x{result.get('Coords', {}).get('height', 0)}")
        print(f"状态: {info_text}")
        
        return result, info_text
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None, None

def test_area_around_target():
    """测试目标坐标周围的区域"""
    print("🔍 测试账户信息标签周围的坐标区域")
    print("=" * 50)
    
    # 基于测试结果，账户信息标签的实际位置是(214,182)，大小53x22
    # 测试标签区域内的多个点
    test_points = [
        (214, 182),  # 左上角
        (240, 193),  # 中心点
        (267, 204),  # 右下角
        (241, 188),  # 原始测试坐标
        (230, 190),  # 标签内部
    ]
    
    uni = UNI.UNI()
    
    for i, (x, y) in enumerate(test_points):
        print(f"\n测试点 {i+1}: ({x}, {y})")
        print("-" * 30)
        
        try:
            result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
            
            name = result.get('Name', 'N/A')
            role = result.get('Rolename', 'N/A')
            area = result.get('Coords', {}).get('width', 0) * result.get('Coords', {}).get('height', 0)
            
            print(f"名称: {name}")
            print(f"角色: {role}")
            print(f"面积: {area}")
            
            if name == '账户信息' and role == 'label':
                print("✅ 成功识别到账户信息标签！")
            elif area < 5000:
                print("✅ 识别到小控件")
            else:
                print("⚠️ 识别到大控件")
                
        except Exception as e:
            print(f"❌ 识别失败: {e}")

def interactive_coordinate_test():
    """交互式坐标测试"""
    print("🧪 交互式坐标测试")
    print("输入坐标来测试控件识别，输入 'q' 退出")
    print("=" * 50)
    
    uni = UNI.UNI()
    
    while True:
        try:
            user_input = input("\n请输入坐标 (格式: x,y 或 'q' 退出): ").strip()
            
            if user_input.lower() == 'q':
                break
                
            if ',' in user_input:
                x_str, y_str = user_input.split(',')
                x, y = int(x_str.strip()), int(y_str.strip())
                
                print(f"\n🔍 测试坐标: ({x}, {y})")
                
                result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
                
                print(f"名称: {result.get('Name', 'N/A')}")
                print(f"角色: {result.get('Rolename', 'N/A')}")
                print(f"位置: ({result.get('Coords', {}).get('x', 0)}, {result.get('Coords', {}).get('y', 0)})")
                print(f"大小: {result.get('Coords', {}).get('width', 0)}x{result.get('Coords', {}).get('height', 0)}")
                print(f"状态: {info_text}")
                
            else:
                print("❌ 格式错误，请使用 'x,y' 格式")
                
        except ValueError:
            print("❌ 请输入有效的数字坐标")
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("🧪 UNI.py 实时控件识别测试工具")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "area":
            test_area_around_target()
        elif sys.argv[1] == "interactive":
            interactive_coordinate_test()
        else:
            test_current_mouse_position()
    else:
        print("请选择测试模式:")
        print("1. python3 real_time_test.py - 测试固定坐标")
        print("2. python3 real_time_test.py area - 测试账户信息周围区域")
        print("3. python3 real_time_test.py interactive - 交互式测试")
        
        test_current_mouse_position()
