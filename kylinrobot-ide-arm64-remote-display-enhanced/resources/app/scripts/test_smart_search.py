#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import UNI
import sys

def test_coordinate_recognition():
    """测试坐标(241, 188)的智能识别"""
    print("测试坐标 (241, 188) 的智能控件识别")
    print("=" * 50)
    
    try:
        a = UNI.UNI()
        data, text = a.kdk_getElement_Uni(241, 188)
        
        print("识别结果:")
        print(f"  名称: {data.get('Name', 'N/A')}")
        print(f"  角色: {data.get('Rolename', 'N/A')}")
        print(f"  位置: ({data.get('Coords', {}).get('x', 0)}, {data.get('Coords', {}).get('y', 0)})")
        print(f"  大小: {data.get('Coords', {}).get('width', 0)}x{data.get('Coords', {}).get('height', 0)}")
        print(f"  状态: {text}")
        
        if 'error' in data:
            print(f"  错误: {data['error']}")
            return False
        
        # 检查是否识别到了小标签而不是大容器
        width = data.get('Coords', {}).get('width', 0)
        height = data.get('Coords', {}).get('height', 0)
        area = width * height
        
        print(f"\n分析:")
        print(f"  控件面积: {area} 像素")
        
        if area < 5000:  # 小控件
            print("  ✅ 成功识别到小控件（可能是标签）")
            return True
        elif area > 20000:  # 大控件
            print("  ⚠️ 识别到大控件（可能是容器）")
            return False
        else:
            print("  ℹ️ 识别到中等大小控件")
            return True
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_coordinate_recognition()
    sys.exit(0 if success else 1)
