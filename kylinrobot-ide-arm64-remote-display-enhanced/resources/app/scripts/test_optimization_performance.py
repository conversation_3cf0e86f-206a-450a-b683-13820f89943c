#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试auto_recording_manager优化效果
验证窗口关闭后控件识别性能是否改善

优化内容：
1. 窗口关闭检测频率：0.5秒 -> 1.5秒
2. AT-SPI恢复策略：深度恢复 -> 轻量化恢复
3. 缓存超时时间：50ms -> 300ms (桌面缓存), 5秒 -> 8秒 (控件缓存)
4. 性能监控：优化日志输出，减少噪音
"""

import time
import sys
import os
import subprocess
import threading
from pathlib import Path

# 添加scripts目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_widget_recognition_performance():
    """测试控件识别性能"""
    print("=" * 60)
    print("🧪 控件识别性能测试")
    print("=" * 60)
    
    try:
        # 导入优化后的模块
        from auto_recording_manager import WidgetAnalyzer, _log_widget_recognition_timing
        
        # 创建分析器
        analyzer = WidgetAnalyzer(debug=True)
        if not analyzer.uni:
            print("❌ UNI模块不可用，跳过测试")
            return
            
        print("✅ 模块导入成功")
        
        # 测试点：屏幕中央
        test_x, test_y = 500, 400
        
        # 执行多次识别测试
        print(f"\n📍 测试坐标: ({test_x}, {test_y})")
        print("🔄 执行5次控件识别测试...")
        
        times = []
        for i in range(5):
            print(f"\n--- 第 {i+1} 次测试 ---")
            start_time = time.time()
            
            widget_info, info_text = analyzer.analyze_widget_at(test_x, test_y)
            
            end_time = time.time()
            duration = end_time - start_time
            times.append(duration)
            
            # 记录详细耗时
            widget_name = widget_info.get('Name', 'Unknown') if widget_info and not widget_info.get('error') else 'Unknown'
            success = widget_info and not widget_info.get('error')
            
            _log_widget_recognition_timing(
                "analyze_widget_at",
                start_time, end_time, test_x, test_y,
                success, widget_name, info_text if not success else ""
            )
            
            # 等待一段时间再进行下次测试
            time.sleep(0.5)
        
        # 统计结果
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        print("\n" + "=" * 60)
        print("📊 性能测试结果")
        print("=" * 60)
        print(f"平均耗时: {avg_time:.3f}秒")
        print(f"最大耗时: {max_time:.3f}秒")
        print(f"最小耗时: {min_time:.3f}秒")
        print(f"耗时列表: {[f'{t:.3f}s' for t in times]}")
        
        # 性能评估
        if avg_time < 0.3:
            print("🎉 性能评估: 优秀 (平均耗时 < 300ms)")
        elif avg_time < 0.5:
            print("✅ 性能评估: 良好 (平均耗时 < 500ms)")
        elif avg_time < 1.0:
            print("⚠️  性能评估: 一般 (平均耗时 < 1秒)")
        else:
            print("🚨 性能评估: 需要优化 (平均耗时 >= 1秒)")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def simulate_window_close_scenario():
    """模拟窗口关闭场景"""
    print("\n" + "=" * 60)
    print("🪟 窗口关闭场景模拟测试")
    print("=" * 60)
    
    try:
        # 1. 打开一个测试应用
        print("1. 打开测试应用 (pluma)...")
        process = subprocess.Popen(['pluma'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(2)  # 等待应用启动
        
        # 2. 导入并创建hover detector
        from auto_recording_manager import HoverDetector, WidgetAnalyzer
        
        widget_analyzer = WidgetAnalyzer(debug=True)
        hover_detector = HoverDetector(widget_analyzer=widget_analyzer, debug=True)
        hover_detector.start()
        
        print("✅ 悬停检测器已启动")
        
        # 3. 测试窗口关闭前的性能
        print("\n📊 窗口关闭前性能测试...")
        test_x, test_y = 400, 300
        
        before_times = []
        for i in range(3):
            start_time = time.time()
            widget_info, _ = widget_analyzer.analyze_widget_at(test_x, test_y)
            end_time = time.time()
            before_times.append(end_time - start_time)
            time.sleep(0.5)
        
        avg_before = sum(before_times) / len(before_times)
        print(f"关闭前平均耗时: {avg_before:.3f}秒")
        
        # 4. 关闭测试应用
        print("\n🔄 关闭测试应用...")
        process.terminate()
        process.wait()
        time.sleep(1)  # 等待窗口关闭检测触发
        
        # 5. 测试窗口关闭后的性能
        print("📊 窗口关闭后性能测试...")
        
        after_times = []
        for i in range(3):
            start_time = time.time()
            widget_info, _ = widget_analyzer.analyze_widget_at(test_x, test_y)
            end_time = time.time()
            after_times.append(end_time - start_time)
            time.sleep(0.5)
        
        avg_after = sum(after_times) / len(after_times)
        print(f"关闭后平均耗时: {avg_after:.3f}秒")
        
        # 6. 对比分析
        print("\n" + "=" * 60)
        print("📈 性能对比分析")
        print("=" * 60)
        print(f"关闭前平均耗时: {avg_before:.3f}秒")
        print(f"关闭后平均耗时: {avg_after:.3f}秒")
        
        if avg_after <= avg_before * 1.2:  # 允许20%的波动
            print("🎉 优化成功! 窗口关闭后性能没有明显劣化")
        elif avg_after <= avg_before * 1.5:
            print("✅ 优化有效! 性能劣化在可接受范围内")
        else:
            print("⚠️  仍需优化，关闭后性能劣化较明显")
            
        # 清理
        hover_detector.stop()
        
    except Exception as e:
        print(f"❌ 窗口关闭场景测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_cache_optimization():
    """测试缓存优化效果"""
    print("\n" + "=" * 60)
    print("💾 缓存优化效果测试")
    print("=" * 60)
    
    try:
        from auto_recording_manager import HoverDetector, WidgetAnalyzer
        
        widget_analyzer = WidgetAnalyzer(debug=True)
        hover_detector = HoverDetector(widget_analyzer=widget_analyzer, debug=True)
        
        print(f"✅ 控件缓存超时时间: {hover_detector.cache_timeout}秒")
        
        # 测试缓存效果
        test_x, test_y = 500, 400
        
        print("\n🔄 第一次识别 (无缓存)...")
        start_time = time.time()
        hover_detector.widget_analyzer.analyze_widget_at(test_x, test_y)
        first_time = time.time() - start_time
        print(f"首次识别耗时: {first_time:.3f}秒")
        
        # 短时间后再次识别，应该有缓存效果
        print("🔄 第二次识别 (可能有缓存)...")
        time.sleep(0.1)  # 短暂等待
        start_time = time.time()
        hover_detector.widget_analyzer.analyze_widget_at(test_x, test_y)
        second_time = time.time() - start_time
        print(f"第二次识别耗时: {second_time:.3f}秒")
        
        if second_time < first_time * 0.8:
            print("🎉 缓存优化有效! 第二次识别更快")
        else:
            print("📊 缓存效果不明显，可能需要进一步优化")
            
    except Exception as e:
        print(f"❌ 缓存测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 auto_recording_manager 优化效果测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 1. 基础性能测试
    test_widget_recognition_performance()
    
    # 2. 缓存优化测试
    test_cache_optimization()
    
    # 3. 窗口关闭场景测试
    simulate_window_close_scenario()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()