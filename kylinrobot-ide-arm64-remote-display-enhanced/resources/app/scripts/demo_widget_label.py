#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
控件类别文字显示功能演示脚本
在有图形界面的环境中运行此脚本来查看效果
"""

import sys
import time
import os

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def check_display_environment():
    """检查是否有图形显示环境"""
    display = os.environ.get('DISPLAY')
    if not display:
        print("❌ 未检测到图形显示环境 (DISPLAY环境变量未设置)")
        print("   请在有图形界面的环境中运行此演示")
        return False
    
    print(f"✅ 检测到图形显示环境: {display}")
    return True

def demo_widget_label_feature():
    """演示控件类别文字显示功能"""
    print("🎬 控件类别文字显示功能演示")
    print("=" * 50)
    
    if not check_display_environment():
        return False
    
    try:
        from widget_capture_module import HighlightRenderer
        
        print("\n1. 初始化高亮渲染器...")
        renderer = HighlightRenderer(debug=True)
        
        if not renderer.display:
            print("❌ 高亮渲染器初始化失败")
            return False
        
        print("✅ 高亮渲染器初始化成功")
        
        # 演示不同类型的控件
        demo_widgets = [
            {
                "Name": "确定",
                "Rolename": "push button",
                "Description": "确认操作按钮",
                "Coords": {"x": 300, "y": 200, "width": 80, "height": 35},
                "demo_desc": "按钮控件 - 显示为 '按钮:确定'"
            },
            {
                "Name": "用户名输入框",
                "Rolename": "entry", 
                "Description": "输入用户名",
                "Coords": {"x": 400, "y": 250, "width": 150, "height": 30},
                "demo_desc": "输入框控件 - 名称过长，只显示 '输入框'"
            },
            {
                "Name": "",
                "Rolename": "text",
                "Description": "文本标签",
                "Coords": {"x": 200, "y": 300, "width": 100, "height": 25},
                "demo_desc": "文本控件 - 无名称，显示为 '文本'"
            },
            {
                "Name": "文件",
                "Rolename": "menu",
                "Description": "文件菜单",
                "Coords": {"x": 150, "y": 150, "width": 50, "height": 25},
                "demo_desc": "菜单控件 - 显示为 '菜单:文件'"
            },
            {
                "Name": "保存",
                "Rolename": "menu item",
                "Description": "保存文件",
                "Coords": {"x": 500, "y": 180, "width": 60, "height": 20},
                "demo_desc": "菜单项控件 - 显示为 '菜单项:保存'"
            },
            {
                "Name": "启用选项",
                "Rolename": "check box",
                "Description": "复选框",
                "Coords": {"x": 350, "y": 350, "width": 20, "height": 20},
                "demo_desc": "复选框控件 - 显示为 '复选框:启用选项'"
            }
        ]
        
        print(f"\n2. 开始演示 {len(demo_widgets)} 种不同类型的控件...")
        print("   请观察屏幕上的红色边框和文字标签")
        
        for i, widget in enumerate(demo_widgets):
            print(f"\n--- 演示 {i+1}/{len(demo_widgets)}: {widget['demo_desc']} ---")
            
            coords = widget["Coords"]
            print(f"位置: ({coords['x']}, {coords['y']}) 大小: {coords['width']}x{coords['height']}")
            
            # 显示高亮和文字标签
            success = renderer.highlight_widget(
                coords["x"],
                coords["y"],
                coords["width"], 
                coords["height"],
                widget
            )
            
            if success:
                print("✅ 高亮显示成功")
                print("   请查看屏幕上的红色边框和控件类别文字标签")
                
                # 显示5秒让用户观察
                for countdown in range(5, 0, -1):
                    print(f"   {countdown}秒后切换到下一个控件...", end='\r')
                    time.sleep(1)
                print("   " + " " * 30)  # 清除倒计时
                
                # 清除当前高亮
                renderer.clear_highlight()
                print("   高亮已清除")
                
                # 间隔1秒
                time.sleep(1)
            else:
                print("❌ 高亮显示失败")
        
        print("\n3. 演示边界位置处理...")
        
        # 获取屏幕尺寸进行边界测试
        screen_width = renderer.screen.width_in_pixels
        screen_height = renderer.screen.height_in_pixels
        print(f"屏幕尺寸: {screen_width}x{screen_height}")
        
        # 右边缘控件
        edge_widget = {
            "Name": "边缘按钮",
            "Rolename": "push button",
            "Coords": {
                "x": screen_width - 120,  # 接近右边缘
                "y": 100,
                "width": 100,
                "height": 30
            }
        }
        
        print("演示右边缘位置的文字标签自动调整...")
        coords = edge_widget["Coords"]
        success = renderer.highlight_widget(
            coords["x"], coords["y"], coords["width"], coords["height"], edge_widget
        )
        
        if success:
            print("✅ 边缘位置处理成功")
            print("   文字标签应该自动调整到控件左边显示")
            time.sleep(3)
            renderer.clear_highlight()
        
        print("\n4. 清理资源...")
        renderer.cleanup()
        print("✅ 演示完成！")
        
        print("\n🎉 控件类别文字显示功能演示结束")
        print("   该功能已集成到GAT全流程录制系统中")
        print("   启动录制后，鼠标悬停在控件上即可看到类似效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 启动控件类别文字显示功能演示")
    print("   注意：此演示需要在有图形界面的环境中运行")
    print("   如果您在SSH或无图形环境中，请切换到桌面环境")
    
    demo_widget_label_feature()
