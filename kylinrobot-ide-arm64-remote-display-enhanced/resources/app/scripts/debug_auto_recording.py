#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import sys
import time
import threading
from typing import Dict, Any, Optional, Tuple

# 导入auto_recording_manager模块
try:
    from auto_recording_manager import WidgetAnalyzer
    print("✅ 成功导入auto_recording_manager.WidgetAnalyzer")
except ImportError as e:
    print(f"❌ 无法导入auto_recording_manager: {e}")
    sys.exit(1)

def test_widget_analyzer_directly():
    """直接测试WidgetAnalyzer类"""
    print("🧪 直接测试auto_recording_manager.WidgetAnalyzer")
    print("=" * 60)
    
    # 创建WidgetAnalyzer实例（启用调试）
    analyzer = WidgetAnalyzer(debug=True)
    
    if not analyzer.uni:
        print("❌ WidgetAnalyzer的UNI模块初始化失败")
        return False
    
    # 测试坐标
    test_coords = [
        (241, 188),  # 原始测试坐标
        (214, 182),  # 标签左上角
        (240, 193),  # 标签中心
        (267, 204),  # 标签右下角
        (230, 190),  # 标签内部
    ]
    
    success_count = 0
    
    for i, (x, y) in enumerate(test_coords):
        print(f"\n🎯 测试 {i+1}: 坐标 ({x}, {y})")
        print("-" * 40)
        
        try:
            # 调用analyze_widget_at方法
            result, status = analyzer.analyze_widget_at(x, y)
            
            if result:
                name = result.get('Name', 'N/A')
                role = result.get('Rolename', 'N/A')
                coords = result.get('Coords', {})
                width = coords.get('width', 0)
                height = coords.get('height', 0)
                area = width * height
                
                print(f"✅ 识别成功:")
                print(f"   名称: {name}")
                print(f"   角色: {role}")
                print(f"   大小: {width}x{height}")
                print(f"   面积: {area}")
                print(f"   状态: {status}")
                
                if name == '账户信息' and role == 'label':
                    print(f"   🎉 成功识别到'账户信息'小标签！")
                    success_count += 1
                elif area < 5000:
                    print(f"   ✅ 识别到小控件")
                    success_count += 1
                else:
                    print(f"   ⚠️ 识别到大控件")
            else:
                print(f"❌ 识别失败: {status}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试总结: {success_count}/{len(test_coords)} 个坐标成功识别到期望结果")
    return success_count > 0

def test_with_new_app_detection():
    """测试带新应用检测的控件识别"""
    print("\n🧪 测试auto_recording_manager的新应用检测功能")
    print("=" * 60)
    
    analyzer = WidgetAnalyzer(debug=True)
    
    if not analyzer.uni:
        print("❌ WidgetAnalyzer的UNI模块初始化失败")
        return False
    
    x, y = 241, 188
    print(f"🎯 测试坐标: ({x}, {y})")
    
    try:
        # 调用analyze_widget_at_with_new_app_detection方法
        result, status = analyzer.analyze_widget_at_with_new_app_detection(x, y)
        
        if result:
            name = result.get('Name', 'N/A')
            role = result.get('Rolename', 'N/A')
            coords = result.get('Coords', {})
            area = coords.get('width', 0) * coords.get('height', 0)
            
            print(f"✅ 新应用检测识别成功:")
            print(f"   名称: {name}")
            print(f"   角色: {role}")
            print(f"   面积: {area}")
            print(f"   状态: {status}")
            
            if name == '账户信息' and role == 'label':
                print(f"   🎉 新应用检测成功识别到'账户信息'小标签！")
                return True
            else:
                print(f"   ⚠️ 识别到其他控件")
                return True
        else:
            print(f"❌ 新应用检测识别失败: {status}")
            return False
            
    except Exception as e:
        print(f"❌ 新应用检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def interactive_debug():
    """交互式调试模式"""
    print("\n🔧 交互式调试模式")
    print("=" * 60)
    print("输入坐标来实时测试auto_recording_manager的控件识别")
    print("格式: x,y  例如: 241,188")
    print("输入 'q' 退出")
    
    analyzer = WidgetAnalyzer(debug=True)
    
    if not analyzer.uni:
        print("❌ WidgetAnalyzer的UNI模块初始化失败")
        return
    
    while True:
        try:
            user_input = input("\n请输入坐标: ").strip()
            
            if user_input.lower() == 'q':
                break
                
            if ',' in user_input:
                x_str, y_str = user_input.split(',')
                x, y = int(x_str.strip()), int(y_str.strip())
                
                print(f"\n🔍 测试坐标: ({x}, {y})")
                print("-" * 30)
                
                # 测试标准方法
                result1, status1 = analyzer.analyze_widget_at(x, y)
                
                print(f"标准识别:")
                if result1:
                    name = result1.get('Name', 'N/A')
                    role = result1.get('Rolename', 'N/A')
                    area = result1.get('Coords', {}).get('width', 0) * result1.get('Coords', {}).get('height', 0)
                    print(f"  ✅ {name} ({role}) 面积:{area}")
                else:
                    print(f"  ❌ {status1}")
                
                # 测试新应用检测方法
                result2, status2 = analyzer.analyze_widget_at_with_new_app_detection(x, y)
                
                print(f"新应用检测:")
                if result2:
                    name = result2.get('Name', 'N/A')
                    role = result2.get('Rolename', 'N/A')
                    area = result2.get('Coords', {}).get('width', 0) * result2.get('Coords', {}).get('height', 0)
                    print(f"  ✅ {name} ({role}) 面积:{area}")
                else:
                    print(f"  ❌ {status2}")
                    
            else:
                print("❌ 格式错误，请使用 'x,y' 格式")
                
        except ValueError:
            print("❌ 请输入有效的数字坐标")
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🔧 auto_recording_manager.py 调试工具")
    print("=" * 70)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "interactive":
            interactive_debug()
            return
    
    # 运行所有测试
    print("开始全面测试auto_recording_manager的控件识别功能...")
    
    # 测试1: 直接测试WidgetAnalyzer
    success1 = test_widget_analyzer_directly()
    
    # 测试2: 测试新应用检测
    success2 = test_with_new_app_detection()
    
    print(f"\n{'='*70}")
    print(f"📋 最终测试结果:")
    print(f"{'='*70}")
    print(f"直接测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"新应用检测: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print(f"\n🎉 auto_recording_manager.py的控件识别功能完全正常！")
        print(f"   如果您在实际使用中仍然无法感知到小标签，请检查:")
        print(f"   1. 界面是否显示'账户信息'标签")
        print(f"   2. 点击位置是否在标签范围内(214-267, 182-204)")
        print(f"   3. 录制界面是否正确显示识别结果")
    else:
        print(f"\n⚠️ auto_recording_manager.py的控件识别功能存在问题")
        print(f"   建议运行交互式调试: python3 debug_auto_recording.py interactive")
    
    print(f"\n💡 提示: 运行 'python3 debug_auto_recording.py interactive' 进入交互式调试模式")

if __name__ == "__main__":
    main()
