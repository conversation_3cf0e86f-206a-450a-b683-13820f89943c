#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import UNI
import sys

def test_fixed_search():
    """测试修复后的控件搜索功能"""
    print("🧪 测试修复后的控件搜索功能")
    print("=" * 60)
    
    try:
        uni = UNI.UNI()
        
        # 测试坐标 (347, 176) - 这是日志中的坐标
        test_x, test_y = 347, 176
        
        print(f"🎯 测试坐标: ({test_x}, {test_y})")
        print("-" * 50)
        
        # 调用控件识别
        result, status = uni.kdk_getElement_Uni(test_x, test_y, False, True)
        
        if result:
            name = result.get('Name', 'N/A')
            role = result.get('Rolename', 'N/A')
            coords = result.get('Coords', {})
            width = coords.get('width', 0)
            height = coords.get('height', 0)
            area = width * height
            
            print(f"📊 识别结果:")
            print(f"  名称: {name}")
            print(f"  角色: {role}")
            print(f"  位置: ({coords.get('x', 0)}, {coords.get('y', 0)})")
            print(f"  大小: {width}x{height}")
            print(f"  面积: {area}")
            print(f"  状态: {status}")
            
            # 分析结果
            if '账户信息' in name and role == 'label':
                print(f"\n🎉 成功！识别到'账户信息'小标签")
                print(f"   这正是我们期望的结果！")
                return True
            elif role == 'label' and area < 5000:
                print(f"\n✅ 识别到小标签控件")
                print(f"   面积: {area} 像素（小控件）")
                return True
            elif role == 'push button' and area > 20000:
                print(f"\n⚠️ 仍然识别到大按钮控件")
                print(f"   面积: {area} 像素（大控件）")
                print(f"   修复可能还需要进一步调整")
                return False
            else:
                print(f"\n ℹ️ 识别到其他控件: {name} ({role})")
                print(f"   面积: {area} 像素")
                return True
        else:
            print(f"❌ 识别失败: {status}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_coordinates():
    """测试多个坐标的识别效果"""
    print("\n🧪 测试多个坐标的识别效果")
    print("=" * 60)
    
    # 测试多个坐标
    test_coords = [
        (347, 176),  # 日志中的坐标
        (241, 188),  # 原始测试坐标
        (300, 200),  # 按钮中心区域
        (280, 180),  # 按钮左侧
        (320, 190),  # 按钮右侧
    ]
    
    uni = UNI.UNI()
    success_count = 0
    
    for i, (x, y) in enumerate(test_coords):
        print(f"\n测试 {i+1}: 坐标 ({x}, {y})")
        print("-" * 30)
        
        try:
            # 使用简化调用，减少调试输出
            import sys
            from io import StringIO
            old_stderr = sys.stderr
            sys.stderr = StringIO()
            
            result, status = uni.kdk_getElement_Uni(x, y, False, True)
            
            captured_stderr = sys.stderr.getvalue()
            sys.stderr = old_stderr
            
            if result:
                name = result.get('Name', 'N/A')
                role = result.get('Rolename', 'N/A')
                area = result.get('Coords', {}).get('width', 0) * result.get('Coords', {}).get('height', 0)
                
                print(f"名称: {name}")
                print(f"角色: {role}")
                print(f"面积: {area}")
                
                # 检查是否有改进
                if 'label' in role:
                    print("✅ 识别到标签控件")
                    success_count += 1
                elif area < 10000:
                    print("✅ 识别到小控件")
                    success_count += 1
                else:
                    print("⚠️ 仍然是大控件")
                    
                # 检查调试信息中是否有标签相关的内容
                if '账户信息' in captured_stderr:
                    print("🎯 调试信息中发现'账户信息'标签")
                elif 'label' in captured_stderr:
                    print("🔍 调试信息中发现标签控件")
                    
            else:
                print(f"❌ 识别失败: {status}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print(f"\n📊 测试总结: {success_count}/{len(test_coords)} 个坐标识别到期望结果")
    return success_count > 0

def main():
    """主函数"""
    print("🔧 测试修复后的控件搜索功能")
    print("=" * 70)
    
    # 测试1: 单个坐标测试
    success1 = test_fixed_search()
    
    # 测试2: 多个坐标测试
    success2 = test_multiple_coordinates()
    
    print(f"\n{'='*70}")
    print("📋 最终测试结果:")
    print(f"{'='*70}")
    print(f"单坐标测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"多坐标测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print(f"\n🎉 修复成功！现在应该能识别到小标签控件了")
        print(f"   auto_recording_manager.py应该能正常工作")
    else:
        print(f"\n⚠️ 修复可能还需要进一步调整")
        print(f"   建议检查调试日志以了解详细情况")

if __name__ == "__main__":
    main()
