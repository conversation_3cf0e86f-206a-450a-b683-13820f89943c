#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import UNI
import sys

def check_interface_state():
    """检查当前界面状态，确认是否显示账户信息页面"""
    print("🔍 检查当前设置界面状态")
    print("=" * 60)
    
    try:
        uni = UNI.UNI()
        
        # 检查预期的账户信息标签位置
        expected_coords = [
            (214, 182),  # 标签左上角
            (240, 193),  # 标签中心
            (267, 204),  # 标签右下角
        ]
        
        print("🎯 检查预期的'账户信息'标签位置...")
        
        found_account_info = False
        
        for i, (x, y) in enumerate(expected_coords):
            print(f"\n检查点 {i+1}: ({x}, {y})")
            print("-" * 30)
            
            try:
                result, status = uni.kdk_getElement_Uni(x, y, False, True)
                
                if result:
                    name = result.get('Name', 'N/A')
                    role = result.get('Rolename', 'N/A')
                    coords = result.get('Coords', {})
                    width = coords.get('width', 0)
                    height = coords.get('height', 0)
                    area = width * height
                    
                    print(f"名称: {name}")
                    print(f"角色: {role}")
                    print(f"大小: {width}x{height}")
                    print(f"面积: {area}")
                    
                    if name == '账户信息' and role == 'label':
                        print("✅ 找到'账户信息'标签！")
                        found_account_info = True
                    elif area > 100000:
                        print("⚠️ 识别到大容器，可能界面状态不正确")
                    else:
                        print("ℹ️ 识别到其他控件")
                else:
                    print(f"❌ 识别失败: {status}")
                    
            except Exception as e:
                print(f"❌ 检查失败: {e}")
        
        print(f"\n{'='*60}")
        print("📊 界面状态检查结果:")
        print(f"{'='*60}")
        
        if found_account_info:
            print("✅ 当前界面正确显示'账户信息'标签")
            print("   auto_recording_manager.py应该能正常识别")
            return True
        else:
            print("❌ 当前界面未显示'账户信息'标签")
            print("   请切换到包含'账户信息'的设置页面")
            
            # 提供界面导航建议
            print(f"\n💡 界面导航建议:")
            print("   1. 确保设置窗口是活动窗口")
            print("   2. 查找并点击包含'账户信息'的菜单项或标签页")
            print("   3. 确认'账户信息'标签在界面中可见")
            
            return False
            
    except Exception as e:
        print(f"❌ 界面状态检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def scan_current_interface():
    """扫描当前界面的控件结构"""
    print("\n🔍 扫描当前界面的控件结构")
    print("=" * 60)
    
    try:
        uni = UNI.UNI()
        
        # 扫描界面中心区域的控件
        scan_coords = [
            (200, 150), (300, 150), (400, 150),
            (200, 200), (300, 200), (400, 200),
            (200, 250), (300, 250), (400, 250),
            (200, 300), (300, 300), (400, 300),
        ]
        
        print("扫描界面中心区域的控件...")
        
        controls_found = []
        
        for x, y in scan_coords:
            try:
                # 使用简化的调用，避免过多调试信息
                import sys
                from io import StringIO
                old_stderr = sys.stderr
                sys.stderr = StringIO()
                
                result, status = uni.kdk_getElement_Uni(x, y, False, True)
                
                sys.stderr = old_stderr
                
                if result:
                    name = result.get('Name', 'N/A')
                    role = result.get('Rolename', 'N/A')
                    coords = result.get('Coords', {})
                    
                    # 避免重复记录相同的控件
                    control_key = f"{name}_{role}_{coords.get('x', 0)}_{coords.get('y', 0)}"
                    if control_key not in [c['key'] for c in controls_found]:
                        controls_found.append({
                            'name': name,
                            'role': role,
                            'coords': coords,
                            'key': control_key,
                            'scan_pos': (x, y)
                        })
                        
            except:
                continue
        
        print(f"\n发现 {len(controls_found)} 个不同的控件:")
        print("-" * 40)
        
        for i, control in enumerate(controls_found[:10]):  # 只显示前10个
            name = control['name']
            role = control['role']
            coords = control['coords']
            width = coords.get('width', 0)
            height = coords.get('height', 0)
            
            print(f"{i+1:2d}. {name} ({role})")
            print(f"    位置: ({coords.get('x', 0)}, {coords.get('y', 0)})")
            print(f"    大小: {width}x{height}")
            
            # 检查是否包含"账户"相关的文字
            if '账户' in name or 'account' in name.lower():
                print(f"    🎯 可能相关的控件！")
        
        if len(controls_found) > 10:
            print(f"... 还有 {len(controls_found) - 10} 个控件未显示")
            
        return len(controls_found) > 0
        
    except Exception as e:
        print(f"❌ 界面扫描失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 设置界面状态检查工具")
    print("=" * 70)
    
    # 检查界面状态
    state_ok = check_interface_state()
    
    # 扫描当前界面
    scan_ok = scan_current_interface()
    
    print(f"\n{'='*70}")
    print("📋 总结:")
    print(f"{'='*70}")
    
    if state_ok:
        print("🎉 界面状态正确，auto_recording_manager.py应该能识别到'账户信息'标签")
    else:
        print("⚠️ 界面状态不正确，需要切换到正确的设置页面")
        
        if scan_ok:
            print("💡 建议:")
            print("   1. 查看上面扫描到的控件列表")
            print("   2. 寻找包含'账户'相关的控件")
            print("   3. 点击相应的菜单项或标签页")
            print("   4. 重新运行此工具确认界面状态")
        else:
            print("❌ 界面扫描也失败，可能存在其他问题")

if __name__ == "__main__":
    main()
