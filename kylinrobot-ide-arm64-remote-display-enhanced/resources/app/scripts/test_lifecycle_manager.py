#!/usr/bin/env python3
"""
测试AutoRecordingLifecycleManager的功能
"""

import sys
import time
import threading
import os
from unittest.mock import Mock, patch, MagicMock

# 添加scripts目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 模拟一个会产生慢识别的AutoRecordingManager
class MockSlowAutoRecordingManager:
    def __init__(self, **kwargs):
        self.running = False
        self.debug = kwargs.get('debug', False)
        self.slow_recognition_count = 0
        
    def start_recording(self):
        """启动录制并模拟慢识别"""
        self.running = True
        print(f"[MOCK] AutoRecordingManager启动，debug={self.debug}")
        
        # 启动一个线程模拟慢识别日志输出
        def simulate_slow_recognition():
            while self.running:
                time.sleep(2)  # 每2秒模拟一次慢识别
                if self.running:
                    self.slow_recognition_count += 1
                    self._output_slow_recognition_log()
                
        self.simulation_thread = threading.Thread(target=simulate_slow_recognition, daemon=True)
        self.simulation_thread.start()
        
    def stop_recording(self):
        """停止录制"""
        print(f"[MOCK] AutoRecordingManager停止")
        self.running = False
        
    def cleanup(self):
        """清理资源"""
        print(f"[MOCK] AutoRecordingManager清理资源")
        
    def _output_slow_recognition_log(self):
        """输出慢识别日志"""
        duration = 4.5 + (self.slow_recognition_count % 3) * 0.5  # 模拟4.5-6秒的耗时
        
        # 模拟性能警告日志格式
        log_output = f"""================================================================================
🚨 严重性能警告 🚨
================================================================================
⏰ 耗时: {duration:.3f}秒 (严重超时!)
📍 坐标: (100, 200)
🔧 方法: analyze_widget_at
🎯 控件: MockWidget_{self.slow_recognition_count}
✅ 状态: 识别成功
💡 建议: 检查AT-SPI状态或考虑重启应用
================================================================================"""
        
        print(log_output, file=sys.stderr)

def test_lifecycle_manager_basic():
    """测试生命周期管理器基本功能"""
    print("\n=== 测试1: 生命周期管理器基本功能 ===")
    
    try:
        # 打补丁，用我们的模拟管理器替换真实的
        with patch('auto_recording_lifecycle_manager.AutoRecordingManager', MockSlowAutoRecordingManager):
            from auto_recording_lifecycle_manager import AutoRecordingLifecycleManager
            
            # 创建生命周期管理器
            lifecycle_manager = AutoRecordingLifecycleManager(
                slow_threshold=3.0,
                max_slow_count=3,
                restart_delay=1.0,
                max_restarts=2,
                restart_cooldown=5.0
            )
            
            # 配置并启动
            lifecycle_manager.configure_manager(debug=True)
            lifecycle_manager.start()
            
            # 运行一段时间观察行为
            print("[TEST] 运行15秒观察重启行为...")
            start_time = time.time()
            
            while time.time() - start_time < 15:
                status = lifecycle_manager.get_status()
                print(f"[TEST] 状态: 运行={status['running']}, 重启次数={status['restart_count']}, "
                      f"慢识别={status['performance_stats']['slow_recognitions']}")
                time.sleep(3)
                
            # 停止管理器
            lifecycle_manager.stop()
            
            # 检查结果
            final_status = lifecycle_manager.get_status()
            if final_status['restart_count'] > 0:
                print(f"[TEST] ✅ 基本功能测试通过，发生了{final_status['restart_count']}次重启")
            else:
                print(f"[TEST] ❌ 基本功能测试失败，没有发生重启")
                
    except Exception as e:
        print(f"[TEST] ❌ 基本功能测试出现异常: {e}")
        import traceback
        traceback.print_exc()

def test_performance_monitoring():
    """测试性能监控功能"""
    print("\n=== 测试2: 性能监控功能 ===")
    
    try:
        from auto_recording_lifecycle_manager import PerformanceMonitor
        
        # 创建性能监控器
        monitor = PerformanceMonitor(slow_threshold=3.0, max_slow_count=2)
        
        # 添加回调来验证事件
        event_received = []
        def test_callback(event_type, event_data):
            event_received.append((event_type, event_data))
            print(f"[TEST] 收到性能事件: {event_type}, 数据: {event_data}")
            
        monitor.add_performance_callback(test_callback)
        
        # 模拟性能日志
        slow_log = """================================================================================
🚨 严重性能警告 🚨
================================================================================
⏰ 耗时: 4.125秒 (严重超时!)
📍 坐标: (150, 250)
🔧 方法: analyze_widget_at
🎯 控件: TestWidget
✅ 状态: 识别成功
💡 建议: 检查AT-SPI状态或考虑重启应用
================================================================================"""
        
        # 解析多次慢识别日志
        for i in range(3):
            print(f"[TEST] 模拟第{i+1}次慢识别")
            monitor.parse_performance_log(slow_log)
            time.sleep(0.1)
            
        # 检查结果
        stats = monitor.get_statistics()
        print(f"[TEST] 性能统计: {stats}")
        
        if len(event_received) > 0 and stats['slow_recognitions'] >= 3:
            print(f"[TEST] ✅ 性能监控测试通过，检测到{stats['slow_recognitions']}次慢识别")
        else:
            print(f"[TEST] ❌ 性能监控测试失败")
            
    except Exception as e:
        print(f"[TEST] ❌ 性能监控测试出现异常: {e}")
        import traceback
        traceback.print_exc()

def test_restart_limits():
    """测试重启限制功能"""
    print("\n=== 测试3: 重启限制功能 ===")
    
    try:
        with patch('auto_recording_lifecycle_manager.AutoRecordingManager', MockSlowAutoRecordingManager):
            from auto_recording_lifecycle_manager import AutoRecordingLifecycleManager
            
            # 创建生命周期管理器，设置较小的重启限制
            lifecycle_manager = AutoRecordingLifecycleManager(
                slow_threshold=2.0,  # 更低的阈值
                max_slow_count=2,    # 更少的慢识别次数
                restart_delay=0.5,   # 更快的重启
                max_restarts=2,      # 最多2次重启
                restart_cooldown=1.0 # 更短的冷却时间
            )
            
            lifecycle_manager.configure_manager(debug=True)
            lifecycle_manager.start()
            
            # 运行较长时间，观察是否会停止重启
            print("[TEST] 运行20秒观察重启限制...")
            start_time = time.time()
            
            while time.time() - start_time < 20:
                status = lifecycle_manager.get_status()
                print(f"[TEST] 重启次数: {status['restart_count']}/{status['config']['max_restarts']}")
                
                # 如果达到最大重启次数，验证是否停止重启
                if status['restart_count'] >= status['config']['max_restarts']:
                    print(f"[TEST] 已达到最大重启次数，等待5秒验证不再重启...")
                    old_count = status['restart_count']
                    time.sleep(5)
                    new_status = lifecycle_manager.get_status()
                    if new_status['restart_count'] == old_count:
                        print(f"[TEST] ✅ 重启限制测试通过，重启次数保持在{old_count}")
                        break
                    else:
                        print(f"[TEST] ❌ 重启限制测试失败，重启次数继续增加")
                        break
                        
                time.sleep(2)
                
            lifecycle_manager.stop()
            
    except Exception as e:
        print(f"[TEST] ❌ 重启限制测试出现异常: {e}")
        import traceback
        traceback.print_exc()

def main():
    """运行所有测试"""
    print("开始测试生命周期管理器...")
    
    # 测试1: 基本功能
    test_lifecycle_manager_basic()
    
    # 测试2: 性能监控
    test_performance_monitoring()
    
    # 测试3: 重启限制
    test_restart_limits()
    
    print("\n=== 所有测试完成 ===")

if __name__ == "__main__":
    main()