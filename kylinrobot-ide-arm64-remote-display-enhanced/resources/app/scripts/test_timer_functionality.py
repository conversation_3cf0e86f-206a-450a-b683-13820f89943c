#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试listenHF.py中的定时器功能
作者: AI助手
日期: 2024.12.17
"""

import sys
import os
import time
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from listenHF import AppSpecificMenuMonitor

def test_timer_functionality():
    """测试定时器功能"""
    print("🧪 测试listenHF.py中的定时器功能")
    print("=" * 60)
    
    # 创建监控器实例
    monitor = AppSpecificMenuMonitor()
    record_file = monitor.recordfile1
    
    print(f"📄 测试文件: {record_file}")
    print()
    
    # 测试1: 写入内容并启动定时器
    print("📝 测试1: 写入内容并启动定时器")
    print("-" * 40)
    
    # 模拟写入内容到文件
    test_data = {
        "0": {
            "Name": "测试选项1",
            "Rolename": "list item",
            "Coords": {"x": 100, "y": 200, "width": 80, "height": 20}
        },
        "1": {
            "Name": "测试选项2", 
            "Rolename": "list item",
            "Coords": {"x": 100, "y": 220, "width": 80, "height": 20}
        }
    }
    
    try:
        # 清空文件
        with open(record_file, 'w', encoding='UTF-8') as f:
            f.write('')
        
        # 写入测试数据
        with open(record_file, 'w', encoding='UTF-8') as f:
            json.dump(test_data, f, indent=4, ensure_ascii=False)
        
        print(f"✅ 已写入测试数据到 {record_file}")
        
        # 启动定时器
        monitor.start_clear_timer()
        print("✅ 已启动5秒定时器")
        
        # 检查文件内容
        print("\n📋 当前文件内容:")
        with open(record_file, 'r', encoding='UTF-8') as f:
            content = f.read()
            if content.strip():
                print(f"   文件大小: {len(content)} 字符")
                print(f"   内容预览: {content[:100]}...")
            else:
                print("   文件为空")
        
        # 等待定时器触发
        print("\n⏰ 等待5秒定时器触发...")
        for i in range(6):
            print(f"   倒计时: {5-i}秒", end='\r')
            time.sleep(1)
        
        print("\n")
        
        # 检查文件是否被清空
        print("🔍 检查定时器是否生效:")
        with open(record_file, 'r', encoding='UTF-8') as f:
            content_after = f.read()
            if content_after.strip():
                print(f"❌ 文件未被清空，当前大小: {len(content_after)} 字符")
            else:
                print("✅ 文件已被自动清空")
        
    except Exception as e:
        print(f"❌ 测试1失败: {e}")
    
    print("\n" + "=" * 60)
    
    # 测试2: 测试定时器取消功能
    print("📝 测试2: 测试定时器取消功能")
    print("-" * 40)
    
    try:
        # 再次写入内容
        with open(record_file, 'w', encoding='UTF-8') as f:
            json.dump({"test": "cancel timer"}, f)
        
        print(f"✅ 已写入测试数据到 {record_file}")
        
        # 启动定时器
        monitor.start_clear_timer()
        print("✅ 已启动5秒定时器")
        
        # 等待2秒后取消定时器
        print("⏰ 等待2秒后取消定时器...")
        time.sleep(2)
        
        monitor.cancel_clear_timer()
        print("✅ 已取消定时器")
        
        # 再等待4秒，确认文件不会被清空
        print("⏰ 再等待4秒，确认文件不会被清空...")
        time.sleep(4)
        
        # 检查文件内容
        with open(record_file, 'r', encoding='UTF-8') as f:
            content = f.read()
            if content.strip():
                print("✅ 文件内容保持不变，定时器取消成功")
            else:
                print("❌ 文件被意外清空")
        
    except Exception as e:
        print(f"❌ 测试2失败: {e}")
    
    print("\n" + "=" * 60)
    
    # 测试3: 测试定时器重置功能
    print("📝 测试3: 测试定时器重置功能")
    print("-" * 40)
    
    try:
        # 写入内容并启动定时器
        with open(record_file, 'w', encoding='UTF-8') as f:
            json.dump({"test": "reset timer"}, f)
        
        monitor.start_clear_timer()
        print("✅ 启动第一个定时器")
        
        # 等待2秒后重新启动定时器
        time.sleep(2)
        monitor.start_clear_timer()
        print("✅ 重新启动定时器（应该重置为5秒）")
        
        # 等待4秒（总共6秒，但定时器应该重置了）
        print("⏰ 等待4秒...")
        time.sleep(4)
        
        # 检查文件是否还存在内容
        with open(record_file, 'r', encoding='UTF-8') as f:
            content = f.read()
            if content.strip():
                print("✅ 文件内容仍存在，定时器重置成功")
            else:
                print("❌ 文件被意外清空")
        
        # 再等待2秒，定时器应该触发
        print("⏰ 再等待2秒，定时器应该触发...")
        time.sleep(2)
        
        with open(record_file, 'r', encoding='UTF-8') as f:
            content = f.read()
            if content.strip():
                print("❌ 文件未被清空")
            else:
                print("✅ 文件被正确清空")
        
    except Exception as e:
        print(f"❌ 测试3失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 定时器功能测试完成！")

def test_file_monitoring():
    """测试文件监控功能"""
    print("\n🔍 文件监控测试")
    print("-" * 40)
    
    monitor = AppSpecificMenuMonitor()
    record_file = monitor.recordfile1
    
    print(f"📄 监控文件: {record_file}")
    print("💡 手动操作指南:")
    print("1. 在另一个终端中向文件写入内容")
    print("2. 观察5秒后文件是否自动清空")
    print("3. 按 Ctrl+C 停止监控")
    print()
    
    try:
        last_content = ""
        while True:
            try:
                if os.path.exists(record_file):
                    with open(record_file, 'r', encoding='UTF-8') as f:
                        current_content = f.read()
                    
                    if current_content != last_content:
                        if current_content.strip():
                            print(f"📝 检测到文件内容变化 ({len(current_content)} 字符)")
                            print("   启动5秒定时器...")
                            monitor.start_clear_timer()
                        else:
                            print("🗑️ 文件已清空")
                        
                        last_content = current_content
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                print("\n👋 停止监控")
                break
            except Exception as e:
                print(f"⚠️ 监控错误: {e}")
                time.sleep(1)
                
    finally:
        monitor.cancel_clear_timer()

def main():
    """主函数"""
    print("🧪 listenHF.py 定时器功能测试工具")
    print("=" * 80)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 自动定时器功能测试")
        print("2. 手动文件监控测试")
        print("3. 退出")
        
        try:
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == '1':
                test_timer_functionality()
            elif choice == '2':
                test_file_monitoring()
            elif choice == '3':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
