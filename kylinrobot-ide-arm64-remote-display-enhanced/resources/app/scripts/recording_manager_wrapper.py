#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动录制管理器包装器
负责管理 auto_recording_manager.py 的生命周期，包括超时检测和自动重启
"""

import sys
import os
import time
import threading
import subprocess
import signal
import queue
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import psutil

class RecordingManagerWrapper:
    """录制管理器包装器类"""
    
    def __init__(self, timeout_seconds: int = 3):
        """
        初始化包装器
        
        Args:
            timeout_seconds: 控件识别超时时间（秒）
        """
        self.timeout_seconds = timeout_seconds
        self.process: Optional[subprocess.Popen] = None
        self.monitor_thread: Optional[threading.Thread] = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 10  # 最大重启次数
        
        # 性能监控
        self.last_response_time = time.time()
        self.response_queue = queue.Queue()
        
        # 脚本路径
        self.script_path = Path(__file__).parent / "auto_recording_manager.py"
        if not self.script_path.exists():
            raise FileNotFoundError(f"找不到录制管理器脚本: {self.script_path}")
        
        print(f"[RecordingWrapper] 初始化完成，超时设置: {timeout_seconds}秒")
    
    def start(self) -> bool:
        """
        启动录制管理器
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_running:
                print("[RecordingWrapper] 录制管理器已在运行")
                return True
            
            print(f"[RecordingWrapper] 启动录制管理器 (第{self.restart_count + 1}次)")
            
            # 启动子进程
            self.process = subprocess.Popen(
                [sys.executable, str(self.script_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.is_running = True
            self.last_response_time = time.time()
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self._monitor_process, daemon=True)
            self.monitor_thread.start()
            
            # 启动性能监控线程
            performance_thread = threading.Thread(target=self._monitor_performance, daemon=True)
            performance_thread.start()
            
            print(f"[RecordingWrapper] 录制管理器启动成功，PID: {self.process.pid}")
            return True
            
        except Exception as e:
            print(f"[RecordingWrapper] 启动失败: {e}")
            self.is_running = False
            return False
    
    def stop(self) -> bool:
        """
        停止录制管理器
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.is_running:
                print("[RecordingWrapper] 录制管理器未在运行")
                return True
            
            print("[RecordingWrapper] 停止录制管理器...")
            self.is_running = False
            
            if self.process:
                # 尝试优雅关闭
                try:
                    self.process.terminate()
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 强制关闭
                    print("[RecordingWrapper] 优雅关闭超时，强制终止进程")
                    self.process.kill()
                    self.process.wait()
                
                print(f"[RecordingWrapper] 进程已终止，退出码: {self.process.returncode}")
                self.process = None
            
            return True
            
        except Exception as e:
            print(f"[RecordingWrapper] 停止失败: {e}")
            return False
    
    def restart(self) -> bool:
        """
        重启录制管理器
        
        Returns:
            bool: 重启是否成功
        """
        if self.restart_count >= self.max_restarts:
            print(f"[RecordingWrapper] 已达到最大重启次数 ({self.max_restarts})，停止重启")
            return False
        
        print(f"[RecordingWrapper] 重启录制管理器 (第{self.restart_count + 1}次)")
        
        # 停止当前进程
        self.stop()
        time.sleep(1)  # 等待进程完全停止
        
        # 增加重启计数
        self.restart_count += 1
        
        # 重新启动
        return self.start()
    
    def _monitor_process(self):
        """监控子进程状态"""
        while self.is_running:
            try:
                if not self.process or self.process.poll() is not None:
                    print("[RecordingWrapper] 检测到进程异常退出，尝试重启...")
                    if self.is_running:  # 确保不是主动停止
                        self.restart()
                    break
                
                time.sleep(1)
                
            except Exception as e:
                print(f"[RecordingWrapper] 进程监控异常: {e}")
                break
    
    def _monitor_performance(self):
        """监控性能和响应时间"""
        while self.is_running:
            try:
                current_time = time.time()
                
                # 检查是否超时
                if current_time - self.last_response_time > self.timeout_seconds:
                    print(f"[RecordingWrapper] 检测到响应超时 ({current_time - self.last_response_time:.1f}s > {self.timeout_seconds}s)")
                    
                    # 检查进程是否还活着但卡住了
                    if self.process and self.process.poll() is None:
                        try:
                            # 检查CPU使用率
                            proc = psutil.Process(self.process.pid)
                            cpu_percent = proc.cpu_percent(interval=1)
                            memory_info = proc.memory_info()
                            
                            print(f"[RecordingWrapper] 进程状态 - CPU: {cpu_percent:.1f}%, 内存: {memory_info.rss / 1024 / 1024:.1f}MB")
                            
                            # 如果CPU使用率很低，可能是卡住了
                            if cpu_percent < 1.0:
                                print("[RecordingWrapper] 进程可能卡住，尝试重启...")
                                self.restart()
                                break
                                
                        except psutil.NoSuchProcess:
                            print("[RecordingWrapper] 进程已不存在")
                            break
                        except Exception as e:
                            print(f"[RecordingWrapper] 检查进程状态失败: {e}")
                
                time.sleep(0.5)  # 更频繁的检查
                
            except Exception as e:
                print(f"[RecordingWrapper] 性能监控异常: {e}")
                break
    
    def send_command(self, command: str, data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        向录制管理器发送命令
        
        Args:
            command: 命令名称
            data: 命令数据
            
        Returns:
            命令响应结果
        """
        if not self.is_running or not self.process:
            print("[RecordingWrapper] 录制管理器未运行")
            return None
        
        try:
            # 构造命令
            cmd_data = {
                "command": command,
                "data": data or {},
                "timestamp": time.time()
            }
            
            # 发送命令
            cmd_json = json.dumps(cmd_data) + "\n"
            self.process.stdin.write(cmd_json)
            self.process.stdin.flush()
            
            # 更新响应时间
            self.last_response_time = time.time()
            
            print(f"[RecordingWrapper] 发送命令: {command}")
            return {"status": "sent", "command": command}
            
        except Exception as e:
            print(f"[RecordingWrapper] 发送命令失败: {e}")
            return None
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取包装器状态
        
        Returns:
            状态信息字典
        """
        status = {
            "is_running": self.is_running,
            "restart_count": self.restart_count,
            "timeout_seconds": self.timeout_seconds,
            "last_response_time": self.last_response_time,
            "process_pid": self.process.pid if self.process else None
        }
        
        if self.process:
            try:
                proc = psutil.Process(self.process.pid)
                status.update({
                    "cpu_percent": proc.cpu_percent(),
                    "memory_mb": proc.memory_info().rss / 1024 / 1024,
                    "create_time": proc.create_time()
                })
            except:
                pass
        
        return status
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()


def main():
    """主函数 - 演示用法"""
    print("=== 录制管理器包装器演示 ===")
    
    # 创建包装器实例
    wrapper = RecordingManagerWrapper(timeout_seconds=3)
    
    try:
        # 启动录制管理器
        if wrapper.start():
            print("录制管理器启动成功")
            
            # 运行一段时间进行测试
            for i in range(30):
                time.sleep(1)
                status = wrapper.get_status()
                print(f"状态检查 {i+1}: 运行={status['is_running']}, 重启次数={status['restart_count']}")
                
                # 模拟发送命令
                if i % 5 == 0:
                    wrapper.send_command("test_command", {"test": True})
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止...")
    
    finally:
        # 停止录制管理器
        wrapper.stop()
        print("录制管理器已停止")


if __name__ == "__main__":
    main()
