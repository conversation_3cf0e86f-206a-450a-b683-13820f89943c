#!/usr/bin/env python3
"""
带生命周期管理的AutoRecording启动脚本
当控件识别过慢时自动重启管理器
"""

import sys
import os
import argparse
import signal

# 添加scripts目录到Python路径
scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scripts')
if scripts_dir not in sys.path:
    sys.path.insert(0, scripts_dir)

try:
    from auto_recording_lifecycle_manager import AutoRecordingLifecycleManager
except ImportError as e:
    print(f"[ERROR] 无法导入生命周期管理器: {e}", file=sys.stderr)
    print(f"[ERROR] 请确保scripts/auto_recording_lifecycle_manager.py文件存在", file=sys.stderr)
    sys.exit(1)

# 全局变量
lifecycle_manager = None

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n[MAIN] 🛑 收到信号 {signum}，正在优雅退出...", file=sys.stderr)
    global lifecycle_manager
    if lifecycle_manager:
        lifecycle_manager.stop()
    sys.exit(0)

def main():
    """主函数"""
    global lifecycle_manager
    
    # 设置DISPLAY环境变量
    if 'DISPLAY' not in os.environ or not os.environ['DISPLAY']:
        os.environ['DISPLAY'] = ':0'
        print(f"[MAIN] 设置DISPLAY环境变量为: {os.environ['DISPLAY']}", file=sys.stderr)
    
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='带生命周期管理的AutoRecording启动器')
    parser.add_argument('--slow-threshold', type=float, default=3.0,
                       help='慢识别阈值（秒），默认3.0')
    parser.add_argument('--max-slow-count', type=int, default=1,
                       help='最大慢识别次数，默认1')
    parser.add_argument('--restart-delay', type=float, default=2.0,
                       help='重启延迟时间（秒），默认2.0')
    parser.add_argument('--max-restarts', type=int, default=5,
                       help='最大重启次数，默认5')
    parser.add_argument('--restart-cooldown', type=float, default=30.0,
                       help='重启冷却时间（秒），默认30.0')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    parser.add_argument('--json-output', action='store_true',
                       help='启用JSON输出模式')
    parser.add_argument('--storage-path', type=str, default='recordings',
                       help='录制文件存储路径，默认recordings')
    
    # 兼容auto_recording_manager.py的参数
    parser.add_argument('--duration', type=int, help='录制时长（秒）')
    parser.add_argument('--test-case-id', type=str, help='测试用例ID')
    parser.add_argument('--testcase-path', type=str, help='测试用例路径')
    parser.add_argument('--app-name', type=str, help='录制的应用名称')
    
    args = parser.parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        print("=" * 80, file=sys.stderr)
        print("🚀 AutoRecording生命周期管理启动器", file=sys.stderr)
        print("=" * 80, file=sys.stderr)
        print(f"📊 配置参数:", file=sys.stderr)
        print(f"   慢识别阈值: {args.slow_threshold}秒", file=sys.stderr)
        print(f"   最大慢识别次数: {args.max_slow_count}", file=sys.stderr)
        print(f"   重启延迟: {args.restart_delay}秒", file=sys.stderr)
        print(f"   最大重启次数: {args.max_restarts}", file=sys.stderr)
        print(f"   重启冷却时间: {args.restart_cooldown}秒", file=sys.stderr)
        print(f"   调试模式: {args.debug}", file=sys.stderr)
        print(f"   JSON输出模式: {args.json_output}", file=sys.stderr)
        print(f"   存储路径: {args.storage_path}", file=sys.stderr)
        print("=" * 80, file=sys.stderr)
        
        # 创建生命周期管理器
        lifecycle_manager = AutoRecordingLifecycleManager(
            slow_threshold=args.slow_threshold,
            max_slow_count=args.max_slow_count,
            restart_delay=args.restart_delay,
            max_restarts=args.max_restarts,
            restart_cooldown=args.restart_cooldown
        )
        
        # 配置管理器参数 - 只传递AutoRecordingManager构造函数支持的参数
        manager_config = {
            'debug': args.debug,
            'json_output': args.json_output,
            'storage_path': args.storage_path
        }
        
        # 将扩展参数保存，在管理器启动时使用
        extended_config = {}
        if args.duration:
            extended_config['duration'] = args.duration
        if args.test_case_id:
            extended_config['test_case_id'] = args.test_case_id
        if args.testcase_path:
            extended_config['testcase_path'] = args.testcase_path
        if args.app_name:
            extended_config['app_name'] = args.app_name
            
        lifecycle_manager.configure_manager(**manager_config)
        lifecycle_manager.set_extended_config(extended_config)
        
        # 启动生命周期管理器
        lifecycle_manager.start()
        
        print(f"[MAIN] 🎯 AutoRecording已启动，生命周期管理器正在监控性能", file=sys.stderr)
        print(f"[MAIN] 📊 按Ctrl+C退出程序", file=sys.stderr)
        restart_text = "第一次" if args.max_slow_count == 1 else f"连续{args.max_slow_count}次"
        print(f"[MAIN] 🔄 当{restart_text}识别超过{args.slow_threshold}秒时将自动重启", file=sys.stderr)
        
        # 主循环 - 定期报告状态
        import time
        status_interval = 30  # 每30秒报告一次状态
        last_status_time = 0
        
        while lifecycle_manager.is_running():
            current_time = time.time()
            
            # 定期输出状态
            if current_time - last_status_time >= status_interval:
                status = lifecycle_manager.get_status()
                stats = status['performance_stats']
                
                print(f"[MAIN] 📈 状态报告:", file=sys.stderr)
                print(f"   运行状态: {'正常' if status['running'] else '已停止'}", file=sys.stderr)
                print(f"   管理器活跃: {'是' if status['manager_active'] else '否'}", file=sys.stderr)
                print(f"   重启次数: {status['restart_count']}/{status['config']['max_restarts']}", file=sys.stderr)
                print(f"   总识别次数: {stats['total_recognitions']}", file=sys.stderr)
                print(f"   慢识别次数: {stats['slow_recognitions']}", file=sys.stderr)
                print(f"   当前连续慢识别: {stats['current_slow_count']}/{status['config']['max_slow_count']}", file=sys.stderr)
                
                if status['restart_count'] > 0:
                    last_restart = time.ctime(status['last_restart_time']) if status['last_restart_time'] > 0 else '无'
                    print(f"   最后重启时间: {last_restart}", file=sys.stderr)
                
                print(f"[MAIN] " + "─" * 60, file=sys.stderr)
                last_status_time = current_time
                
            time.sleep(1)
            
    except KeyboardInterrupt:
        print(f"\n[MAIN] 🛑 用户中断，正在退出...", file=sys.stderr)
    except Exception as e:
        print(f"[ERROR] 主程序发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
    finally:
        if lifecycle_manager:
            lifecycle_manager.stop()
        print(f"[MAIN] 👋 程序已退出", file=sys.stderr)

if __name__ == "__main__":
    main()