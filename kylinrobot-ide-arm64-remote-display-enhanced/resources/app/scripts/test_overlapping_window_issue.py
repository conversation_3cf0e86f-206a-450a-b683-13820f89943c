#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重叠窗口识别问题
坐标(552, 224) - 应该识别到最上层的"打开文件"对话框，而不是下层的"信任区"对话框
"""

import sys
import time
from pathlib import Path

# 添加scripts目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_coordinate_recognition():
    """测试指定坐标的控件识别"""
    try:
        from UNI import UNI
        from auto_recording_manager import WidgetAnalyzer
        
        print("=" * 80)
        print("🔍 重叠窗口识别问题分析")
        print("=" * 80)
        print(f"测试坐标: (552, 224)")
        print(f"预期: 识别到最上层的'打开文件'对话框")
        print(f"问题: 只识别到下层的'信任区'对话框")
        print("=" * 80)
        
        # 创建UNI实例
        uni = UNI()
        
        # 方法1: 直接使用UNI识别
        print("\n📍 方法1: 直接UNI识别")
        print("-" * 40)
        
        start_time = time.time()
        result, info = uni.kdk_getElement_Uni(552, 224, False, True)
        end_time = time.time()
        
        print(f"⏰ 耗时: {end_time - start_time:.3f}秒")
        if result:
            print(f"🎯 识别结果:")
            print(f"   控件名称: {result.get('Name', 'N/A')}")
            print(f"   控件类型: {result.get('ControlType', 'N/A')}")
            print(f"   进程名称: {result.get('ProcessName', 'N/A')}")
            print(f"   窗口名称: {result.get('WindowName', 'N/A')}")
            print(f"   父窗口路径: {result.get('ParentPath', 'N/A')}")
            if 'Coords' in result:
                coords = result['Coords']
                print(f"   坐标信息: x={coords.get('x', 'N/A')}, y={coords.get('y', 'N/A')}, w={coords.get('width', 'N/A')}, h={coords.get('height', 'N/A')}")
        else:
            print(f"❌ 识别失败: {info}")
        
        # 方法2: 使用WidgetAnalyzer
        print("\n📍 方法2: WidgetAnalyzer识别")
        print("-" * 40)
        
        analyzer = WidgetAnalyzer(debug=True)
        start_time = time.time()
        widget_info, info_text = analyzer.analyze_widget_at(552, 224)
        end_time = time.time()
        
        print(f"⏰ 耗时: {end_time - start_time:.3f}秒")
        if widget_info and not widget_info.get('error'):
            print(f"🎯 识别结果:")
            print(f"   控件名称: {widget_info.get('Name', 'N/A')}")
            print(f"   控件类型: {widget_info.get('ControlType', 'N/A')}")
            print(f"   进程名称: {widget_info.get('ProcessName', 'N/A')}")
            print(f"   窗口名称: {widget_info.get('WindowName', 'N/A')}")
        else:
            print(f"❌ 识别失败: {info_text}")
            
        # 方法3: 分析窗口层级
        print("\n📍 方法3: 窗口层级分析")
        print("-" * 40)
        
        analyze_window_hierarchy(uni, 552, 224)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_window_hierarchy(uni, x, y):
    """分析指定坐标处的窗口层级"""
    try:
        print("🔍 分析窗口层级...")
        
        # 获取活动窗口信息
        active_window, process_id, coords, role_name, child_count = uni._get_active_window(x, y)
        
        if active_window:
            print(f"🪟 活动窗口信息:")
            print(f"   窗口名称: {getattr(active_window, 'name', 'N/A')}")
            print(f"   进程ID: {process_id}")
            print(f"   角色名称: {role_name}")
            print(f"   子控件数: {child_count}")
            if coords:
                print(f"   窗口坐标: x={coords[0]}, y={coords[1]}, w={coords[2]}, h={coords[3]}")
        
        # 分析所有可能的窗口
        print(f"\n🔍 分析坐标({x}, {y})处的所有可能窗口:")
        analyze_all_windows_at_point(uni, x, y)
        
    except Exception as e:
        print(f"❌ 窗口层级分析失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_all_windows_at_point(uni, x, y):
    """分析指定坐标处的所有窗口"""
    try:
        import pyatspi
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        if not desktop:
            print("❌ 无法获取桌面对象")
            return
            
        print(f"📱 桌面应用程序数量: {desktop.childCount}")
        
        matching_windows = []
        
        # 遍历所有应用程序
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if not app:
                    continue
                    
                print(f"\n🔍 检查应用程序 {i+1}: {getattr(app, 'name', 'N/A')}")
                
                # 遍历应用程序的所有窗口
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        if not window:
                            continue
                            
                        # 获取窗口坐标
                        try:
                            component = window.queryComponent()
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            win_x, win_y, win_w, win_h = extents.x, extents.y, extents.width, extents.height
                            
                            # 检查点击坐标是否在窗口内
                            if win_x <= x <= win_x + win_w and win_y <= y <= win_y + win_h:
                                window_info = {
                                    'window': window,
                                    'app_name': getattr(app, 'name', 'N/A'),
                                    'window_name': getattr(window, 'name', 'N/A'),
                                    'role': window.getRoleName() if hasattr(window, 'getRoleName') else 'N/A',
                                    'coords': (win_x, win_y, win_w, win_h),
                                    'z_order': j  # 简单的z-order，后面的窗口通常在上层
                                }
                                matching_windows.append(window_info)
                                
                                print(f"   ✅ 匹配窗口: {window_info['window_name']}")
                                print(f"      角色: {window_info['role']}")
                                print(f"      坐标: ({win_x}, {win_y}, {win_w}, {win_h})")
                                print(f"      Z-Order: {j}")
                                
                        except Exception as coord_e:
                            # 无法获取坐标，跳过
                            pass
                            
                    except Exception as window_e:
                        # 窗口访问失败，跳过
                        pass
                        
            except Exception as app_e:
                # 应用程序访问失败，跳过
                pass
        
        # 分析匹配结果
        print(f"\n📊 分析结果:")
        print(f"   匹配的窗口数量: {len(matching_windows)}")
        
        if len(matching_windows) > 1:
            print("   🚨 发现重叠窗口!")
            
            # 按Z-order排序（简单排序）
            matching_windows.sort(key=lambda w: w['z_order'], reverse=True)
            
            print("   窗口层级 (从上到下):")
            for idx, win_info in enumerate(matching_windows):
                print(f"     {idx+1}. {win_info['window_name']} ({win_info['app_name']})")
                print(f"        角色: {win_info['role']}")
                print(f"        Z-Order: {win_info['z_order']}")
            
            # 当前识别逻辑可能选择了错误的窗口
            print(f"\n💡 问题分析:")
            print(f"   - 应该识别: {matching_windows[0]['window_name']} (最上层)")
            print(f"   - 实际识别: 可能是 {matching_windows[-1]['window_name']} (下层)")
            
    except Exception as e:
        print(f"❌ 窗口分析失败: {e}")

def main():
    """主函数"""
    print("🚀 重叠窗口识别问题分析工具")
    print("请确保桌面上有重叠窗口的场景（信任区对话框 + 打开文件对话框）")
    input("按Enter键开始分析...")
    
    test_coordinate_recognition()
    
    print("\n" + "=" * 80)
    print("✅ 分析完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()