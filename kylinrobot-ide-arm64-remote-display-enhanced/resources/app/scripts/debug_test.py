#!/usr/bin/env python3
# -*- coding=utf-8 -*-

import UNI
import sys

def test_current_recognition():
    """测试当前的控件识别情况"""
    print("=" * 60)
    print("测试坐标 (241, 188) 的控件识别")
    print("=" * 60)
    
    try:
        # 重定向stderr到stdout以便看到调试信息
        import os
        os.environ['PYTHONUNBUFFERED'] = '1'
        
        a = UNI.UNI()
        
        print("\n🔍 开始控件识别...")
        data, text = a.kdk_getElement_Uni(241, 188)
        
        print("\n" + "="*60)
        print("📊 最终识别结果:")
        print("="*60)
        print(f"名称: {data.get('Name', 'N/A')}")
        print(f"角色: {data.get('Rolename', 'N/A')}")
        print(f"描述: {data.get('Description', 'N/A')}")
        print(f"位置: ({data.get('Coords', {}).get('x', 0)}, {data.get('Coords', {}).get('y', 0)})")
        print(f"大小: {data.get('Coords', {}).get('width', 0)}x{data.get('Coords', {}).get('height', 0)}")
        print(f"面积: {data.get('Coords', {}).get('width', 0) * data.get('Coords', {}).get('height', 0)} 像素")
        print(f"状态: {text}")
        print(f"进程: {data.get('ProcessName', 'N/A')}")
        print(f"窗口: {data.get('WindowName', 'N/A')}")
        
        if 'error' in data:
            print(f"❌ 错误: {data['error']}")
            return False
        
        # 分析结果
        print("\n📈 结果分析:")
        width = data.get('Coords', {}).get('width', 0)
        height = data.get('Coords', {}).get('height', 0)
        area = width * height
        role = data.get('Rolename', '')
        name = data.get('Name', '')
        
        if role == 'label' and name == '账户信息':
            print("✅ 成功！识别到了'账户信息'小标签")
            print(f"   - 这是一个 {width}x{height} 的小标签控件")
            return True
        elif 'button' in role.lower():
            print("⚠️  识别到按钮控件，可能是包含标签的大按钮")
            print(f"   - 按钮大小: {width}x{height}")
            print("   - 期望识别到内部的小标签")
            return False
        elif area > 10000:
            print("⚠️  识别到大面积控件，可能是容器")
            print(f"   - 控件面积: {area} 像素")
            print("   - 期望识别到更精确的小控件")
            return False
        else:
            print(f"ℹ️  识别到控件: {name} ({role})")
            print(f"   - 面积: {area} 像素")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_call():
    """简单测试调用"""
    print("\n" + "="*60)
    print("🧪 简单测试调用")
    print("="*60)
    
    try:
        a = UNI.UNI()
        # 直接调用，不显示调试信息
        import sys
        from io import StringIO
        
        # 捕获stderr
        old_stderr = sys.stderr
        sys.stderr = StringIO()
        
        data, text = a.kdk_getElement_Uni(241, 188)
        
        # 恢复stderr
        captured_stderr = sys.stderr.getvalue()
        sys.stderr = old_stderr
        
        print("识别结果（无调试信息）:")
        print(f"  名称: {data.get('Name', 'N/A')}")
        print(f"  角色: {data.get('Rolename', 'N/A')}")
        print(f"  大小: {data.get('Coords', {}).get('width', 0)}x{data.get('Coords', {}).get('height', 0)}")
        print(f"  状态: {text}")
        
        # 检查是否有智能搜索的痕迹
        if "智能深度搜索" in captured_stderr:
            print("✅ 智能深度搜索已启用")
        else:
            print("❌ 智能深度搜索未启用")
            
        if "账户信息" in captured_stderr:
            print("✅ 找到了'账户信息'标签")
        else:
            print("❌ 未找到'账户信息'标签")
            
        return data.get('Name') == '账户信息' and data.get('Rolename') == 'label'
        
    except Exception as e:
        print(f"简单测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试UNI.py的控件识别功能...")
    
    # 测试1：详细测试
    success1 = test_current_recognition()
    
    # 测试2：简单测试
    success2 = test_simple_call()
    
    print("\n" + "="*60)
    print("📋 测试总结:")
    print("="*60)
    print(f"详细测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"简单测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试通过！智能识别功能正常工作")
    else:
        print("⚠️  测试未完全通过，需要进一步调试")
    
    sys.exit(0 if (success1 and success2) else 1)
