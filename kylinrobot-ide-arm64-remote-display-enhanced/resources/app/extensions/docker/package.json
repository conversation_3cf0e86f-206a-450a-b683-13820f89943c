{"name": "docker", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin moby/moby contrib/syntax/textmate/Docker.tmbundle/Syntaxes/Dockerfile.tmLanguage ./syntaxes/docker.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "dockerfile", "extensions": [".docker<PERSON>le", ".containerfile"], "filenames": ["Dockerfile", "Containerfile"], "filenamePatterns": ["Dockerfile.*", "Containerfile.*"], "aliases": ["<PERSON>er", "Dockerfile", "Containerfile"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "dockerfile", "scopeName": "source.dockerfile", "path": "./syntaxes/docker.tmLanguage.json"}], "configurationDefaults": {"[dockerfile]": {"editor.quickSuggestions": {"strings": true}}}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}