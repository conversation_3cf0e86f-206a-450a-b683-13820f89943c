/*! For license information please see npmMain.js.LICENSE.txt */
(()=>{var e={54:(e,t,n)=>{"use strict";const r=n(1929),i=(e,t,n)=>{if(!1===r(e))throw new TypeError("toRegexRange: expected the first argument to be a number");if(void 0===t||e===t)return String(e);if(!1===r(t))throw new TypeError("toRegexRange: expected the second argument to be a number.");let s={relaxZeros:!0,...n};"boolean"==typeof s.strictZeros&&(s.relaxZeros=!1===s.strictZeros);let u=e+":"+t+"="+String(s.relaxZeros)+String(s.shorthand)+String(s.capture)+String(s.wrap);if(i.cache.hasOwnProperty(u))return i.cache[u].result;let c=Math.min(e,t),l=Math.max(e,t);if(1===Math.abs(c-l)){let n=e+"|"+t;return s.capture?`(${n})`:!1===s.wrap?n:`(?:${n})`}let h=d(e)||d(t),p={min:e,max:t,a:c,b:l},f=[],m=[];return h&&(p.isPadded=h,p.maxLen=String(p.max).length),c<0&&(m=o(l<0?Math.abs(l):1,Math.abs(c),p,s),c=p.a=0),l>=0&&(f=o(c,l,p,s)),p.negatives=m,p.positives=f,p.result=function(e,t){let n=a(e,t,"-",!1)||[],r=a(t,e,"",!1)||[],i=a(e,t,"-?",!0)||[];return n.concat(i).concat(r).join("|")}(m,f),!0===s.capture?p.result=`(${p.result})`:!1!==s.wrap&&f.length+m.length>1&&(p.result=`(?:${p.result})`),i.cache[u]=p,p.result};function s(e,t,n){if(e===t)return{pattern:e,count:[],digits:0};let r=function(e,t){let n=[];for(let r=0;r<e.length;r++)n.push([e[r],t[r]]);return n}(e,t),i=r.length,s="",o=0;for(let e=0;e<i;e++){let[t,n]=r[e];t===n?s+=t:"0"!==t||"9"!==n?s+=f(t,n):o++}return o&&(s+=!0===n.shorthand?"\\d":"[0-9]"),{pattern:s,count:[o],digits:i}}function o(e,t,n,r){let i,o=function(e,t){let n=1,r=1,i=l(e,n),s=new Set([t]);for(;e<=i&&i<=t;)s.add(i),n+=1,i=l(e,n);for(i=h(t+1,r)-1;e<i&&i<=t;)s.add(i),r+=1,i=h(t+1,r)-1;return s=[...s],s.sort(u),s}(e,t),a=[],c=e;for(let e=0;e<o.length;e++){let t=o[e],u=s(String(c),String(t),r),l="";n.isPadded||!i||i.pattern!==u.pattern?(n.isPadded&&(l=m(t,n,r)),u.string=l+u.pattern+p(u.count),a.push(u),c=t+1,i=u):(i.count.length>1&&i.count.pop(),i.count.push(u.count[0]),i.string=i.pattern+p(i.count),c=t+1)}return a}function a(e,t,n,r,i){let s=[];for(let i of e){let{string:e}=i;r||c(t,"string",e)||s.push(n+e),r&&c(t,"string",e)&&s.push(n+e)}return s}function u(e,t){return e>t?1:t>e?-1:0}function c(e,t,n){return e.some(e=>e[t]===n)}function l(e,t){return Number(String(e).slice(0,-t)+"9".repeat(t))}function h(e,t){return e-e%Math.pow(10,t)}function p(e){let[t=0,n=""]=e;return n||t>1?`{${t+(n?","+n:"")}}`:""}function f(e,t,n){return`[${e}${t-e===1?"":"-"}${t}]`}function d(e){return/^-?(0+)\d/.test(e)}function m(e,t,n){if(!t.isPadded)return e;let r=Math.abs(t.maxLen-String(e).length),i=!1!==n.relaxZeros;switch(r){case 0:return"";case 1:return i?"0?":"0";case 2:return i?"0{0,2}":"00";default:return i?`0{0,${r}}`:`0{${r}}`}}i.cache={},i.clearCache=()=>i.cache={},e.exports=i},165:(e,t,n)=>{"use strict";const r=n(4693);e.exports=e=>{if(!Number.isInteger(e)&&e!==1/0||!(e>0))throw new TypeError("Expected `concurrency` to be a number from 1 and up");const t=new r;let n=0;const i=async(e,r,...i)=>{n++;const s=(async()=>e(...i))();r(s);try{await s}catch{}n--,t.size>0&&t.dequeue()()},s=(r,...s)=>new Promise(o=>{((r,s,...o)=>{t.enqueue(i.bind(null,r,s,...o)),(async()=>{await Promise.resolve(),n<e&&t.size>0&&t.dequeue()()})()})(r,o,...s)});return Object.defineProperties(s,{activeCount:{get:()=>n},pendingCount:{get:()=>t.size},clearQueue:{value:()=>{t.clear()}}}),s}},181:e=>{"use strict";e.exports=require("buffer")},280:(e,t,n)=>{"use strict";const r=n(6928),i="win32"===process.platform,{REGEX_BACKSLASH:s,REGEX_REMOVE_BACKSLASH:o,REGEX_SPECIAL_CHARS:a,REGEX_SPECIAL_CHARS_GLOBAL:u}=n(3940);t.isObject=e=>null!==e&&"object"==typeof e&&!Array.isArray(e),t.hasRegexChars=e=>a.test(e),t.isRegexChar=e=>1===e.length&&t.hasRegexChars(e),t.escapeRegex=e=>e.replace(u,"\\$1"),t.toPosixSlashes=e=>e.replace(s,"/"),t.removeBackslashes=e=>e.replace(o,e=>"\\"===e?"":e),t.supportsLookbehinds=()=>{const e=process.version.slice(1).split(".").map(Number);return 3===e.length&&e[0]>=9||8===e[0]&&e[1]>=10},t.isWindows=e=>e&&"boolean"==typeof e.windows?e.windows:!0===i||"\\"===r.sep,t.escapeLast=(e,n,r)=>{const i=e.lastIndexOf(n,r);return-1===i?e:"\\"===e[i-1]?t.escapeLast(e,n,i-1):`${e.slice(0,i)}\\${e.slice(i)}`},t.removePrefix=(e,t={})=>{let n=e;return n.startsWith("./")&&(n=n.slice(2),t.prefix="./"),n},t.wrapOutput=(e,t={},n={})=>{let r=`${n.contains?"":"^"}(?:${e})${n.contains?"":"$"}`;return!0===t.negated&&(r=`(?:^(?!${r}).*$)`),r}},499:(e,t,n)=>{"use strict";var r=n(2111);function i(e,t,n,r,i){this.name=e,this.buffer=t,this.position=n,this.line=r,this.column=i}i.prototype.getSnippet=function(e,t){var n,i,s,o,a;if(!this.buffer)return null;for(e=e||4,t=t||75,n="",i=this.position;i>0&&-1==="\0\r\n\u2028\u2029".indexOf(this.buffer.charAt(i-1));)if(i-=1,this.position-i>t/2-1){n=" ... ",i+=5;break}for(s="",o=this.position;o<this.buffer.length&&-1==="\0\r\n\u2028\u2029".indexOf(this.buffer.charAt(o));)if((o+=1)-this.position>t/2-1){s=" ... ",o-=5;break}return a=this.buffer.slice(i,o),r.repeat(" ",e)+n+a+s+"\n"+r.repeat(" ",e+this.position-i+n.length)+"^"},i.prototype.toString=function(e){var t,n="";return this.name&&(n+='in "'+this.name+'" '),n+="at line "+(this.line+1)+", column "+(this.column+1),e||(t=this.getSnippet())&&(n+=":\n"+t),n},e.exports=i},585:(e,t,n)=>{"use strict";var r;try{r=n(1206)}catch(e){"undefined"!=typeof window&&(r=window.esprima)}var i=n(6274);e.exports=new i("tag:yaml.org,2002:js/function",{kind:"scalar",resolve:function(e){if(null===e)return!1;try{var t="("+e+")",n=r.parse(t,{range:!0});return"Program"===n.type&&1===n.body.length&&"ExpressionStatement"===n.body[0].type&&("ArrowFunctionExpression"===n.body[0].expression.type||"FunctionExpression"===n.body[0].expression.type)}catch(e){return!1}},construct:function(e){var t,n="("+e+")",i=r.parse(n,{range:!0}),s=[];if("Program"!==i.type||1!==i.body.length||"ExpressionStatement"!==i.body[0].type||"ArrowFunctionExpression"!==i.body[0].expression.type&&"FunctionExpression"!==i.body[0].expression.type)throw new Error("Failed to resolve function");return i.body[0].expression.params.forEach(function(e){s.push(e.name)}),t=i.body[0].expression.body.range,"BlockStatement"===i.body[0].expression.body.type?new Function(s,n.slice(t[0]+1,t[1]-1)):new Function(s,"return "+n.slice(t[0],t[1]))},predicate:function(e){return"[object Function]"===Object.prototype.toString.call(e)},represent:function(e){return e.toString()}})},599:(e,t,n)=>{"use strict";var r=n(2111),i=n(6274),s=new RegExp("^(?:[-+]?(?:0|[1-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),o=/^[-+]?[0-9]+e/;e.exports=new i("tag:yaml.org,2002:float",{kind:"scalar",resolve:function(e){return null!==e&&!(!s.test(e)||"_"===e[e.length-1])},construct:function(e){var t,n,r,i;return n="-"===(t=e.replace(/_/g,"").toLowerCase())[0]?-1:1,i=[],"+-".indexOf(t[0])>=0&&(t=t.slice(1)),".inf"===t?1===n?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===t?NaN:t.indexOf(":")>=0?(t.split(":").forEach(function(e){i.unshift(parseFloat(e,10))}),t=0,r=1,i.forEach(function(e){t+=e*r,r*=60}),n*t):n*parseFloat(t,10)},predicate:function(e){return"[object Number]"===Object.prototype.toString.call(e)&&(e%1!=0||r.isNegativeZero(e))},represent:function(e,t){var n;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(r.isNegativeZero(e))return"-0.0";return n=e.toString(10),o.test(n)?n.replace("e",".e"):n},defaultStyle:"lowercase"})},628:(e,t,n)=>{"use strict";const r=n(9023),i=n(4537),s=n(5157),o=n(280),a=e=>""===e||"./"===e,u=e=>{const t=e.indexOf("{");return t>-1&&e.indexOf("}",t)>-1},c=(e,t,n)=>{t=[].concat(t),e=[].concat(e);let r=new Set,i=new Set,o=new Set,a=0,u=e=>{o.add(e.output),n&&n.onResult&&n.onResult(e)};for(let o=0;o<t.length;o++){let c=s(String(t[o]),{...n,onResult:u},!0),l=c.state.negated||c.state.negatedExtglob;l&&a++;for(let t of e){let e=c(t,!0);(l?!e.isMatch:e.isMatch)&&(l?r.add(e.output):(r.delete(e.output),i.add(e.output)))}}let c=(a===t.length?[...o]:[...i]).filter(e=>!r.has(e));if(n&&0===c.length){if(!0===n.failglob)throw new Error(`No matches found for "${t.join(", ")}"`);if(!0===n.nonull||!0===n.nullglob)return n.unescape?t.map(e=>e.replace(/\\/g,"")):t}return c};c.match=c,c.matcher=(e,t)=>s(e,t),c.any=c.isMatch=(e,t,n)=>s(t,n)(e),c.not=(e,t,n={})=>{t=[].concat(t).map(String);let r=new Set,i=[],s=new Set(c(e,t,{...n,onResult:e=>{n.onResult&&n.onResult(e),i.push(e.output)}}));for(let e of i)s.has(e)||r.add(e);return[...r]},c.contains=(e,t,n)=>{if("string"!=typeof e)throw new TypeError(`Expected a string: "${r.inspect(e)}"`);if(Array.isArray(t))return t.some(t=>c.contains(e,t,n));if("string"==typeof t){if(a(e)||a(t))return!1;if(e.includes(t)||e.startsWith("./")&&e.slice(2).includes(t))return!0}return c.isMatch(e,t,{...n,contains:!0})},c.matchKeys=(e,t,n)=>{if(!o.isObject(e))throw new TypeError("Expected the first argument to be an object");let r=c(Object.keys(e),t,n),i={};for(let t of r)i[t]=e[t];return i},c.some=(e,t,n)=>{let r=[].concat(e);for(let e of[].concat(t)){let t=s(String(e),n);if(r.some(e=>t(e)))return!0}return!1},c.every=(e,t,n)=>{let r=[].concat(e);for(let e of[].concat(t)){let t=s(String(e),n);if(!r.every(e=>t(e)))return!1}return!0},c.all=(e,t,n)=>{if("string"!=typeof e)throw new TypeError(`Expected a string: "${r.inspect(e)}"`);return[].concat(t).every(t=>s(t,n)(e))},c.capture=(e,t,n)=>{let r=o.isWindows(n),i=s.makeRe(String(e),{...n,capture:!0}).exec(r?o.toPosixSlashes(t):t);if(i)return i.slice(1).map(e=>void 0===e?"":e)},c.makeRe=(...e)=>s.makeRe(...e),c.scan=(...e)=>s.scan(...e),c.parse=(e,t)=>{let n=[];for(let r of[].concat(e||[]))for(let e of i(String(r),t))n.push(s.parse(e,t));return n},c.braces=(e,t)=>{if("string"!=typeof e)throw new TypeError("Expected a string");return t&&!0===t.nobrace||!u(e)?[e]:i(e,t)},c.braceExpand=(e,t)=>{if("string"!=typeof e)throw new TypeError("Expected a string");return c.braces(e,{...t,expand:!0})},c.hasBraces=u,e.exports=c},733:(e,t,n)=>{"use strict";var r=n(6274),i=Object.prototype.hasOwnProperty;e.exports=new r("tag:yaml.org,2002:set",{kind:"mapping",resolve:function(e){if(null===e)return!0;var t,n=e;for(t in n)if(i.call(n,t)&&null!==n[t])return!1;return!0},construct:function(e){return null!==e?e:{}}})},810:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&i(t,e,n[o]);return s(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.findPreferredPM=async function(e){const t=[],n=[],r=await async function(e){const t=await f(l.join(e,"package-lock.json"));return{isPreferred:t,hasLockfile:t}}(e);r.isPreferred&&(t.push("npm"),n.push(r));const i=await async function(e){return await f(l.join(e,"pnpm-lock.yaml"))||await f(l.join(e,"shrinkwrap.yaml"))||await(0,c.default)("pnpm-lock.yaml",{cwd:e})?{isPreferred:!0,hasLockfile:!0}:{isPreferred:!1,hasLockfile:!1}}(e);i.isPreferred&&(t.push("pnpm"),n.push(i));const s=await async function(e){if(await f(l.join(e,"yarn.lock")))return{isPreferred:!0,hasLockfile:!0};try{if("string"==typeof u(e))return{isPreferred:!0,hasLockfile:!1}}catch(e){}return{isPreferred:!1,hasLockfile:!1}}(e);s.isPreferred&&(t.push("yarn"),n.push(s));const o=await async function(e){return await f(l.join(e,"bun.lockb"))||await f(l.join(e,"bun.lock"))?{isPreferred:!0,hasLockfile:!0}:{isPreferred:!1,hasLockfile:!1}}(e);o.isPreferred&&(t.push("bun"),n.push(o));const a=await(0,h.default)(e);a&&!t.includes(a.name)&&(t.push(a.name),n.push({isPreferred:!0,hasLockfile:!1}));let p=0;return n.forEach(e=>p+=e.hasLockfile?1:0),{name:t[0]||"npm",multipleLockFilesDetected:p>1}};const u=n(8962),c=a(n(3360)),l=o(n(6928)),h=a(n(8146)),p=n(1398);async function f(e){try{await p.workspace.fs.stat(p.Uri.file(e))}catch{return!1}return!0}},842:(e,t,n)=>{"use strict";n.r(t),n.d(t,{isexe:()=>m,posix:()=>r,sync:()=>g,win32:()=>i});var r={};n.r(r),n.d(r,{isexe:()=>a,sync:()=>u});var i={};n.r(i),n.d(i,{isexe:()=>h,sync:()=>p});var s=n(9896);const o=require("fs/promises"),a=async(e,t={})=>{const{ignoreErrors:n=!1}=t;try{return c(await(0,o.stat)(e),t)}catch(e){const t=e;if(n||"EACCES"===t.code)return!1;throw t}},u=(e,t={})=>{const{ignoreErrors:n=!1}=t;try{return c((0,s.statSync)(e),t)}catch(e){const t=e;if(n||"EACCES"===t.code)return!1;throw t}},c=(e,t)=>e.isFile()&&l(e,t),l=(e,t)=>{const n=t.uid??process.getuid?.(),r=t.groups??process.getgroups?.()??[],i=t.gid??process.getgid?.()??r[0];if(void 0===n||void 0===i)throw new Error("cannot get uid or gid");const s=new Set([i,...r]),o=e.mode,a=e.uid,u=e.gid,c=parseInt("100",8),l=parseInt("010",8),h=c|l;return!!(o&parseInt("001",8)||o&l&&s.has(u)||o&c&&a===n||o&h&&0===n)},h=async(e,t={})=>{const{ignoreErrors:n=!1}=t;try{return f(await(0,o.stat)(e),e,t)}catch(e){const t=e;if(n||"EACCES"===t.code)return!1;throw t}},p=(e,t={})=>{const{ignoreErrors:n=!1}=t;try{return f((0,s.statSync)(e),e,t)}catch(e){const t=e;if(n||"EACCES"===t.code)return!1;throw t}},f=(e,t,n)=>e.isFile()&&((e,t)=>{const{pathExt:n=process.env.PATHEXT||""}=t,r=n.split(";");if(-1!==r.indexOf(""))return!0;for(let t=0;t<r.length;t++){const n=r[t].toLowerCase(),i=e.substring(e.length-n.length).toLowerCase();if(n&&i===n)return!0}return!1})(t,n),d="win32"===(process.env._ISEXE_TEST_PLATFORM_||process.platform)?i:r,m=d.isexe,g=d.sync},919:(e,t,n)=>{"use strict";var r=n(1621);e.exports=new r({include:[n(5491)],implicit:[n(7933),n(1573)],explicit:[n(1072),n(3668),n(8480),n(733)]})},1072:(e,t,n)=>{"use strict";var r;try{r=n(181).Buffer}catch(e){}var i=n(6274),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";e.exports=new i("tag:yaml.org,2002:binary",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t,n,r=0,i=e.length,o=s;for(n=0;n<i;n++)if(!((t=o.indexOf(e.charAt(n)))>64)){if(t<0)return!1;r+=6}return r%8==0},construct:function(e){var t,n,i=e.replace(/[\r\n=]/g,""),o=i.length,a=s,u=0,c=[];for(t=0;t<o;t++)t%4==0&&t&&(c.push(u>>16&255),c.push(u>>8&255),c.push(255&u)),u=u<<6|a.indexOf(i.charAt(t));return 0==(n=o%4*6)?(c.push(u>>16&255),c.push(u>>8&255),c.push(255&u)):18===n?(c.push(u>>10&255),c.push(u>>2&255)):12===n&&c.push(u>>4&255),r?r.from?r.from(c):new r(c):c},predicate:function(e){return r&&r.isBuffer(e)},represent:function(e){var t,n,r="",i=0,o=e.length,a=s;for(t=0;t<o;t++)t%3==0&&t&&(r+=a[i>>18&63],r+=a[i>>12&63],r+=a[i>>6&63],r+=a[63&i]),i=(i<<8)+e[t];return 0==(n=o%3)?(r+=a[i>>18&63],r+=a[i>>12&63],r+=a[i>>6&63],r+=a[63&i]):2===n?(r+=a[i>>10&63],r+=a[i>>4&63],r+=a[i<<2&63],r+=a[64]):1===n&&(r+=a[i>>2&63],r+=a[i<<4&63],r+=a[64],r+=a[64]),r}})},1154:(e,t,n)=>{"use strict";const r=n(3940),i=n(280),{MAX_LENGTH:s,POSIX_REGEX_SOURCE:o,REGEX_NON_SPECIAL_CHARS:a,REGEX_SPECIAL_CHARS_BACKREF:u,REPLACEMENTS:c}=r,l=(e,t)=>{if("function"==typeof t.expandRange)return t.expandRange(...e,t);e.sort();const n=`[${e.join("-")}]`;try{new RegExp(n)}catch(t){return e.map(e=>i.escapeRegex(e)).join("..")}return n},h=(e,t)=>`Missing ${e}: "${t}" - use "\\\\${t}" to match literal characters`,p=(e,t)=>{if("string"!=typeof e)throw new TypeError("Expected a string");e=c[e]||e;const n={...t},f="number"==typeof n.maxLength?Math.min(s,n.maxLength):s;let d=e.length;if(d>f)throw new SyntaxError(`Input length: ${d}, exceeds maximum allowed length: ${f}`);const m={type:"bos",value:"",output:n.prepend||""},g=[m],x=n.capture?"":"?:",y=i.isWindows(t),v=r.globChars(y),E=r.extglobChars(v),{DOT_LITERAL:C,PLUS_LITERAL:D,SLASH_LITERAL:A,ONE_CHAR:S,DOTS_SLASH:b,NO_DOT:w,NO_DOT_SLASH:k,NO_DOTS_SLASH:F,QMARK:T,QMARK_NO_DOT:_,STAR:O,START_ANCHOR:P}=v,B=e=>`(${x}(?:(?!${P}${e.dot?b:C}).)*?)`,N=n.dot?"":w,I=n.dot?T:_;let R=!0===n.bash?B(n):O;n.capture&&(R=`(${R})`),"boolean"==typeof n.noext&&(n.noextglob=n.noext);const M={input:e,index:-1,start:0,dot:!0===n.dot,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:g};e=i.removePrefix(e,M),d=e.length;const L=[],j=[],U=[];let H,$=m;const J=()=>M.index===d-1,X=M.peek=(t=1)=>e[M.index+t],z=M.advance=()=>e[++M.index]||"",K=()=>e.slice(M.index+1),q=(e="",t=0)=>{M.consumed+=e,M.index+=t},G=e=>{M.output+=null!=e.output?e.output:e.value,q(e.value)},W=()=>{let e=1;for(;"!"===X()&&("("!==X(2)||"?"===X(3));)z(),M.start++,e++;return e%2!=0&&(M.negated=!0,M.start++,!0)},V=e=>{M[e]++,U.push(e)},Y=e=>{M[e]--,U.pop()},Q=e=>{if("globstar"===$.type){const t=M.braces>0&&("comma"===e.type||"brace"===e.type),n=!0===e.extglob||L.length&&("pipe"===e.type||"paren"===e.type);"slash"===e.type||"paren"===e.type||t||n||(M.output=M.output.slice(0,-$.output.length),$.type="star",$.value="*",$.output=R,M.output+=$.output)}if(L.length&&"paren"!==e.type&&(L[L.length-1].inner+=e.value),(e.value||e.output)&&G(e),$&&"text"===$.type&&"text"===e.type)return $.value+=e.value,void($.output=($.output||"")+e.value);e.prev=$,g.push(e),$=e},Z=(e,t)=>{const r={...E[t],conditions:1,inner:""};r.prev=$,r.parens=M.parens,r.output=M.output;const i=(n.capture?"(":"")+r.open;V("parens"),Q({type:e,value:t,output:M.output?"":S}),Q({type:"paren",extglob:!0,value:z(),output:i}),L.push(r)},ee=e=>{let r,i=e.close+(n.capture?")":"");if("negate"===e.type){let s=R;if(e.inner&&e.inner.length>1&&e.inner.includes("/")&&(s=B(n)),(s!==R||J()||/^\)+$/.test(K()))&&(i=e.close=`)$))${s}`),e.inner.includes("*")&&(r=K())&&/^\.[^\\/.]+$/.test(r)){const n=p(r,{...t,fastpaths:!1}).output;i=e.close=`)${n})${s})`}"bos"===e.prev.type&&(M.negatedExtglob=!0)}Q({type:"paren",extglob:!0,value:H,output:i}),Y("parens")};if(!1!==n.fastpaths&&!/(^[*!]|[/()[\]{}"])/.test(e)){let r=!1,s=e.replace(u,(e,t,n,i,s,o)=>"\\"===i?(r=!0,e):"?"===i?t?t+i+(s?T.repeat(s.length):""):0===o?I+(s?T.repeat(s.length):""):T.repeat(n.length):"."===i?C.repeat(n.length):"*"===i?t?t+i+(s?R:""):R:t?e:`\\${e}`);return!0===r&&(s=!0===n.unescape?s.replace(/\\/g,""):s.replace(/\\+/g,e=>e.length%2==0?"\\\\":e?"\\":"")),s===e&&!0===n.contains?(M.output=e,M):(M.output=i.wrapOutput(s,M,t),M)}for(;!J();){if(H=z(),"\0"===H)continue;if("\\"===H){const e=X();if("/"===e&&!0!==n.bash)continue;if("."===e||";"===e)continue;if(!e){H+="\\",Q({type:"text",value:H});continue}const t=/^\\+/.exec(K());let r=0;if(t&&t[0].length>2&&(r=t[0].length,M.index+=r,r%2!=0&&(H+="\\")),!0===n.unescape?H=z():H+=z(),0===M.brackets){Q({type:"text",value:H});continue}}if(M.brackets>0&&("]"!==H||"["===$.value||"[^"===$.value)){if(!1!==n.posix&&":"===H){const e=$.value.slice(1);if(e.includes("[")&&($.posix=!0,e.includes(":"))){const e=$.value.lastIndexOf("["),t=$.value.slice(0,e),n=$.value.slice(e+2),r=o[n];if(r){$.value=t+r,M.backtrack=!0,z(),m.output||1!==g.indexOf($)||(m.output=S);continue}}}("["===H&&":"!==X()||"-"===H&&"]"===X())&&(H=`\\${H}`),"]"!==H||"["!==$.value&&"[^"!==$.value||(H=`\\${H}`),!0===n.posix&&"!"===H&&"["===$.value&&(H="^"),$.value+=H,G({value:H});continue}if(1===M.quotes&&'"'!==H){H=i.escapeRegex(H),$.value+=H,G({value:H});continue}if('"'===H){M.quotes=1===M.quotes?0:1,!0===n.keepQuotes&&Q({type:"text",value:H});continue}if("("===H){V("parens"),Q({type:"paren",value:H});continue}if(")"===H){if(0===M.parens&&!0===n.strictBrackets)throw new SyntaxError(h("opening","("));const e=L[L.length-1];if(e&&M.parens===e.parens+1){ee(L.pop());continue}Q({type:"paren",value:H,output:M.parens?")":"\\)"}),Y("parens");continue}if("["===H){if(!0!==n.nobracket&&K().includes("]"))V("brackets");else{if(!0!==n.nobracket&&!0===n.strictBrackets)throw new SyntaxError(h("closing","]"));H=`\\${H}`}Q({type:"bracket",value:H});continue}if("]"===H){if(!0===n.nobracket||$&&"bracket"===$.type&&1===$.value.length){Q({type:"text",value:H,output:`\\${H}`});continue}if(0===M.brackets){if(!0===n.strictBrackets)throw new SyntaxError(h("opening","["));Q({type:"text",value:H,output:`\\${H}`});continue}Y("brackets");const e=$.value.slice(1);if(!0===$.posix||"^"!==e[0]||e.includes("/")||(H=`/${H}`),$.value+=H,G({value:H}),!1===n.literalBrackets||i.hasRegexChars(e))continue;const t=i.escapeRegex($.value);if(M.output=M.output.slice(0,-$.value.length),!0===n.literalBrackets){M.output+=t,$.value=t;continue}$.value=`(${x}${t}|${$.value})`,M.output+=$.value;continue}if("{"===H&&!0!==n.nobrace){V("braces");const e={type:"brace",value:H,output:"(",outputIndex:M.output.length,tokensIndex:M.tokens.length};j.push(e),Q(e);continue}if("}"===H){const e=j[j.length-1];if(!0===n.nobrace||!e){Q({type:"text",value:H,output:H});continue}let t=")";if(!0===e.dots){const e=g.slice(),r=[];for(let t=e.length-1;t>=0&&(g.pop(),"brace"!==e[t].type);t--)"dots"!==e[t].type&&r.unshift(e[t].value);t=l(r,n),M.backtrack=!0}if(!0!==e.comma&&!0!==e.dots){const n=M.output.slice(0,e.outputIndex),r=M.tokens.slice(e.tokensIndex);e.value=e.output="\\{",H=t="\\}",M.output=n;for(const e of r)M.output+=e.output||e.value}Q({type:"brace",value:H,output:t}),Y("braces"),j.pop();continue}if("|"===H){L.length>0&&L[L.length-1].conditions++,Q({type:"text",value:H});continue}if(","===H){let e=H;const t=j[j.length-1];t&&"braces"===U[U.length-1]&&(t.comma=!0,e="|"),Q({type:"comma",value:H,output:e});continue}if("/"===H){if("dot"===$.type&&M.index===M.start+1){M.start=M.index+1,M.consumed="",M.output="",g.pop(),$=m;continue}Q({type:"slash",value:H,output:A});continue}if("."===H){if(M.braces>0&&"dot"===$.type){"."===$.value&&($.output=C);const e=j[j.length-1];$.type="dots",$.output+=H,$.value+=H,e.dots=!0;continue}if(M.braces+M.parens===0&&"bos"!==$.type&&"slash"!==$.type){Q({type:"text",value:H,output:C});continue}Q({type:"dot",value:H,output:C});continue}if("?"===H){if((!$||"("!==$.value)&&!0!==n.noextglob&&"("===X()&&"?"!==X(2)){Z("qmark",H);continue}if($&&"paren"===$.type){const e=X();let t=H;if("<"===e&&!i.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");("("===$.value&&!/[!=<:]/.test(e)||"<"===e&&!/<([!=]|\w+>)/.test(K()))&&(t=`\\${H}`),Q({type:"text",value:H,output:t});continue}if(!0!==n.dot&&("slash"===$.type||"bos"===$.type)){Q({type:"qmark",value:H,output:_});continue}Q({type:"qmark",value:H,output:T});continue}if("!"===H){if(!0!==n.noextglob&&"("===X()&&("?"!==X(2)||!/[!=<:]/.test(X(3)))){Z("negate",H);continue}if(!0!==n.nonegate&&0===M.index){W();continue}}if("+"===H){if(!0!==n.noextglob&&"("===X()&&"?"!==X(2)){Z("plus",H);continue}if($&&"("===$.value||!1===n.regex){Q({type:"plus",value:H,output:D});continue}if($&&("bracket"===$.type||"paren"===$.type||"brace"===$.type)||M.parens>0){Q({type:"plus",value:H});continue}Q({type:"plus",value:D});continue}if("@"===H){if(!0!==n.noextglob&&"("===X()&&"?"!==X(2)){Q({type:"at",extglob:!0,value:H,output:""});continue}Q({type:"text",value:H});continue}if("*"!==H){"$"!==H&&"^"!==H||(H=`\\${H}`);const e=a.exec(K());e&&(H+=e[0],M.index+=e[0].length),Q({type:"text",value:H});continue}if($&&("globstar"===$.type||!0===$.star)){$.type="star",$.star=!0,$.value+=H,$.output=R,M.backtrack=!0,M.globstar=!0,q(H);continue}let t=K();if(!0!==n.noextglob&&/^\([^?]/.test(t)){Z("star",H);continue}if("star"===$.type){if(!0===n.noglobstar){q(H);continue}const r=$.prev,i=r.prev,s="slash"===r.type||"bos"===r.type,o=i&&("star"===i.type||"globstar"===i.type);if(!0===n.bash&&(!s||t[0]&&"/"!==t[0])){Q({type:"star",value:H,output:""});continue}const a=M.braces>0&&("comma"===r.type||"brace"===r.type),u=L.length&&("pipe"===r.type||"paren"===r.type);if(!s&&"paren"!==r.type&&!a&&!u){Q({type:"star",value:H,output:""});continue}for(;"/**"===t.slice(0,3);){const n=e[M.index+4];if(n&&"/"!==n)break;t=t.slice(3),q("/**",3)}if("bos"===r.type&&J()){$.type="globstar",$.value+=H,$.output=B(n),M.output=$.output,M.globstar=!0,q(H);continue}if("slash"===r.type&&"bos"!==r.prev.type&&!o&&J()){M.output=M.output.slice(0,-(r.output+$.output).length),r.output=`(?:${r.output}`,$.type="globstar",$.output=B(n)+(n.strictSlashes?")":"|$)"),$.value+=H,M.globstar=!0,M.output+=r.output+$.output,q(H);continue}if("slash"===r.type&&"bos"!==r.prev.type&&"/"===t[0]){const e=void 0!==t[1]?"|$":"";M.output=M.output.slice(0,-(r.output+$.output).length),r.output=`(?:${r.output}`,$.type="globstar",$.output=`${B(n)}${A}|${A}${e})`,$.value+=H,M.output+=r.output+$.output,M.globstar=!0,q(H+z()),Q({type:"slash",value:"/",output:""});continue}if("bos"===r.type&&"/"===t[0]){$.type="globstar",$.value+=H,$.output=`(?:^|${A}|${B(n)}${A})`,M.output=$.output,M.globstar=!0,q(H+z()),Q({type:"slash",value:"/",output:""});continue}M.output=M.output.slice(0,-$.output.length),$.type="globstar",$.output=B(n),$.value+=H,M.output+=$.output,M.globstar=!0,q(H);continue}const r={type:"star",value:H,output:R};!0!==n.bash?!$||"bracket"!==$.type&&"paren"!==$.type||!0!==n.regex?(M.index!==M.start&&"slash"!==$.type&&"dot"!==$.type||("dot"===$.type?(M.output+=k,$.output+=k):!0===n.dot?(M.output+=F,$.output+=F):(M.output+=N,$.output+=N),"*"!==X()&&(M.output+=S,$.output+=S)),Q(r)):(r.output=H,Q(r)):(r.output=".*?","bos"!==$.type&&"slash"!==$.type||(r.output=N+r.output),Q(r))}for(;M.brackets>0;){if(!0===n.strictBrackets)throw new SyntaxError(h("closing","]"));M.output=i.escapeLast(M.output,"["),Y("brackets")}for(;M.parens>0;){if(!0===n.strictBrackets)throw new SyntaxError(h("closing",")"));M.output=i.escapeLast(M.output,"("),Y("parens")}for(;M.braces>0;){if(!0===n.strictBrackets)throw new SyntaxError(h("closing","}"));M.output=i.escapeLast(M.output,"{"),Y("braces")}if(!0===n.strictSlashes||"star"!==$.type&&"bracket"!==$.type||Q({type:"maybe_slash",value:"",output:`${A}?`}),!0===M.backtrack){M.output="";for(const e of M.tokens)M.output+=null!=e.output?e.output:e.value,e.suffix&&(M.output+=e.suffix)}return M};p.fastpaths=(e,t)=>{const n={...t},o="number"==typeof n.maxLength?Math.min(s,n.maxLength):s,a=e.length;if(a>o)throw new SyntaxError(`Input length: ${a}, exceeds maximum allowed length: ${o}`);e=c[e]||e;const u=i.isWindows(t),{DOT_LITERAL:l,SLASH_LITERAL:h,ONE_CHAR:p,DOTS_SLASH:f,NO_DOT:d,NO_DOTS:m,NO_DOTS_SLASH:g,STAR:x,START_ANCHOR:y}=r.globChars(u),v=n.dot?m:d,E=n.dot?g:d,C=n.capture?"":"?:";let D=!0===n.bash?".*?":x;n.capture&&(D=`(${D})`);const A=e=>!0===e.noglobstar?D:`(${C}(?:(?!${y}${e.dot?f:l}).)*?)`,S=e=>{switch(e){case"*":return`${v}${p}${D}`;case".*":return`${l}${p}${D}`;case"*.*":return`${v}${D}${l}${p}${D}`;case"*/*":return`${v}${D}${h}${p}${E}${D}`;case"**":return v+A(n);case"**/*":return`(?:${v}${A(n)}${h})?${E}${p}${D}`;case"**/*.*":return`(?:${v}${A(n)}${h})?${E}${D}${l}${p}${D}`;case"**/.*":return`(?:${v}${A(n)}${h})?${l}${p}${D}`;default:{const t=/^(.*?)\.(\w+)$/.exec(e);if(!t)return;const n=S(t[1]);if(!n)return;return n+l+t[2]}}},b=i.removePrefix(e,{negated:!1,prefix:""});let w=S(b);return w&&!0!==n.strictSlashes&&(w+=`${h}?`),w},e.exports=p},1206:function(e){var t;t=function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={exports:{},id:r,loaded:!1};return e[r].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}return n.m=e,n.c=t,n.p="",n(0)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=n(3),s=n(8),o=n(15);function a(e,t,n){var o=null,a=function(e,t){n&&n(e,t),o&&o.visit(e,t)},u="function"==typeof n?a:null,c=!1;if(t){c="boolean"==typeof t.comment&&t.comment;var l="boolean"==typeof t.attachComment&&t.attachComment;(c||l)&&((o=new r.CommentHandler).attach=l,t.comment=!0,u=a)}var h,p=!1;t&&"string"==typeof t.sourceType&&(p="module"===t.sourceType),h=t&&"boolean"==typeof t.jsx&&t.jsx?new i.JSXParser(e,t,u):new s.Parser(e,t,u);var f=p?h.parseModule():h.parseScript();return c&&o&&(f.comments=o.comments),h.config.tokens&&(f.tokens=h.tokens),h.config.tolerant&&(f.errors=h.errorHandler.errors),f}t.parse=a,t.parseModule=function(e,t,n){var r=t||{};return r.sourceType="module",a(e,r,n)},t.parseScript=function(e,t,n){var r=t||{};return r.sourceType="script",a(e,r,n)},t.tokenize=function(e,t,n){var r,i=new o.Tokenizer(e,t);r=[];try{for(;;){var s=i.getNextToken();if(!s)break;n&&(s=n(s)),r.push(s)}}catch(e){i.errorHandler.tolerate(e)}return i.errorHandler.tolerant&&(r.errors=i.errors()),r};var u=n(2);t.Syntax=u.Syntax,t.version="4.0.1"},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(2),i=function(){function e(){this.attach=!1,this.comments=[],this.stack=[],this.leading=[],this.trailing=[]}return e.prototype.insertInnerComments=function(e,t){if(e.type===r.Syntax.BlockStatement&&0===e.body.length){for(var n=[],i=this.leading.length-1;i>=0;--i){var s=this.leading[i];t.end.offset>=s.start&&(n.unshift(s.comment),this.leading.splice(i,1),this.trailing.splice(i,1))}n.length&&(e.innerComments=n)}},e.prototype.findTrailingComments=function(e){var t=[];if(this.trailing.length>0){for(var n=this.trailing.length-1;n>=0;--n){var r=this.trailing[n];r.start>=e.end.offset&&t.unshift(r.comment)}return this.trailing.length=0,t}var i=this.stack[this.stack.length-1];if(i&&i.node.trailingComments){var s=i.node.trailingComments[0];s&&s.range[0]>=e.end.offset&&(t=i.node.trailingComments,delete i.node.trailingComments)}return t},e.prototype.findLeadingComments=function(e){for(var t,n=[];this.stack.length>0&&(s=this.stack[this.stack.length-1])&&s.start>=e.start.offset;)t=s.node,this.stack.pop();if(t){for(var r=(t.leadingComments?t.leadingComments.length:0)-1;r>=0;--r){var i=t.leadingComments[r];i.range[1]<=e.start.offset&&(n.unshift(i),t.leadingComments.splice(r,1))}return t.leadingComments&&0===t.leadingComments.length&&delete t.leadingComments,n}for(r=this.leading.length-1;r>=0;--r){var s;(s=this.leading[r]).start<=e.start.offset&&(n.unshift(s.comment),this.leading.splice(r,1))}return n},e.prototype.visitNode=function(e,t){if(!(e.type===r.Syntax.Program&&e.body.length>0)){this.insertInnerComments(e,t);var n=this.findTrailingComments(t),i=this.findLeadingComments(t);i.length>0&&(e.leadingComments=i),n.length>0&&(e.trailingComments=n),this.stack.push({node:e,start:t.start.offset})}},e.prototype.visitComment=function(e,t){var n="L"===e.type[0]?"Line":"Block",r={type:n,value:e.value};if(e.range&&(r.range=e.range),e.loc&&(r.loc=e.loc),this.comments.push(r),this.attach){var i={comment:{type:n,value:e.value,range:[t.start.offset,t.end.offset]},start:t.start.offset};e.loc&&(i.comment.loc=e.loc),e.type=n,this.leading.push(i),this.trailing.push(i)}},e.prototype.visit=function(e,t){"LineComment"===e.type||"BlockComment"===e.type?this.visitComment(e,t):this.attach&&this.visitNode(e,t)},e}();t.CommentHandler=i},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Syntax={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",AwaitExpression:"AwaitExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DoWhileStatement:"DoWhileStatement",DebuggerStatement:"DebuggerStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForOfStatement:"ForOfStatement",ForInStatement:"ForInStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",Program:"Program",Property:"Property",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchCase:"SwitchCase",SwitchStatement:"SwitchStatement",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"}},function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0});var s=n(4),o=n(5),a=n(6),u=n(7),c=n(8),l=n(13),h=n(14);function p(e){var t;switch(e.type){case a.JSXSyntax.JSXIdentifier:t=e.name;break;case a.JSXSyntax.JSXNamespacedName:var n=e;t=p(n.namespace)+":"+p(n.name);break;case a.JSXSyntax.JSXMemberExpression:var r=e;t=p(r.object)+"."+p(r.property)}return t}l.TokenName[100]="JSXIdentifier",l.TokenName[101]="JSXText";var f=function(e){function t(t,n,r){return e.call(this,t,n,r)||this}return i(t,e),t.prototype.parsePrimaryExpression=function(){return this.match("<")?this.parseJSXRoot():e.prototype.parsePrimaryExpression.call(this)},t.prototype.startJSX=function(){this.scanner.index=this.startMarker.index,this.scanner.lineNumber=this.startMarker.line,this.scanner.lineStart=this.startMarker.index-this.startMarker.column},t.prototype.finishJSX=function(){this.nextToken()},t.prototype.reenterJSX=function(){this.startJSX(),this.expectJSX("}"),this.config.tokens&&this.tokens.pop()},t.prototype.createJSXNode=function(){return this.collectComments(),{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},t.prototype.createJSXChildNode=function(){return{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},t.prototype.scanXHTMLEntity=function(e){for(var t="&",n=!0,r=!1,i=!1,o=!1;!this.scanner.eof()&&n&&!r;){var a=this.scanner.source[this.scanner.index];if(a===e)break;if(r=";"===a,t+=a,++this.scanner.index,!r)switch(t.length){case 2:i="#"===a;break;case 3:i&&(n=(o="x"===a)||s.Character.isDecimalDigit(a.charCodeAt(0)),i=i&&!o);break;default:n=(n=n&&!(i&&!s.Character.isDecimalDigit(a.charCodeAt(0))))&&!(o&&!s.Character.isHexDigit(a.charCodeAt(0)))}}if(n&&r&&t.length>2){var u=t.substr(1,t.length-2);i&&u.length>1?t=String.fromCharCode(parseInt(u.substr(1),10)):o&&u.length>2?t=String.fromCharCode(parseInt("0"+u.substr(1),16)):i||o||!h.XHTMLEntities[u]||(t=h.XHTMLEntities[u])}return t},t.prototype.lexJSX=function(){var e=this.scanner.source.charCodeAt(this.scanner.index);if(60===e||62===e||47===e||58===e||61===e||123===e||125===e)return{type:7,value:a=this.scanner.source[this.scanner.index++],lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index-1,end:this.scanner.index};if(34===e||39===e){for(var t=this.scanner.index,n=this.scanner.source[this.scanner.index++],r="";!this.scanner.eof()&&(u=this.scanner.source[this.scanner.index++])!==n;)r+="&"===u?this.scanXHTMLEntity(n):u;return{type:8,value:r,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:t,end:this.scanner.index}}if(46===e){var i=this.scanner.source.charCodeAt(this.scanner.index+1),o=this.scanner.source.charCodeAt(this.scanner.index+2),a=46===i&&46===o?"...":".";return t=this.scanner.index,this.scanner.index+=a.length,{type:7,value:a,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:t,end:this.scanner.index}}if(96===e)return{type:10,value:"",lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index,end:this.scanner.index};if(s.Character.isIdentifierStart(e)&&92!==e){for(t=this.scanner.index,++this.scanner.index;!this.scanner.eof();){var u=this.scanner.source.charCodeAt(this.scanner.index);if(s.Character.isIdentifierPart(u)&&92!==u)++this.scanner.index;else{if(45!==u)break;++this.scanner.index}}return{type:100,value:this.scanner.source.slice(t,this.scanner.index),lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:t,end:this.scanner.index}}return this.scanner.lex()},t.prototype.nextJSXToken=function(){this.collectComments(),this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;var e=this.lexJSX();return this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.config.tokens&&this.tokens.push(this.convertToken(e)),e},t.prototype.nextJSXText=function(){this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;for(var e=this.scanner.index,t="";!this.scanner.eof();){var n=this.scanner.source[this.scanner.index];if("{"===n||"<"===n)break;++this.scanner.index,t+=n,s.Character.isLineTerminator(n.charCodeAt(0))&&(++this.scanner.lineNumber,"\r"===n&&"\n"===this.scanner.source[this.scanner.index]&&++this.scanner.index,this.scanner.lineStart=this.scanner.index)}this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart;var r={type:101,value:t,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:e,end:this.scanner.index};return t.length>0&&this.config.tokens&&this.tokens.push(this.convertToken(r)),r},t.prototype.peekJSXToken=function(){var e=this.scanner.saveState();this.scanner.scanComments();var t=this.lexJSX();return this.scanner.restoreState(e),t},t.prototype.expectJSX=function(e){var t=this.nextJSXToken();7===t.type&&t.value===e||this.throwUnexpectedToken(t)},t.prototype.matchJSX=function(e){var t=this.peekJSXToken();return 7===t.type&&t.value===e},t.prototype.parseJSXIdentifier=function(){var e=this.createJSXNode(),t=this.nextJSXToken();return 100!==t.type&&this.throwUnexpectedToken(t),this.finalize(e,new o.JSXIdentifier(t.value))},t.prototype.parseJSXElementName=function(){var e=this.createJSXNode(),t=this.parseJSXIdentifier();if(this.matchJSX(":")){var n=t;this.expectJSX(":");var r=this.parseJSXIdentifier();t=this.finalize(e,new o.JSXNamespacedName(n,r))}else if(this.matchJSX("."))for(;this.matchJSX(".");){var i=t;this.expectJSX(".");var s=this.parseJSXIdentifier();t=this.finalize(e,new o.JSXMemberExpression(i,s))}return t},t.prototype.parseJSXAttributeName=function(){var e,t=this.createJSXNode(),n=this.parseJSXIdentifier();if(this.matchJSX(":")){var r=n;this.expectJSX(":");var i=this.parseJSXIdentifier();e=this.finalize(t,new o.JSXNamespacedName(r,i))}else e=n;return e},t.prototype.parseJSXStringLiteralAttribute=function(){var e=this.createJSXNode(),t=this.nextJSXToken();8!==t.type&&this.throwUnexpectedToken(t);var n=this.getTokenRaw(t);return this.finalize(e,new u.Literal(t.value,n))},t.prototype.parseJSXExpressionAttribute=function(){var e=this.createJSXNode();this.expectJSX("{"),this.finishJSX(),this.match("}")&&this.tolerateError("JSX attributes must only be assigned a non-empty expression");var t=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(e,new o.JSXExpressionContainer(t))},t.prototype.parseJSXAttributeValue=function(){return this.matchJSX("{")?this.parseJSXExpressionAttribute():this.matchJSX("<")?this.parseJSXElement():this.parseJSXStringLiteralAttribute()},t.prototype.parseJSXNameValueAttribute=function(){var e=this.createJSXNode(),t=this.parseJSXAttributeName(),n=null;return this.matchJSX("=")&&(this.expectJSX("="),n=this.parseJSXAttributeValue()),this.finalize(e,new o.JSXAttribute(t,n))},t.prototype.parseJSXSpreadAttribute=function(){var e=this.createJSXNode();this.expectJSX("{"),this.expectJSX("..."),this.finishJSX();var t=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(e,new o.JSXSpreadAttribute(t))},t.prototype.parseJSXAttributes=function(){for(var e=[];!this.matchJSX("/")&&!this.matchJSX(">");){var t=this.matchJSX("{")?this.parseJSXSpreadAttribute():this.parseJSXNameValueAttribute();e.push(t)}return e},t.prototype.parseJSXOpeningElement=function(){var e=this.createJSXNode();this.expectJSX("<");var t=this.parseJSXElementName(),n=this.parseJSXAttributes(),r=this.matchJSX("/");return r&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(e,new o.JSXOpeningElement(t,r,n))},t.prototype.parseJSXBoundaryElement=function(){var e=this.createJSXNode();if(this.expectJSX("<"),this.matchJSX("/")){this.expectJSX("/");var t=this.parseJSXElementName();return this.expectJSX(">"),this.finalize(e,new o.JSXClosingElement(t))}var n=this.parseJSXElementName(),r=this.parseJSXAttributes(),i=this.matchJSX("/");return i&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(e,new o.JSXOpeningElement(n,i,r))},t.prototype.parseJSXEmptyExpression=function(){var e=this.createJSXChildNode();return this.collectComments(),this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.finalize(e,new o.JSXEmptyExpression)},t.prototype.parseJSXExpressionContainer=function(){var e,t=this.createJSXNode();return this.expectJSX("{"),this.matchJSX("}")?(e=this.parseJSXEmptyExpression(),this.expectJSX("}")):(this.finishJSX(),e=this.parseAssignmentExpression(),this.reenterJSX()),this.finalize(t,new o.JSXExpressionContainer(e))},t.prototype.parseJSXChildren=function(){for(var e=[];!this.scanner.eof();){var t=this.createJSXChildNode(),n=this.nextJSXText();if(n.start<n.end){var r=this.getTokenRaw(n),i=this.finalize(t,new o.JSXText(n.value,r));e.push(i)}if("{"!==this.scanner.source[this.scanner.index])break;var s=this.parseJSXExpressionContainer();e.push(s)}return e},t.prototype.parseComplexJSXElement=function(e){for(var t=[];!this.scanner.eof();){e.children=e.children.concat(this.parseJSXChildren());var n=this.createJSXChildNode(),r=this.parseJSXBoundaryElement();if(r.type===a.JSXSyntax.JSXOpeningElement){var i=r;if(i.selfClosing){var s=this.finalize(n,new o.JSXElement(i,[],null));e.children.push(s)}else t.push(e),e={node:n,opening:i,closing:null,children:[]}}if(r.type===a.JSXSyntax.JSXClosingElement){e.closing=r;var u=p(e.opening.name);if(u!==p(e.closing.name)&&this.tolerateError("Expected corresponding JSX closing tag for %0",u),!(t.length>0))break;s=this.finalize(e.node,new o.JSXElement(e.opening,e.children,e.closing)),(e=t[t.length-1]).children.push(s),t.pop()}}return e},t.prototype.parseJSXElement=function(){var e=this.createJSXNode(),t=this.parseJSXOpeningElement(),n=[],r=null;if(!t.selfClosing){var i=this.parseComplexJSXElement({node:e,opening:t,closing:r,children:n});n=i.children,r=i.closing}return this.finalize(e,new o.JSXElement(t,n,r))},t.prototype.parseJSXRoot=function(){this.config.tokens&&this.tokens.pop(),this.startJSX();var e=this.parseJSXElement();return this.finishJSX(),e},t.prototype.isStartOfExpression=function(){return e.prototype.isStartOfExpression.call(this)||this.match("<")},t}(c.Parser);t.JSXParser=f},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFC-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C4\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/};t.Character={fromCodePoint:function(e){return e<65536?String.fromCharCode(e):String.fromCharCode(55296+(e-65536>>10))+String.fromCharCode(56320+(e-65536&1023))},isWhiteSpace:function(e){return 32===e||9===e||11===e||12===e||160===e||e>=5760&&[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(e)>=0},isLineTerminator:function(e){return 10===e||13===e||8232===e||8233===e},isIdentifierStart:function(e){return 36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||92===e||e>=128&&n.NonAsciiIdentifierStart.test(t.Character.fromCodePoint(e))},isIdentifierPart:function(e){return 36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57||92===e||e>=128&&n.NonAsciiIdentifierPart.test(t.Character.fromCodePoint(e))},isDecimalDigit:function(e){return e>=48&&e<=57},isHexDigit:function(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102},isOctalDigit:function(e){return e>=48&&e<=55}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(6);t.JSXClosingElement=function(e){this.type=r.JSXSyntax.JSXClosingElement,this.name=e};t.JSXElement=function(e,t,n){this.type=r.JSXSyntax.JSXElement,this.openingElement=e,this.children=t,this.closingElement=n};t.JSXEmptyExpression=function(){this.type=r.JSXSyntax.JSXEmptyExpression};t.JSXExpressionContainer=function(e){this.type=r.JSXSyntax.JSXExpressionContainer,this.expression=e};t.JSXIdentifier=function(e){this.type=r.JSXSyntax.JSXIdentifier,this.name=e};t.JSXMemberExpression=function(e,t){this.type=r.JSXSyntax.JSXMemberExpression,this.object=e,this.property=t};t.JSXAttribute=function(e,t){this.type=r.JSXSyntax.JSXAttribute,this.name=e,this.value=t};t.JSXNamespacedName=function(e,t){this.type=r.JSXSyntax.JSXNamespacedName,this.namespace=e,this.name=t};t.JSXOpeningElement=function(e,t,n){this.type=r.JSXSyntax.JSXOpeningElement,this.name=e,this.selfClosing=t,this.attributes=n};t.JSXSpreadAttribute=function(e){this.type=r.JSXSyntax.JSXSpreadAttribute,this.argument=e};t.JSXText=function(e,t){this.type=r.JSXSyntax.JSXText,this.value=e,this.raw=t}},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JSXSyntax={JSXAttribute:"JSXAttribute",JSXClosingElement:"JSXClosingElement",JSXElement:"JSXElement",JSXEmptyExpression:"JSXEmptyExpression",JSXExpressionContainer:"JSXExpressionContainer",JSXIdentifier:"JSXIdentifier",JSXMemberExpression:"JSXMemberExpression",JSXNamespacedName:"JSXNamespacedName",JSXOpeningElement:"JSXOpeningElement",JSXSpreadAttribute:"JSXSpreadAttribute",JSXText:"JSXText"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(2);t.ArrayExpression=function(e){this.type=r.Syntax.ArrayExpression,this.elements=e};t.ArrayPattern=function(e){this.type=r.Syntax.ArrayPattern,this.elements=e};t.ArrowFunctionExpression=function(e,t,n){this.type=r.Syntax.ArrowFunctionExpression,this.id=null,this.params=e,this.body=t,this.generator=!1,this.expression=n,this.async=!1};t.AssignmentExpression=function(e,t,n){this.type=r.Syntax.AssignmentExpression,this.operator=e,this.left=t,this.right=n};t.AssignmentPattern=function(e,t){this.type=r.Syntax.AssignmentPattern,this.left=e,this.right=t};t.AsyncArrowFunctionExpression=function(e,t,n){this.type=r.Syntax.ArrowFunctionExpression,this.id=null,this.params=e,this.body=t,this.generator=!1,this.expression=n,this.async=!0};t.AsyncFunctionDeclaration=function(e,t,n){this.type=r.Syntax.FunctionDeclaration,this.id=e,this.params=t,this.body=n,this.generator=!1,this.expression=!1,this.async=!0};t.AsyncFunctionExpression=function(e,t,n){this.type=r.Syntax.FunctionExpression,this.id=e,this.params=t,this.body=n,this.generator=!1,this.expression=!1,this.async=!0};t.AwaitExpression=function(e){this.type=r.Syntax.AwaitExpression,this.argument=e};t.BinaryExpression=function(e,t,n){var i="||"===e||"&&"===e;this.type=i?r.Syntax.LogicalExpression:r.Syntax.BinaryExpression,this.operator=e,this.left=t,this.right=n};t.BlockStatement=function(e){this.type=r.Syntax.BlockStatement,this.body=e};t.BreakStatement=function(e){this.type=r.Syntax.BreakStatement,this.label=e};t.CallExpression=function(e,t){this.type=r.Syntax.CallExpression,this.callee=e,this.arguments=t};t.CatchClause=function(e,t){this.type=r.Syntax.CatchClause,this.param=e,this.body=t};t.ClassBody=function(e){this.type=r.Syntax.ClassBody,this.body=e};t.ClassDeclaration=function(e,t,n){this.type=r.Syntax.ClassDeclaration,this.id=e,this.superClass=t,this.body=n};t.ClassExpression=function(e,t,n){this.type=r.Syntax.ClassExpression,this.id=e,this.superClass=t,this.body=n};t.ComputedMemberExpression=function(e,t){this.type=r.Syntax.MemberExpression,this.computed=!0,this.object=e,this.property=t};t.ConditionalExpression=function(e,t,n){this.type=r.Syntax.ConditionalExpression,this.test=e,this.consequent=t,this.alternate=n};t.ContinueStatement=function(e){this.type=r.Syntax.ContinueStatement,this.label=e};t.DebuggerStatement=function(){this.type=r.Syntax.DebuggerStatement};t.Directive=function(e,t){this.type=r.Syntax.ExpressionStatement,this.expression=e,this.directive=t};t.DoWhileStatement=function(e,t){this.type=r.Syntax.DoWhileStatement,this.body=e,this.test=t};t.EmptyStatement=function(){this.type=r.Syntax.EmptyStatement};t.ExportAllDeclaration=function(e){this.type=r.Syntax.ExportAllDeclaration,this.source=e};t.ExportDefaultDeclaration=function(e){this.type=r.Syntax.ExportDefaultDeclaration,this.declaration=e};t.ExportNamedDeclaration=function(e,t,n){this.type=r.Syntax.ExportNamedDeclaration,this.declaration=e,this.specifiers=t,this.source=n};t.ExportSpecifier=function(e,t){this.type=r.Syntax.ExportSpecifier,this.exported=t,this.local=e};t.ExpressionStatement=function(e){this.type=r.Syntax.ExpressionStatement,this.expression=e};t.ForInStatement=function(e,t,n){this.type=r.Syntax.ForInStatement,this.left=e,this.right=t,this.body=n,this.each=!1};t.ForOfStatement=function(e,t,n){this.type=r.Syntax.ForOfStatement,this.left=e,this.right=t,this.body=n};t.ForStatement=function(e,t,n,i){this.type=r.Syntax.ForStatement,this.init=e,this.test=t,this.update=n,this.body=i};t.FunctionDeclaration=function(e,t,n,i){this.type=r.Syntax.FunctionDeclaration,this.id=e,this.params=t,this.body=n,this.generator=i,this.expression=!1,this.async=!1};t.FunctionExpression=function(e,t,n,i){this.type=r.Syntax.FunctionExpression,this.id=e,this.params=t,this.body=n,this.generator=i,this.expression=!1,this.async=!1};t.Identifier=function(e){this.type=r.Syntax.Identifier,this.name=e};t.IfStatement=function(e,t,n){this.type=r.Syntax.IfStatement,this.test=e,this.consequent=t,this.alternate=n};t.ImportDeclaration=function(e,t){this.type=r.Syntax.ImportDeclaration,this.specifiers=e,this.source=t};t.ImportDefaultSpecifier=function(e){this.type=r.Syntax.ImportDefaultSpecifier,this.local=e};t.ImportNamespaceSpecifier=function(e){this.type=r.Syntax.ImportNamespaceSpecifier,this.local=e};t.ImportSpecifier=function(e,t){this.type=r.Syntax.ImportSpecifier,this.local=e,this.imported=t};t.LabeledStatement=function(e,t){this.type=r.Syntax.LabeledStatement,this.label=e,this.body=t};t.Literal=function(e,t){this.type=r.Syntax.Literal,this.value=e,this.raw=t};t.MetaProperty=function(e,t){this.type=r.Syntax.MetaProperty,this.meta=e,this.property=t};t.MethodDefinition=function(e,t,n,i,s){this.type=r.Syntax.MethodDefinition,this.key=e,this.computed=t,this.value=n,this.kind=i,this.static=s};t.Module=function(e){this.type=r.Syntax.Program,this.body=e,this.sourceType="module"};t.NewExpression=function(e,t){this.type=r.Syntax.NewExpression,this.callee=e,this.arguments=t};t.ObjectExpression=function(e){this.type=r.Syntax.ObjectExpression,this.properties=e};t.ObjectPattern=function(e){this.type=r.Syntax.ObjectPattern,this.properties=e};t.Property=function(e,t,n,i,s,o){this.type=r.Syntax.Property,this.key=t,this.computed=n,this.value=i,this.kind=e,this.method=s,this.shorthand=o};t.RegexLiteral=function(e,t,n,i){this.type=r.Syntax.Literal,this.value=e,this.raw=t,this.regex={pattern:n,flags:i}};t.RestElement=function(e){this.type=r.Syntax.RestElement,this.argument=e};t.ReturnStatement=function(e){this.type=r.Syntax.ReturnStatement,this.argument=e};t.Script=function(e){this.type=r.Syntax.Program,this.body=e,this.sourceType="script"};t.SequenceExpression=function(e){this.type=r.Syntax.SequenceExpression,this.expressions=e};t.SpreadElement=function(e){this.type=r.Syntax.SpreadElement,this.argument=e};t.StaticMemberExpression=function(e,t){this.type=r.Syntax.MemberExpression,this.computed=!1,this.object=e,this.property=t};t.Super=function(){this.type=r.Syntax.Super};t.SwitchCase=function(e,t){this.type=r.Syntax.SwitchCase,this.test=e,this.consequent=t};t.SwitchStatement=function(e,t){this.type=r.Syntax.SwitchStatement,this.discriminant=e,this.cases=t};t.TaggedTemplateExpression=function(e,t){this.type=r.Syntax.TaggedTemplateExpression,this.tag=e,this.quasi=t};t.TemplateElement=function(e,t){this.type=r.Syntax.TemplateElement,this.value=e,this.tail=t};t.TemplateLiteral=function(e,t){this.type=r.Syntax.TemplateLiteral,this.quasis=e,this.expressions=t};t.ThisExpression=function(){this.type=r.Syntax.ThisExpression};t.ThrowStatement=function(e){this.type=r.Syntax.ThrowStatement,this.argument=e};t.TryStatement=function(e,t,n){this.type=r.Syntax.TryStatement,this.block=e,this.handler=t,this.finalizer=n};t.UnaryExpression=function(e,t){this.type=r.Syntax.UnaryExpression,this.operator=e,this.argument=t,this.prefix=!0};t.UpdateExpression=function(e,t,n){this.type=r.Syntax.UpdateExpression,this.operator=e,this.argument=t,this.prefix=n};t.VariableDeclaration=function(e,t){this.type=r.Syntax.VariableDeclaration,this.declarations=e,this.kind=t};t.VariableDeclarator=function(e,t){this.type=r.Syntax.VariableDeclarator,this.id=e,this.init=t};t.WhileStatement=function(e,t){this.type=r.Syntax.WhileStatement,this.test=e,this.body=t};t.WithStatement=function(e,t){this.type=r.Syntax.WithStatement,this.object=e,this.body=t};t.YieldExpression=function(e,t){this.type=r.Syntax.YieldExpression,this.argument=e,this.delegate=t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(9),i=n(10),s=n(11),o=n(7),a=n(12),u=n(2),c=n(13),l="ArrowParameterPlaceHolder",h=function(){function e(e,t,n){void 0===t&&(t={}),this.config={range:"boolean"==typeof t.range&&t.range,loc:"boolean"==typeof t.loc&&t.loc,source:null,tokens:"boolean"==typeof t.tokens&&t.tokens,comment:"boolean"==typeof t.comment&&t.comment,tolerant:"boolean"==typeof t.tolerant&&t.tolerant},this.config.loc&&t.source&&null!==t.source&&(this.config.source=String(t.source)),this.delegate=n,this.errorHandler=new i.ErrorHandler,this.errorHandler.tolerant=this.config.tolerant,this.scanner=new a.Scanner(e,this.errorHandler),this.scanner.trackComment=this.config.comment,this.operatorPrecedence={")":0,";":0,",":0,"=":0,"]":0,"||":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"===":6,"!==":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":11,"/":11,"%":11},this.lookahead={type:2,value:"",lineNumber:this.scanner.lineNumber,lineStart:0,start:0,end:0},this.hasLineTerminator=!1,this.context={isModule:!1,await:!1,allowIn:!0,allowStrictDirective:!0,allowYield:!0,firstCoverInitializedNameError:null,isAssignmentTarget:!1,isBindingElement:!1,inFunctionBody:!1,inIteration:!1,inSwitch:!1,labelSet:{},strict:!1},this.tokens=[],this.startMarker={index:0,line:this.scanner.lineNumber,column:0},this.lastMarker={index:0,line:this.scanner.lineNumber,column:0},this.nextToken(),this.lastMarker={index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}return e.prototype.throwError=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=Array.prototype.slice.call(arguments,1),s=e.replace(/%(\d)/g,function(e,t){return r.assert(t<i.length,"Message reference must be in range"),i[t]}),o=this.lastMarker.index,a=this.lastMarker.line,u=this.lastMarker.column+1;throw this.errorHandler.createError(o,a,u,s)},e.prototype.tolerateError=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=Array.prototype.slice.call(arguments,1),s=e.replace(/%(\d)/g,function(e,t){return r.assert(t<i.length,"Message reference must be in range"),i[t]}),o=this.lastMarker.index,a=this.scanner.lineNumber,u=this.lastMarker.column+1;this.errorHandler.tolerateError(o,a,u,s)},e.prototype.unexpectedTokenError=function(e,t){var n,r=t||s.Messages.UnexpectedToken;if(e?(t||(r=2===e.type?s.Messages.UnexpectedEOS:3===e.type?s.Messages.UnexpectedIdentifier:6===e.type?s.Messages.UnexpectedNumber:8===e.type?s.Messages.UnexpectedString:10===e.type?s.Messages.UnexpectedTemplate:s.Messages.UnexpectedToken,4===e.type&&(this.scanner.isFutureReservedWord(e.value)?r=s.Messages.UnexpectedReserved:this.context.strict&&this.scanner.isStrictModeReservedWord(e.value)&&(r=s.Messages.StrictReservedWord))),n=e.value):n="ILLEGAL",r=r.replace("%0",n),e&&"number"==typeof e.lineNumber){var i=e.start,o=e.lineNumber,a=this.lastMarker.index-this.lastMarker.column,u=e.start-a+1;return this.errorHandler.createError(i,o,u,r)}return i=this.lastMarker.index,o=this.lastMarker.line,u=this.lastMarker.column+1,this.errorHandler.createError(i,o,u,r)},e.prototype.throwUnexpectedToken=function(e,t){throw this.unexpectedTokenError(e,t)},e.prototype.tolerateUnexpectedToken=function(e,t){this.errorHandler.tolerate(this.unexpectedTokenError(e,t))},e.prototype.collectComments=function(){if(this.config.comment){var e=this.scanner.scanComments();if(e.length>0&&this.delegate)for(var t=0;t<e.length;++t){var n=e[t],r=void 0;r={type:n.multiLine?"BlockComment":"LineComment",value:this.scanner.source.slice(n.slice[0],n.slice[1])},this.config.range&&(r.range=n.range),this.config.loc&&(r.loc=n.loc);var i={start:{line:n.loc.start.line,column:n.loc.start.column,offset:n.range[0]},end:{line:n.loc.end.line,column:n.loc.end.column,offset:n.range[1]}};this.delegate(r,i)}}else this.scanner.scanComments()},e.prototype.getTokenRaw=function(e){return this.scanner.source.slice(e.start,e.end)},e.prototype.convertToken=function(e){var t={type:c.TokenName[e.type],value:this.getTokenRaw(e)};if(this.config.range&&(t.range=[e.start,e.end]),this.config.loc&&(t.loc={start:{line:this.startMarker.line,column:this.startMarker.column},end:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}),9===e.type){var n=e.pattern,r=e.flags;t.regex={pattern:n,flags:r}}return t},e.prototype.nextToken=function(){var e=this.lookahead;this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.collectComments(),this.scanner.index!==this.startMarker.index&&(this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart);var t=this.scanner.lex();return this.hasLineTerminator=e.lineNumber!==t.lineNumber,t&&this.context.strict&&3===t.type&&this.scanner.isStrictModeReservedWord(t.value)&&(t.type=4),this.lookahead=t,this.config.tokens&&2!==t.type&&this.tokens.push(this.convertToken(t)),e},e.prototype.nextRegexToken=function(){this.collectComments();var e=this.scanner.scanRegExp();return this.config.tokens&&(this.tokens.pop(),this.tokens.push(this.convertToken(e))),this.lookahead=e,this.nextToken(),e},e.prototype.createNode=function(){return{index:this.startMarker.index,line:this.startMarker.line,column:this.startMarker.column}},e.prototype.startNode=function(e,t){void 0===t&&(t=0);var n=e.start-e.lineStart,r=e.lineNumber;return n<0&&(n+=t,r--),{index:e.start,line:r,column:n}},e.prototype.finalize=function(e,t){if(this.config.range&&(t.range=[e.index,this.lastMarker.index]),this.config.loc&&(t.loc={start:{line:e.line,column:e.column},end:{line:this.lastMarker.line,column:this.lastMarker.column}},this.config.source&&(t.loc.source=this.config.source)),this.delegate){var n={start:{line:e.line,column:e.column,offset:e.index},end:{line:this.lastMarker.line,column:this.lastMarker.column,offset:this.lastMarker.index}};this.delegate(t,n)}return t},e.prototype.expect=function(e){var t=this.nextToken();7===t.type&&t.value===e||this.throwUnexpectedToken(t)},e.prototype.expectCommaSeparator=function(){if(this.config.tolerant){var e=this.lookahead;7===e.type&&","===e.value?this.nextToken():7===e.type&&";"===e.value?(this.nextToken(),this.tolerateUnexpectedToken(e)):this.tolerateUnexpectedToken(e,s.Messages.UnexpectedToken)}else this.expect(",")},e.prototype.expectKeyword=function(e){var t=this.nextToken();4===t.type&&t.value===e||this.throwUnexpectedToken(t)},e.prototype.match=function(e){return 7===this.lookahead.type&&this.lookahead.value===e},e.prototype.matchKeyword=function(e){return 4===this.lookahead.type&&this.lookahead.value===e},e.prototype.matchContextualKeyword=function(e){return 3===this.lookahead.type&&this.lookahead.value===e},e.prototype.matchAssign=function(){if(7!==this.lookahead.type)return!1;var e=this.lookahead.value;return"="===e||"*="===e||"**="===e||"/="===e||"%="===e||"+="===e||"-="===e||"<<="===e||">>="===e||">>>="===e||"&="===e||"^="===e||"|="===e},e.prototype.isolateCoverGrammar=function(e){var t=this.context.isBindingElement,n=this.context.isAssignmentTarget,r=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var i=e.call(this);return null!==this.context.firstCoverInitializedNameError&&this.throwUnexpectedToken(this.context.firstCoverInitializedNameError),this.context.isBindingElement=t,this.context.isAssignmentTarget=n,this.context.firstCoverInitializedNameError=r,i},e.prototype.inheritCoverGrammar=function(e){var t=this.context.isBindingElement,n=this.context.isAssignmentTarget,r=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var i=e.call(this);return this.context.isBindingElement=this.context.isBindingElement&&t,this.context.isAssignmentTarget=this.context.isAssignmentTarget&&n,this.context.firstCoverInitializedNameError=r||this.context.firstCoverInitializedNameError,i},e.prototype.consumeSemicolon=function(){this.match(";")?this.nextToken():this.hasLineTerminator||(2===this.lookahead.type||this.match("}")||this.throwUnexpectedToken(this.lookahead),this.lastMarker.index=this.startMarker.index,this.lastMarker.line=this.startMarker.line,this.lastMarker.column=this.startMarker.column)},e.prototype.parsePrimaryExpression=function(){var e,t,n,r=this.createNode();switch(this.lookahead.type){case 3:(this.context.isModule||this.context.await)&&"await"===this.lookahead.value&&this.tolerateUnexpectedToken(this.lookahead),e=this.matchAsyncFunction()?this.parseFunctionExpression():this.finalize(r,new o.Identifier(this.nextToken().value));break;case 6:case 8:this.context.strict&&this.lookahead.octal&&this.tolerateUnexpectedToken(this.lookahead,s.Messages.StrictOctalLiteral),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,t=this.nextToken(),n=this.getTokenRaw(t),e=this.finalize(r,new o.Literal(t.value,n));break;case 1:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,t=this.nextToken(),n=this.getTokenRaw(t),e=this.finalize(r,new o.Literal("true"===t.value,n));break;case 5:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,t=this.nextToken(),n=this.getTokenRaw(t),e=this.finalize(r,new o.Literal(null,n));break;case 10:e=this.parseTemplateLiteral();break;case 7:switch(this.lookahead.value){case"(":this.context.isBindingElement=!1,e=this.inheritCoverGrammar(this.parseGroupExpression);break;case"[":e=this.inheritCoverGrammar(this.parseArrayInitializer);break;case"{":e=this.inheritCoverGrammar(this.parseObjectInitializer);break;case"/":case"/=":this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.scanner.index=this.startMarker.index,t=this.nextRegexToken(),n=this.getTokenRaw(t),e=this.finalize(r,new o.RegexLiteral(t.regex,n,t.pattern,t.flags));break;default:e=this.throwUnexpectedToken(this.nextToken())}break;case 4:!this.context.strict&&this.context.allowYield&&this.matchKeyword("yield")?e=this.parseIdentifierName():!this.context.strict&&this.matchKeyword("let")?e=this.finalize(r,new o.Identifier(this.nextToken().value)):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.matchKeyword("function")?e=this.parseFunctionExpression():this.matchKeyword("this")?(this.nextToken(),e=this.finalize(r,new o.ThisExpression)):e=this.matchKeyword("class")?this.parseClassExpression():this.throwUnexpectedToken(this.nextToken()));break;default:e=this.throwUnexpectedToken(this.nextToken())}return e},e.prototype.parseSpreadElement=function(){var e=this.createNode();this.expect("...");var t=this.inheritCoverGrammar(this.parseAssignmentExpression);return this.finalize(e,new o.SpreadElement(t))},e.prototype.parseArrayInitializer=function(){var e=this.createNode(),t=[];for(this.expect("[");!this.match("]");)if(this.match(","))this.nextToken(),t.push(null);else if(this.match("...")){var n=this.parseSpreadElement();this.match("]")||(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.expect(",")),t.push(n)}else t.push(this.inheritCoverGrammar(this.parseAssignmentExpression)),this.match("]")||this.expect(",");return this.expect("]"),this.finalize(e,new o.ArrayExpression(t))},e.prototype.parsePropertyMethod=function(e){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var t=this.context.strict,n=this.context.allowStrictDirective;this.context.allowStrictDirective=e.simple;var r=this.isolateCoverGrammar(this.parseFunctionSourceElements);return this.context.strict&&e.firstRestricted&&this.tolerateUnexpectedToken(e.firstRestricted,e.message),this.context.strict&&e.stricted&&this.tolerateUnexpectedToken(e.stricted,e.message),this.context.strict=t,this.context.allowStrictDirective=n,r},e.prototype.parsePropertyMethodFunction=function(){var e=this.createNode(),t=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters(),r=this.parsePropertyMethod(n);return this.context.allowYield=t,this.finalize(e,new o.FunctionExpression(null,n.params,r,!1))},e.prototype.parsePropertyMethodAsyncFunction=function(){var e=this.createNode(),t=this.context.allowYield,n=this.context.await;this.context.allowYield=!1,this.context.await=!0;var r=this.parseFormalParameters(),i=this.parsePropertyMethod(r);return this.context.allowYield=t,this.context.await=n,this.finalize(e,new o.AsyncFunctionExpression(null,r.params,i))},e.prototype.parseObjectPropertyKey=function(){var e,t=this.createNode(),n=this.nextToken();switch(n.type){case 8:case 6:this.context.strict&&n.octal&&this.tolerateUnexpectedToken(n,s.Messages.StrictOctalLiteral);var r=this.getTokenRaw(n);e=this.finalize(t,new o.Literal(n.value,r));break;case 3:case 1:case 5:case 4:e=this.finalize(t,new o.Identifier(n.value));break;case 7:"["===n.value?(e=this.isolateCoverGrammar(this.parseAssignmentExpression),this.expect("]")):e=this.throwUnexpectedToken(n);break;default:e=this.throwUnexpectedToken(n)}return e},e.prototype.isPropertyKey=function(e,t){return e.type===u.Syntax.Identifier&&e.name===t||e.type===u.Syntax.Literal&&e.value===t},e.prototype.parseObjectProperty=function(e){var t,n=this.createNode(),r=this.lookahead,i=null,a=null,u=!1,c=!1,l=!1,h=!1;if(3===r.type){var p=r.value;this.nextToken(),u=this.match("["),i=(h=!(this.hasLineTerminator||"async"!==p||this.match(":")||this.match("(")||this.match("*")||this.match(",")))?this.parseObjectPropertyKey():this.finalize(n,new o.Identifier(p))}else this.match("*")?this.nextToken():(u=this.match("["),i=this.parseObjectPropertyKey());var f=this.qualifiedPropertyName(this.lookahead);if(3===r.type&&!h&&"get"===r.value&&f)t="get",u=this.match("["),i=this.parseObjectPropertyKey(),this.context.allowYield=!1,a=this.parseGetterMethod();else if(3===r.type&&!h&&"set"===r.value&&f)t="set",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseSetterMethod();else if(7===r.type&&"*"===r.value&&f)t="init",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseGeneratorMethod(),c=!0;else if(i||this.throwUnexpectedToken(this.lookahead),t="init",this.match(":")&&!h)!u&&this.isPropertyKey(i,"__proto__")&&(e.value&&this.tolerateError(s.Messages.DuplicateProtoProperty),e.value=!0),this.nextToken(),a=this.inheritCoverGrammar(this.parseAssignmentExpression);else if(this.match("("))a=h?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0;else if(3===r.type)if(p=this.finalize(n,new o.Identifier(r.value)),this.match("=")){this.context.firstCoverInitializedNameError=this.lookahead,this.nextToken(),l=!0;var d=this.isolateCoverGrammar(this.parseAssignmentExpression);a=this.finalize(n,new o.AssignmentPattern(p,d))}else l=!0,a=p;else this.throwUnexpectedToken(this.nextToken());return this.finalize(n,new o.Property(t,i,u,a,c,l))},e.prototype.parseObjectInitializer=function(){var e=this.createNode();this.expect("{");for(var t=[],n={value:!1};!this.match("}");)t.push(this.parseObjectProperty(n)),this.match("}")||this.expectCommaSeparator();return this.expect("}"),this.finalize(e,new o.ObjectExpression(t))},e.prototype.parseTemplateHead=function(){r.assert(this.lookahead.head,"Template literal must start with a template head");var e=this.createNode(),t=this.nextToken(),n=t.value,i=t.cooked;return this.finalize(e,new o.TemplateElement({raw:n,cooked:i},t.tail))},e.prototype.parseTemplateElement=function(){10!==this.lookahead.type&&this.throwUnexpectedToken();var e=this.createNode(),t=this.nextToken(),n=t.value,r=t.cooked;return this.finalize(e,new o.TemplateElement({raw:n,cooked:r},t.tail))},e.prototype.parseTemplateLiteral=function(){var e=this.createNode(),t=[],n=[],r=this.parseTemplateHead();for(n.push(r);!r.tail;)t.push(this.parseExpression()),r=this.parseTemplateElement(),n.push(r);return this.finalize(e,new o.TemplateLiteral(n,t))},e.prototype.reinterpretExpressionAsPattern=function(e){switch(e.type){case u.Syntax.Identifier:case u.Syntax.MemberExpression:case u.Syntax.RestElement:case u.Syntax.AssignmentPattern:break;case u.Syntax.SpreadElement:e.type=u.Syntax.RestElement,this.reinterpretExpressionAsPattern(e.argument);break;case u.Syntax.ArrayExpression:e.type=u.Syntax.ArrayPattern;for(var t=0;t<e.elements.length;t++)null!==e.elements[t]&&this.reinterpretExpressionAsPattern(e.elements[t]);break;case u.Syntax.ObjectExpression:for(e.type=u.Syntax.ObjectPattern,t=0;t<e.properties.length;t++)this.reinterpretExpressionAsPattern(e.properties[t].value);break;case u.Syntax.AssignmentExpression:e.type=u.Syntax.AssignmentPattern,delete e.operator,this.reinterpretExpressionAsPattern(e.left)}},e.prototype.parseGroupExpression=function(){var e;if(this.expect("("),this.match(")"))this.nextToken(),this.match("=>")||this.expect("=>"),e={type:l,params:[],async:!1};else{var t=this.lookahead,n=[];if(this.match("..."))e=this.parseRestElement(n),this.expect(")"),this.match("=>")||this.expect("=>"),e={type:l,params:[e],async:!1};else{var r=!1;if(this.context.isBindingElement=!0,e=this.inheritCoverGrammar(this.parseAssignmentExpression),this.match(",")){var i=[];for(this.context.isAssignmentTarget=!1,i.push(e);2!==this.lookahead.type&&this.match(",");){if(this.nextToken(),this.match(")")){this.nextToken();for(var s=0;s<i.length;s++)this.reinterpretExpressionAsPattern(i[s]);r=!0,e={type:l,params:i,async:!1}}else if(this.match("...")){for(this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),i.push(this.parseRestElement(n)),this.expect(")"),this.match("=>")||this.expect("=>"),this.context.isBindingElement=!1,s=0;s<i.length;s++)this.reinterpretExpressionAsPattern(i[s]);r=!0,e={type:l,params:i,async:!1}}else i.push(this.inheritCoverGrammar(this.parseAssignmentExpression));if(r)break}r||(e=this.finalize(this.startNode(t),new o.SequenceExpression(i)))}if(!r){if(this.expect(")"),this.match("=>")&&(e.type===u.Syntax.Identifier&&"yield"===e.name&&(r=!0,e={type:l,params:[e],async:!1}),!r)){if(this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),e.type===u.Syntax.SequenceExpression)for(s=0;s<e.expressions.length;s++)this.reinterpretExpressionAsPattern(e.expressions[s]);else this.reinterpretExpressionAsPattern(e);var a=e.type===u.Syntax.SequenceExpression?e.expressions:[e];e={type:l,params:a,async:!1}}this.context.isBindingElement=!1}}}return e},e.prototype.parseArguments=function(){this.expect("(");var e=[];if(!this.match(")"))for(;;){var t=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAssignmentExpression);if(e.push(t),this.match(")"))break;if(this.expectCommaSeparator(),this.match(")"))break}return this.expect(")"),e},e.prototype.isIdentifierName=function(e){return 3===e.type||4===e.type||1===e.type||5===e.type},e.prototype.parseIdentifierName=function(){var e=this.createNode(),t=this.nextToken();return this.isIdentifierName(t)||this.throwUnexpectedToken(t),this.finalize(e,new o.Identifier(t.value))},e.prototype.parseNewExpression=function(){var e,t=this.createNode(),n=this.parseIdentifierName();if(r.assert("new"===n.name,"New expression must start with `new`"),this.match("."))if(this.nextToken(),3===this.lookahead.type&&this.context.inFunctionBody&&"target"===this.lookahead.value){var i=this.parseIdentifierName();e=new o.MetaProperty(n,i)}else this.throwUnexpectedToken(this.lookahead);else{var s=this.isolateCoverGrammar(this.parseLeftHandSideExpression),a=this.match("(")?this.parseArguments():[];e=new o.NewExpression(s,a),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return this.finalize(t,e)},e.prototype.parseAsyncArgument=function(){var e=this.parseAssignmentExpression();return this.context.firstCoverInitializedNameError=null,e},e.prototype.parseAsyncArguments=function(){this.expect("(");var e=[];if(!this.match(")"))for(;;){var t=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAsyncArgument);if(e.push(t),this.match(")"))break;if(this.expectCommaSeparator(),this.match(")"))break}return this.expect(")"),e},e.prototype.parseLeftHandSideExpressionAllowCall=function(){var e,t=this.lookahead,n=this.matchContextualKeyword("async"),r=this.context.allowIn;for(this.context.allowIn=!0,this.matchKeyword("super")&&this.context.inFunctionBody?(e=this.createNode(),this.nextToken(),e=this.finalize(e,new o.Super),this.match("(")||this.match(".")||this.match("[")||this.throwUnexpectedToken(this.lookahead)):e=this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var i=this.parseIdentifierName();e=this.finalize(this.startNode(t),new o.StaticMemberExpression(e,i))}else if(this.match("(")){var s=n&&t.lineNumber===this.lookahead.lineNumber;this.context.isBindingElement=!1,this.context.isAssignmentTarget=!1;var a=s?this.parseAsyncArguments():this.parseArguments();if(e=this.finalize(this.startNode(t),new o.CallExpression(e,a)),s&&this.match("=>")){for(var u=0;u<a.length;++u)this.reinterpretExpressionAsPattern(a[u]);e={type:l,params:a,async:!0}}}else if(this.match("["))this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("["),i=this.isolateCoverGrammar(this.parseExpression),this.expect("]"),e=this.finalize(this.startNode(t),new o.ComputedMemberExpression(e,i));else{if(10!==this.lookahead.type||!this.lookahead.head)break;var c=this.parseTemplateLiteral();e=this.finalize(this.startNode(t),new o.TaggedTemplateExpression(e,c))}return this.context.allowIn=r,e},e.prototype.parseSuper=function(){var e=this.createNode();return this.expectKeyword("super"),this.match("[")||this.match(".")||this.throwUnexpectedToken(this.lookahead),this.finalize(e,new o.Super)},e.prototype.parseLeftHandSideExpression=function(){r.assert(this.context.allowIn,"callee of new expression always allow in keyword.");for(var e=this.startNode(this.lookahead),t=this.matchKeyword("super")&&this.context.inFunctionBody?this.parseSuper():this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var n=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),t=this.finalize(e,new o.ComputedMemberExpression(t,n))}else if(this.match("."))this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("."),n=this.parseIdentifierName(),t=this.finalize(e,new o.StaticMemberExpression(t,n));else{if(10!==this.lookahead.type||!this.lookahead.head)break;var i=this.parseTemplateLiteral();t=this.finalize(e,new o.TaggedTemplateExpression(t,i))}return t},e.prototype.parseUpdateExpression=function(){var e,t=this.lookahead;if(this.match("++")||this.match("--")){var n=this.startNode(t),r=this.nextToken();e=this.inheritCoverGrammar(this.parseUnaryExpression),this.context.strict&&e.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(e.name)&&this.tolerateError(s.Messages.StrictLHSPrefix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment);var i=!0;e=this.finalize(n,new o.UpdateExpression(r.value,e,i)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else if(e=this.inheritCoverGrammar(this.parseLeftHandSideExpressionAllowCall),!this.hasLineTerminator&&7===this.lookahead.type&&(this.match("++")||this.match("--"))){this.context.strict&&e.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(e.name)&&this.tolerateError(s.Messages.StrictLHSPostfix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var a=this.nextToken().value;i=!1,e=this.finalize(this.startNode(t),new o.UpdateExpression(a,e,i))}return e},e.prototype.parseAwaitExpression=function(){var e=this.createNode();this.nextToken();var t=this.parseUnaryExpression();return this.finalize(e,new o.AwaitExpression(t))},e.prototype.parseUnaryExpression=function(){var e;if(this.match("+")||this.match("-")||this.match("~")||this.match("!")||this.matchKeyword("delete")||this.matchKeyword("void")||this.matchKeyword("typeof")){var t=this.startNode(this.lookahead),n=this.nextToken();e=this.inheritCoverGrammar(this.parseUnaryExpression),e=this.finalize(t,new o.UnaryExpression(n.value,e)),this.context.strict&&"delete"===e.operator&&e.argument.type===u.Syntax.Identifier&&this.tolerateError(s.Messages.StrictDelete),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else e=this.context.await&&this.matchContextualKeyword("await")?this.parseAwaitExpression():this.parseUpdateExpression();return e},e.prototype.parseExponentiationExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseUnaryExpression);if(t.type!==u.Syntax.UnaryExpression&&this.match("**")){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var n=t,r=this.isolateCoverGrammar(this.parseExponentiationExpression);t=this.finalize(this.startNode(e),new o.BinaryExpression("**",n,r))}return t},e.prototype.binaryPrecedence=function(e){var t=e.value;return 7===e.type?this.operatorPrecedence[t]||0:4===e.type&&("instanceof"===t||this.context.allowIn&&"in"===t)?7:0},e.prototype.parseBinaryExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseExponentiationExpression),n=this.lookahead,r=this.binaryPrecedence(n);if(r>0){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;for(var i=[e,this.lookahead],s=t,a=this.isolateCoverGrammar(this.parseExponentiationExpression),u=[s,n.value,a],c=[r];!((r=this.binaryPrecedence(this.lookahead))<=0);){for(;u.length>2&&r<=c[c.length-1];){a=u.pop();var l=u.pop();c.pop(),s=u.pop(),i.pop();var h=this.startNode(i[i.length-1]);u.push(this.finalize(h,new o.BinaryExpression(l,s,a)))}u.push(this.nextToken().value),c.push(r),i.push(this.lookahead),u.push(this.isolateCoverGrammar(this.parseExponentiationExpression))}var p=u.length-1;t=u[p];for(var f=i.pop();p>1;){var d=i.pop(),m=f&&f.lineStart;h=this.startNode(d,m),l=u[p-1],t=this.finalize(h,new o.BinaryExpression(l,u[p-2],t)),p-=2,f=d}}return t},e.prototype.parseConditionalExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseBinaryExpression);if(this.match("?")){this.nextToken();var n=this.context.allowIn;this.context.allowIn=!0;var r=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowIn=n,this.expect(":");var i=this.isolateCoverGrammar(this.parseAssignmentExpression);t=this.finalize(this.startNode(e),new o.ConditionalExpression(t,r,i)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return t},e.prototype.checkPatternParam=function(e,t){switch(t.type){case u.Syntax.Identifier:this.validateParam(e,t,t.name);break;case u.Syntax.RestElement:this.checkPatternParam(e,t.argument);break;case u.Syntax.AssignmentPattern:this.checkPatternParam(e,t.left);break;case u.Syntax.ArrayPattern:for(var n=0;n<t.elements.length;n++)null!==t.elements[n]&&this.checkPatternParam(e,t.elements[n]);break;case u.Syntax.ObjectPattern:for(n=0;n<t.properties.length;n++)this.checkPatternParam(e,t.properties[n].value)}e.simple=e.simple&&t instanceof o.Identifier},e.prototype.reinterpretAsCoverFormalsList=function(e){var t,n=[e],r=!1;switch(e.type){case u.Syntax.Identifier:break;case l:n=e.params,r=e.async;break;default:return null}t={simple:!0,paramSet:{}};for(var i=0;i<n.length;++i)(o=n[i]).type===u.Syntax.AssignmentPattern?o.right.type===u.Syntax.YieldExpression&&(o.right.argument&&this.throwUnexpectedToken(this.lookahead),o.right.type=u.Syntax.Identifier,o.right.name="yield",delete o.right.argument,delete o.right.delegate):r&&o.type===u.Syntax.Identifier&&"await"===o.name&&this.throwUnexpectedToken(this.lookahead),this.checkPatternParam(t,o),n[i]=o;if(this.context.strict||!this.context.allowYield)for(i=0;i<n.length;++i){var o;(o=n[i]).type===u.Syntax.YieldExpression&&this.throwUnexpectedToken(this.lookahead)}if(t.message===s.Messages.StrictParamDupe){var a=this.context.strict?t.stricted:t.firstRestricted;this.throwUnexpectedToken(a,t.message)}return{simple:t.simple,params:n,stricted:t.stricted,firstRestricted:t.firstRestricted,message:t.message}},e.prototype.parseAssignmentExpression=function(){var e;if(!this.context.allowYield&&this.matchKeyword("yield"))e=this.parseYieldExpression();else{var t=this.lookahead,n=t;if(e=this.parseConditionalExpression(),3===n.type&&n.lineNumber===this.lookahead.lineNumber&&"async"===n.value&&(3===this.lookahead.type||this.matchKeyword("yield"))){var r=this.parsePrimaryExpression();this.reinterpretExpressionAsPattern(r),e={type:l,params:[r],async:!0}}if(e.type===l||this.match("=>")){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var i=e.async,a=this.reinterpretAsCoverFormalsList(e);if(a){this.hasLineTerminator&&this.tolerateUnexpectedToken(this.lookahead),this.context.firstCoverInitializedNameError=null;var c=this.context.strict,h=this.context.allowStrictDirective;this.context.allowStrictDirective=a.simple;var p=this.context.allowYield,f=this.context.await;this.context.allowYield=!0,this.context.await=i;var d=this.startNode(t);this.expect("=>");var m=void 0;if(this.match("{")){var g=this.context.allowIn;this.context.allowIn=!0,m=this.parseFunctionSourceElements(),this.context.allowIn=g}else m=this.isolateCoverGrammar(this.parseAssignmentExpression);var x=m.type!==u.Syntax.BlockStatement;this.context.strict&&a.firstRestricted&&this.throwUnexpectedToken(a.firstRestricted,a.message),this.context.strict&&a.stricted&&this.tolerateUnexpectedToken(a.stricted,a.message),e=i?this.finalize(d,new o.AsyncArrowFunctionExpression(a.params,m,x)):this.finalize(d,new o.ArrowFunctionExpression(a.params,m,x)),this.context.strict=c,this.context.allowStrictDirective=h,this.context.allowYield=p,this.context.await=f}}else if(this.matchAssign()){if(this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.strict&&e.type===u.Syntax.Identifier){var y=e;this.scanner.isRestrictedWord(y.name)&&this.tolerateUnexpectedToken(n,s.Messages.StrictLHSAssignment),this.scanner.isStrictModeReservedWord(y.name)&&this.tolerateUnexpectedToken(n,s.Messages.StrictReservedWord)}this.match("=")?this.reinterpretExpressionAsPattern(e):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1);var v=(n=this.nextToken()).value,E=this.isolateCoverGrammar(this.parseAssignmentExpression);e=this.finalize(this.startNode(t),new o.AssignmentExpression(v,e,E)),this.context.firstCoverInitializedNameError=null}}return e},e.prototype.parseExpression=function(){var e=this.lookahead,t=this.isolateCoverGrammar(this.parseAssignmentExpression);if(this.match(",")){var n=[];for(n.push(t);2!==this.lookahead.type&&this.match(",");)this.nextToken(),n.push(this.isolateCoverGrammar(this.parseAssignmentExpression));t=this.finalize(this.startNode(e),new o.SequenceExpression(n))}return t},e.prototype.parseStatementListItem=function(){var e;if(this.context.isAssignmentTarget=!0,this.context.isBindingElement=!0,4===this.lookahead.type)switch(this.lookahead.value){case"export":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalExportDeclaration),e=this.parseExportDeclaration();break;case"import":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalImportDeclaration),e=this.parseImportDeclaration();break;case"const":e=this.parseLexicalDeclaration({inFor:!1});break;case"function":e=this.parseFunctionDeclaration();break;case"class":e=this.parseClassDeclaration();break;case"let":e=this.isLexicalDeclaration()?this.parseLexicalDeclaration({inFor:!1}):this.parseStatement();break;default:e=this.parseStatement()}else e=this.parseStatement();return e},e.prototype.parseBlock=function(){var e=this.createNode();this.expect("{");for(var t=[];!this.match("}");)t.push(this.parseStatementListItem());return this.expect("}"),this.finalize(e,new o.BlockStatement(t))},e.prototype.parseLexicalBinding=function(e,t){var n=this.createNode(),r=this.parsePattern([],e);this.context.strict&&r.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(r.name)&&this.tolerateError(s.Messages.StrictVarName);var i=null;return"const"===e?this.matchKeyword("in")||this.matchContextualKeyword("of")||(this.match("=")?(this.nextToken(),i=this.isolateCoverGrammar(this.parseAssignmentExpression)):this.throwError(s.Messages.DeclarationMissingInitializer,"const")):(!t.inFor&&r.type!==u.Syntax.Identifier||this.match("="))&&(this.expect("="),i=this.isolateCoverGrammar(this.parseAssignmentExpression)),this.finalize(n,new o.VariableDeclarator(r,i))},e.prototype.parseBindingList=function(e,t){for(var n=[this.parseLexicalBinding(e,t)];this.match(",");)this.nextToken(),n.push(this.parseLexicalBinding(e,t));return n},e.prototype.isLexicalDeclaration=function(){var e=this.scanner.saveState();this.scanner.scanComments();var t=this.scanner.lex();return this.scanner.restoreState(e),3===t.type||7===t.type&&"["===t.value||7===t.type&&"{"===t.value||4===t.type&&"let"===t.value||4===t.type&&"yield"===t.value},e.prototype.parseLexicalDeclaration=function(e){var t=this.createNode(),n=this.nextToken().value;r.assert("let"===n||"const"===n,"Lexical declaration must be either let or const");var i=this.parseBindingList(n,e);return this.consumeSemicolon(),this.finalize(t,new o.VariableDeclaration(i,n))},e.prototype.parseBindingRestElement=function(e,t){var n=this.createNode();this.expect("...");var r=this.parsePattern(e,t);return this.finalize(n,new o.RestElement(r))},e.prototype.parseArrayPattern=function(e,t){var n=this.createNode();this.expect("[");for(var r=[];!this.match("]");)if(this.match(","))this.nextToken(),r.push(null);else{if(this.match("...")){r.push(this.parseBindingRestElement(e,t));break}r.push(this.parsePatternWithDefault(e,t)),this.match("]")||this.expect(",")}return this.expect("]"),this.finalize(n,new o.ArrayPattern(r))},e.prototype.parsePropertyPattern=function(e,t){var n,r,i=this.createNode(),s=!1,a=!1;if(3===this.lookahead.type){var u=this.lookahead;n=this.parseVariableIdentifier();var c=this.finalize(i,new o.Identifier(u.value));if(this.match("=")){e.push(u),a=!0,this.nextToken();var l=this.parseAssignmentExpression();r=this.finalize(this.startNode(u),new o.AssignmentPattern(c,l))}else this.match(":")?(this.expect(":"),r=this.parsePatternWithDefault(e,t)):(e.push(u),a=!0,r=c)}else s=this.match("["),n=this.parseObjectPropertyKey(),this.expect(":"),r=this.parsePatternWithDefault(e,t);return this.finalize(i,new o.Property("init",n,s,r,!1,a))},e.prototype.parseObjectPattern=function(e,t){var n=this.createNode(),r=[];for(this.expect("{");!this.match("}");)r.push(this.parsePropertyPattern(e,t)),this.match("}")||this.expect(",");return this.expect("}"),this.finalize(n,new o.ObjectPattern(r))},e.prototype.parsePattern=function(e,t){var n;return this.match("[")?n=this.parseArrayPattern(e,t):this.match("{")?n=this.parseObjectPattern(e,t):(!this.matchKeyword("let")||"const"!==t&&"let"!==t||this.tolerateUnexpectedToken(this.lookahead,s.Messages.LetInLexicalBinding),e.push(this.lookahead),n=this.parseVariableIdentifier(t)),n},e.prototype.parsePatternWithDefault=function(e,t){var n=this.lookahead,r=this.parsePattern(e,t);if(this.match("=")){this.nextToken();var i=this.context.allowYield;this.context.allowYield=!0;var s=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowYield=i,r=this.finalize(this.startNode(n),new o.AssignmentPattern(r,s))}return r},e.prototype.parseVariableIdentifier=function(e){var t=this.createNode(),n=this.nextToken();return 4===n.type&&"yield"===n.value?this.context.strict?this.tolerateUnexpectedToken(n,s.Messages.StrictReservedWord):this.context.allowYield||this.throwUnexpectedToken(n):3!==n.type?this.context.strict&&4===n.type&&this.scanner.isStrictModeReservedWord(n.value)?this.tolerateUnexpectedToken(n,s.Messages.StrictReservedWord):(this.context.strict||"let"!==n.value||"var"!==e)&&this.throwUnexpectedToken(n):(this.context.isModule||this.context.await)&&3===n.type&&"await"===n.value&&this.tolerateUnexpectedToken(n),this.finalize(t,new o.Identifier(n.value))},e.prototype.parseVariableDeclaration=function(e){var t=this.createNode(),n=this.parsePattern([],"var");this.context.strict&&n.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(n.name)&&this.tolerateError(s.Messages.StrictVarName);var r=null;return this.match("=")?(this.nextToken(),r=this.isolateCoverGrammar(this.parseAssignmentExpression)):n.type===u.Syntax.Identifier||e.inFor||this.expect("="),this.finalize(t,new o.VariableDeclarator(n,r))},e.prototype.parseVariableDeclarationList=function(e){var t={inFor:e.inFor},n=[];for(n.push(this.parseVariableDeclaration(t));this.match(",");)this.nextToken(),n.push(this.parseVariableDeclaration(t));return n},e.prototype.parseVariableStatement=function(){var e=this.createNode();this.expectKeyword("var");var t=this.parseVariableDeclarationList({inFor:!1});return this.consumeSemicolon(),this.finalize(e,new o.VariableDeclaration(t,"var"))},e.prototype.parseEmptyStatement=function(){var e=this.createNode();return this.expect(";"),this.finalize(e,new o.EmptyStatement)},e.prototype.parseExpressionStatement=function(){var e=this.createNode(),t=this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new o.ExpressionStatement(t))},e.prototype.parseIfClause=function(){return this.context.strict&&this.matchKeyword("function")&&this.tolerateError(s.Messages.StrictFunction),this.parseStatement()},e.prototype.parseIfStatement=function(){var e,t=this.createNode(),n=null;this.expectKeyword("if"),this.expect("(");var r=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new o.EmptyStatement)):(this.expect(")"),e=this.parseIfClause(),this.matchKeyword("else")&&(this.nextToken(),n=this.parseIfClause())),this.finalize(t,new o.IfStatement(r,e,n))},e.prototype.parseDoWhileStatement=function(){var e=this.createNode();this.expectKeyword("do");var t=this.context.inIteration;this.context.inIteration=!0;var n=this.parseStatement();this.context.inIteration=t,this.expectKeyword("while"),this.expect("(");var r=this.parseExpression();return!this.match(")")&&this.config.tolerant?this.tolerateUnexpectedToken(this.nextToken()):(this.expect(")"),this.match(";")&&this.nextToken()),this.finalize(e,new o.DoWhileStatement(n,r))},e.prototype.parseWhileStatement=function(){var e,t=this.createNode();this.expectKeyword("while"),this.expect("(");var n=this.parseExpression();if(!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new o.EmptyStatement);else{this.expect(")");var r=this.context.inIteration;this.context.inIteration=!0,e=this.parseStatement(),this.context.inIteration=r}return this.finalize(t,new o.WhileStatement(n,e))},e.prototype.parseForStatement=function(){var e,t,n,r=null,i=null,a=null,c=!0,l=this.createNode();if(this.expectKeyword("for"),this.expect("("),this.match(";"))this.nextToken();else if(this.matchKeyword("var")){r=this.createNode(),this.nextToken();var h=this.context.allowIn;this.context.allowIn=!1;var p=this.parseVariableDeclarationList({inFor:!0});if(this.context.allowIn=h,1===p.length&&this.matchKeyword("in")){var f=p[0];f.init&&(f.id.type===u.Syntax.ArrayPattern||f.id.type===u.Syntax.ObjectPattern||this.context.strict)&&this.tolerateError(s.Messages.ForInOfLoopInitializer,"for-in"),r=this.finalize(r,new o.VariableDeclaration(p,"var")),this.nextToken(),e=r,t=this.parseExpression(),r=null}else 1===p.length&&null===p[0].init&&this.matchContextualKeyword("of")?(r=this.finalize(r,new o.VariableDeclaration(p,"var")),this.nextToken(),e=r,t=this.parseAssignmentExpression(),r=null,c=!1):(r=this.finalize(r,new o.VariableDeclaration(p,"var")),this.expect(";"))}else if(this.matchKeyword("const")||this.matchKeyword("let")){r=this.createNode();var d=this.nextToken().value;this.context.strict||"in"!==this.lookahead.value?(h=this.context.allowIn,this.context.allowIn=!1,p=this.parseBindingList(d,{inFor:!0}),this.context.allowIn=h,1===p.length&&null===p[0].init&&this.matchKeyword("in")?(r=this.finalize(r,new o.VariableDeclaration(p,d)),this.nextToken(),e=r,t=this.parseExpression(),r=null):1===p.length&&null===p[0].init&&this.matchContextualKeyword("of")?(r=this.finalize(r,new o.VariableDeclaration(p,d)),this.nextToken(),e=r,t=this.parseAssignmentExpression(),r=null,c=!1):(this.consumeSemicolon(),r=this.finalize(r,new o.VariableDeclaration(p,d)))):(r=this.finalize(r,new o.Identifier(d)),this.nextToken(),e=r,t=this.parseExpression(),r=null)}else{var m=this.lookahead;if(h=this.context.allowIn,this.context.allowIn=!1,r=this.inheritCoverGrammar(this.parseAssignmentExpression),this.context.allowIn=h,this.matchKeyword("in"))this.context.isAssignmentTarget&&r.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForIn),this.nextToken(),this.reinterpretExpressionAsPattern(r),e=r,t=this.parseExpression(),r=null;else if(this.matchContextualKeyword("of"))this.context.isAssignmentTarget&&r.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForLoop),this.nextToken(),this.reinterpretExpressionAsPattern(r),e=r,t=this.parseAssignmentExpression(),r=null,c=!1;else{if(this.match(",")){for(var g=[r];this.match(",");)this.nextToken(),g.push(this.isolateCoverGrammar(this.parseAssignmentExpression));r=this.finalize(this.startNode(m),new o.SequenceExpression(g))}this.expect(";")}}if(void 0===e&&(this.match(";")||(i=this.parseExpression()),this.expect(";"),this.match(")")||(a=this.parseExpression())),!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),n=this.finalize(this.createNode(),new o.EmptyStatement);else{this.expect(")");var x=this.context.inIteration;this.context.inIteration=!0,n=this.isolateCoverGrammar(this.parseStatement),this.context.inIteration=x}return void 0===e?this.finalize(l,new o.ForStatement(r,i,a,n)):c?this.finalize(l,new o.ForInStatement(e,t,n)):this.finalize(l,new o.ForOfStatement(e,t,n))},e.prototype.parseContinueStatement=function(){var e=this.createNode();this.expectKeyword("continue");var t=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var n=this.parseVariableIdentifier();t=n;var r="$"+n.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,r)||this.throwError(s.Messages.UnknownLabel,n.name)}return this.consumeSemicolon(),null!==t||this.context.inIteration||this.throwError(s.Messages.IllegalContinue),this.finalize(e,new o.ContinueStatement(t))},e.prototype.parseBreakStatement=function(){var e=this.createNode();this.expectKeyword("break");var t=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var n=this.parseVariableIdentifier(),r="$"+n.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,r)||this.throwError(s.Messages.UnknownLabel,n.name),t=n}return this.consumeSemicolon(),null!==t||this.context.inIteration||this.context.inSwitch||this.throwError(s.Messages.IllegalBreak),this.finalize(e,new o.BreakStatement(t))},e.prototype.parseReturnStatement=function(){this.context.inFunctionBody||this.tolerateError(s.Messages.IllegalReturn);var e=this.createNode();this.expectKeyword("return");var t=(this.match(";")||this.match("}")||this.hasLineTerminator||2===this.lookahead.type)&&8!==this.lookahead.type&&10!==this.lookahead.type?null:this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new o.ReturnStatement(t))},e.prototype.parseWithStatement=function(){this.context.strict&&this.tolerateError(s.Messages.StrictModeWith);var e,t=this.createNode();this.expectKeyword("with"),this.expect("(");var n=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new o.EmptyStatement)):(this.expect(")"),e=this.parseStatement()),this.finalize(t,new o.WithStatement(n,e))},e.prototype.parseSwitchCase=function(){var e,t=this.createNode();this.matchKeyword("default")?(this.nextToken(),e=null):(this.expectKeyword("case"),e=this.parseExpression()),this.expect(":");for(var n=[];!(this.match("}")||this.matchKeyword("default")||this.matchKeyword("case"));)n.push(this.parseStatementListItem());return this.finalize(t,new o.SwitchCase(e,n))},e.prototype.parseSwitchStatement=function(){var e=this.createNode();this.expectKeyword("switch"),this.expect("(");var t=this.parseExpression();this.expect(")");var n=this.context.inSwitch;this.context.inSwitch=!0;var r=[],i=!1;for(this.expect("{");!this.match("}");){var a=this.parseSwitchCase();null===a.test&&(i&&this.throwError(s.Messages.MultipleDefaultsInSwitch),i=!0),r.push(a)}return this.expect("}"),this.context.inSwitch=n,this.finalize(e,new o.SwitchStatement(t,r))},e.prototype.parseLabelledStatement=function(){var e,t=this.createNode(),n=this.parseExpression();if(n.type===u.Syntax.Identifier&&this.match(":")){this.nextToken();var r=n,i="$"+r.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,i)&&this.throwError(s.Messages.Redeclaration,"Label",r.name),this.context.labelSet[i]=!0;var a=void 0;if(this.matchKeyword("class"))this.tolerateUnexpectedToken(this.lookahead),a=this.parseClassDeclaration();else if(this.matchKeyword("function")){var c=this.lookahead,l=this.parseFunctionDeclaration();this.context.strict?this.tolerateUnexpectedToken(c,s.Messages.StrictFunction):l.generator&&this.tolerateUnexpectedToken(c,s.Messages.GeneratorInLegacyContext),a=l}else a=this.parseStatement();delete this.context.labelSet[i],e=new o.LabeledStatement(r,a)}else this.consumeSemicolon(),e=new o.ExpressionStatement(n);return this.finalize(t,e)},e.prototype.parseThrowStatement=function(){var e=this.createNode();this.expectKeyword("throw"),this.hasLineTerminator&&this.throwError(s.Messages.NewlineAfterThrow);var t=this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new o.ThrowStatement(t))},e.prototype.parseCatchClause=function(){var e=this.createNode();this.expectKeyword("catch"),this.expect("("),this.match(")")&&this.throwUnexpectedToken(this.lookahead);for(var t=[],n=this.parsePattern(t),r={},i=0;i<t.length;i++){var a="$"+t[i].value;Object.prototype.hasOwnProperty.call(r,a)&&this.tolerateError(s.Messages.DuplicateBinding,t[i].value),r[a]=!0}this.context.strict&&n.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(n.name)&&this.tolerateError(s.Messages.StrictCatchVariable),this.expect(")");var c=this.parseBlock();return this.finalize(e,new o.CatchClause(n,c))},e.prototype.parseFinallyClause=function(){return this.expectKeyword("finally"),this.parseBlock()},e.prototype.parseTryStatement=function(){var e=this.createNode();this.expectKeyword("try");var t=this.parseBlock(),n=this.matchKeyword("catch")?this.parseCatchClause():null,r=this.matchKeyword("finally")?this.parseFinallyClause():null;return n||r||this.throwError(s.Messages.NoCatchOrFinally),this.finalize(e,new o.TryStatement(t,n,r))},e.prototype.parseDebuggerStatement=function(){var e=this.createNode();return this.expectKeyword("debugger"),this.consumeSemicolon(),this.finalize(e,new o.DebuggerStatement)},e.prototype.parseStatement=function(){var e;switch(this.lookahead.type){case 1:case 5:case 6:case 8:case 10:case 9:e=this.parseExpressionStatement();break;case 7:var t=this.lookahead.value;e="{"===t?this.parseBlock():"("===t?this.parseExpressionStatement():";"===t?this.parseEmptyStatement():this.parseExpressionStatement();break;case 3:e=this.matchAsyncFunction()?this.parseFunctionDeclaration():this.parseLabelledStatement();break;case 4:switch(this.lookahead.value){case"break":e=this.parseBreakStatement();break;case"continue":e=this.parseContinueStatement();break;case"debugger":e=this.parseDebuggerStatement();break;case"do":e=this.parseDoWhileStatement();break;case"for":e=this.parseForStatement();break;case"function":e=this.parseFunctionDeclaration();break;case"if":e=this.parseIfStatement();break;case"return":e=this.parseReturnStatement();break;case"switch":e=this.parseSwitchStatement();break;case"throw":e=this.parseThrowStatement();break;case"try":e=this.parseTryStatement();break;case"var":e=this.parseVariableStatement();break;case"while":e=this.parseWhileStatement();break;case"with":e=this.parseWithStatement();break;default:e=this.parseExpressionStatement()}break;default:e=this.throwUnexpectedToken(this.lookahead)}return e},e.prototype.parseFunctionSourceElements=function(){var e=this.createNode();this.expect("{");var t=this.parseDirectivePrologues(),n=this.context.labelSet,r=this.context.inIteration,i=this.context.inSwitch,s=this.context.inFunctionBody;for(this.context.labelSet={},this.context.inIteration=!1,this.context.inSwitch=!1,this.context.inFunctionBody=!0;2!==this.lookahead.type&&!this.match("}");)t.push(this.parseStatementListItem());return this.expect("}"),this.context.labelSet=n,this.context.inIteration=r,this.context.inSwitch=i,this.context.inFunctionBody=s,this.finalize(e,new o.BlockStatement(t))},e.prototype.validateParam=function(e,t,n){var r="$"+n;this.context.strict?(this.scanner.isRestrictedWord(n)&&(e.stricted=t,e.message=s.Messages.StrictParamName),Object.prototype.hasOwnProperty.call(e.paramSet,r)&&(e.stricted=t,e.message=s.Messages.StrictParamDupe)):e.firstRestricted||(this.scanner.isRestrictedWord(n)?(e.firstRestricted=t,e.message=s.Messages.StrictParamName):this.scanner.isStrictModeReservedWord(n)?(e.firstRestricted=t,e.message=s.Messages.StrictReservedWord):Object.prototype.hasOwnProperty.call(e.paramSet,r)&&(e.stricted=t,e.message=s.Messages.StrictParamDupe)),"function"==typeof Object.defineProperty?Object.defineProperty(e.paramSet,r,{value:!0,enumerable:!0,writable:!0,configurable:!0}):e.paramSet[r]=!0},e.prototype.parseRestElement=function(e){var t=this.createNode();this.expect("...");var n=this.parsePattern(e);return this.match("=")&&this.throwError(s.Messages.DefaultRestParameter),this.match(")")||this.throwError(s.Messages.ParameterAfterRestParameter),this.finalize(t,new o.RestElement(n))},e.prototype.parseFormalParameter=function(e){for(var t=[],n=this.match("...")?this.parseRestElement(t):this.parsePatternWithDefault(t),r=0;r<t.length;r++)this.validateParam(e,t[r],t[r].value);e.simple=e.simple&&n instanceof o.Identifier,e.params.push(n)},e.prototype.parseFormalParameters=function(e){var t;if(t={simple:!0,params:[],firstRestricted:e},this.expect("("),!this.match(")"))for(t.paramSet={};2!==this.lookahead.type&&(this.parseFormalParameter(t),!this.match(")"))&&(this.expect(","),!this.match(")")););return this.expect(")"),{simple:t.simple,params:t.params,stricted:t.stricted,firstRestricted:t.firstRestricted,message:t.message}},e.prototype.matchAsyncFunction=function(){var e=this.matchContextualKeyword("async");if(e){var t=this.scanner.saveState();this.scanner.scanComments();var n=this.scanner.lex();this.scanner.restoreState(t),e=t.lineNumber===n.lineNumber&&4===n.type&&"function"===n.value}return e},e.prototype.parseFunctionDeclaration=function(e){var t=this.createNode(),n=this.matchContextualKeyword("async");n&&this.nextToken(),this.expectKeyword("function");var r,i=!n&&this.match("*");i&&this.nextToken();var a=null,u=null;if(!e||!this.match("(")){var c=this.lookahead;a=this.parseVariableIdentifier(),this.context.strict?this.scanner.isRestrictedWord(c.value)&&this.tolerateUnexpectedToken(c,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(c.value)?(u=c,r=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(c.value)&&(u=c,r=s.Messages.StrictReservedWord)}var l=this.context.await,h=this.context.allowYield;this.context.await=n,this.context.allowYield=!i;var p=this.parseFormalParameters(u),f=p.params,d=p.stricted;u=p.firstRestricted,p.message&&(r=p.message);var m=this.context.strict,g=this.context.allowStrictDirective;this.context.allowStrictDirective=p.simple;var x=this.parseFunctionSourceElements();return this.context.strict&&u&&this.throwUnexpectedToken(u,r),this.context.strict&&d&&this.tolerateUnexpectedToken(d,r),this.context.strict=m,this.context.allowStrictDirective=g,this.context.await=l,this.context.allowYield=h,n?this.finalize(t,new o.AsyncFunctionDeclaration(a,f,x)):this.finalize(t,new o.FunctionDeclaration(a,f,x,i))},e.prototype.parseFunctionExpression=function(){var e=this.createNode(),t=this.matchContextualKeyword("async");t&&this.nextToken(),this.expectKeyword("function");var n,r=!t&&this.match("*");r&&this.nextToken();var i,a=null,u=this.context.await,c=this.context.allowYield;if(this.context.await=t,this.context.allowYield=!r,!this.match("(")){var l=this.lookahead;a=this.context.strict||r||!this.matchKeyword("yield")?this.parseVariableIdentifier():this.parseIdentifierName(),this.context.strict?this.scanner.isRestrictedWord(l.value)&&this.tolerateUnexpectedToken(l,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(l.value)?(i=l,n=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(l.value)&&(i=l,n=s.Messages.StrictReservedWord)}var h=this.parseFormalParameters(i),p=h.params,f=h.stricted;i=h.firstRestricted,h.message&&(n=h.message);var d=this.context.strict,m=this.context.allowStrictDirective;this.context.allowStrictDirective=h.simple;var g=this.parseFunctionSourceElements();return this.context.strict&&i&&this.throwUnexpectedToken(i,n),this.context.strict&&f&&this.tolerateUnexpectedToken(f,n),this.context.strict=d,this.context.allowStrictDirective=m,this.context.await=u,this.context.allowYield=c,t?this.finalize(e,new o.AsyncFunctionExpression(a,p,g)):this.finalize(e,new o.FunctionExpression(a,p,g,r))},e.prototype.parseDirective=function(){var e=this.lookahead,t=this.createNode(),n=this.parseExpression(),r=n.type===u.Syntax.Literal?this.getTokenRaw(e).slice(1,-1):null;return this.consumeSemicolon(),this.finalize(t,r?new o.Directive(n,r):new o.ExpressionStatement(n))},e.prototype.parseDirectivePrologues=function(){for(var e=null,t=[];;){var n=this.lookahead;if(8!==n.type)break;var r=this.parseDirective();t.push(r);var i=r.directive;if("string"!=typeof i)break;"use strict"===i?(this.context.strict=!0,e&&this.tolerateUnexpectedToken(e,s.Messages.StrictOctalLiteral),this.context.allowStrictDirective||this.tolerateUnexpectedToken(n,s.Messages.IllegalLanguageModeDirective)):!e&&n.octal&&(e=n)}return t},e.prototype.qualifiedPropertyName=function(e){switch(e.type){case 3:case 8:case 1:case 5:case 6:case 4:return!0;case 7:return"["===e.value}return!1},e.prototype.parseGetterMethod=function(){var e=this.createNode(),t=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters();n.params.length>0&&this.tolerateError(s.Messages.BadGetterArity);var r=this.parsePropertyMethod(n);return this.context.allowYield=t,this.finalize(e,new o.FunctionExpression(null,n.params,r,!1))},e.prototype.parseSetterMethod=function(){var e=this.createNode(),t=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters();1!==n.params.length?this.tolerateError(s.Messages.BadSetterArity):n.params[0]instanceof o.RestElement&&this.tolerateError(s.Messages.BadSetterRestParameter);var r=this.parsePropertyMethod(n);return this.context.allowYield=t,this.finalize(e,new o.FunctionExpression(null,n.params,r,!1))},e.prototype.parseGeneratorMethod=function(){var e=this.createNode(),t=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters();this.context.allowYield=!1;var r=this.parsePropertyMethod(n);return this.context.allowYield=t,this.finalize(e,new o.FunctionExpression(null,n.params,r,!0))},e.prototype.isStartOfExpression=function(){var e=!0,t=this.lookahead.value;switch(this.lookahead.type){case 7:e="["===t||"("===t||"{"===t||"+"===t||"-"===t||"!"===t||"~"===t||"++"===t||"--"===t||"/"===t||"/="===t;break;case 4:e="class"===t||"delete"===t||"function"===t||"let"===t||"new"===t||"super"===t||"this"===t||"typeof"===t||"void"===t||"yield"===t}return e},e.prototype.parseYieldExpression=function(){var e=this.createNode();this.expectKeyword("yield");var t=null,n=!1;if(!this.hasLineTerminator){var r=this.context.allowYield;this.context.allowYield=!1,(n=this.match("*"))?(this.nextToken(),t=this.parseAssignmentExpression()):this.isStartOfExpression()&&(t=this.parseAssignmentExpression()),this.context.allowYield=r}return this.finalize(e,new o.YieldExpression(t,n))},e.prototype.parseClassElement=function(e){var t=this.lookahead,n=this.createNode(),r="",i=null,a=null,u=!1,c=!1,l=!1,h=!1;if(this.match("*"))this.nextToken();else if(u=this.match("["),"static"===(i=this.parseObjectPropertyKey()).name&&(this.qualifiedPropertyName(this.lookahead)||this.match("*"))&&(t=this.lookahead,l=!0,u=this.match("["),this.match("*")?this.nextToken():i=this.parseObjectPropertyKey()),3===t.type&&!this.hasLineTerminator&&"async"===t.value){var p=this.lookahead.value;":"!==p&&"("!==p&&"*"!==p&&(h=!0,t=this.lookahead,i=this.parseObjectPropertyKey(),3===t.type&&"constructor"===t.value&&this.tolerateUnexpectedToken(t,s.Messages.ConstructorIsAsync))}var f=this.qualifiedPropertyName(this.lookahead);return 3===t.type?"get"===t.value&&f?(r="get",u=this.match("["),i=this.parseObjectPropertyKey(),this.context.allowYield=!1,a=this.parseGetterMethod()):"set"===t.value&&f&&(r="set",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseSetterMethod()):7===t.type&&"*"===t.value&&f&&(r="init",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseGeneratorMethod(),c=!0),!r&&i&&this.match("(")&&(r="init",a=h?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0),r||this.throwUnexpectedToken(this.lookahead),"init"===r&&(r="method"),u||(l&&this.isPropertyKey(i,"prototype")&&this.throwUnexpectedToken(t,s.Messages.StaticPrototype),!l&&this.isPropertyKey(i,"constructor")&&(("method"!==r||!c||a&&a.generator)&&this.throwUnexpectedToken(t,s.Messages.ConstructorSpecialMethod),e.value?this.throwUnexpectedToken(t,s.Messages.DuplicateConstructor):e.value=!0,r="constructor")),this.finalize(n,new o.MethodDefinition(i,u,a,r,l))},e.prototype.parseClassElementList=function(){var e=[],t={value:!1};for(this.expect("{");!this.match("}");)this.match(";")?this.nextToken():e.push(this.parseClassElement(t));return this.expect("}"),e},e.prototype.parseClassBody=function(){var e=this.createNode(),t=this.parseClassElementList();return this.finalize(e,new o.ClassBody(t))},e.prototype.parseClassDeclaration=function(e){var t=this.createNode(),n=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var r=e&&3!==this.lookahead.type?null:this.parseVariableIdentifier(),i=null;this.matchKeyword("extends")&&(this.nextToken(),i=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var s=this.parseClassBody();return this.context.strict=n,this.finalize(t,new o.ClassDeclaration(r,i,s))},e.prototype.parseClassExpression=function(){var e=this.createNode(),t=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var n=3===this.lookahead.type?this.parseVariableIdentifier():null,r=null;this.matchKeyword("extends")&&(this.nextToken(),r=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var i=this.parseClassBody();return this.context.strict=t,this.finalize(e,new o.ClassExpression(n,r,i))},e.prototype.parseModule=function(){this.context.strict=!0,this.context.isModule=!0,this.scanner.isModule=!0;for(var e=this.createNode(),t=this.parseDirectivePrologues();2!==this.lookahead.type;)t.push(this.parseStatementListItem());return this.finalize(e,new o.Module(t))},e.prototype.parseScript=function(){for(var e=this.createNode(),t=this.parseDirectivePrologues();2!==this.lookahead.type;)t.push(this.parseStatementListItem());return this.finalize(e,new o.Script(t))},e.prototype.parseModuleSpecifier=function(){var e=this.createNode();8!==this.lookahead.type&&this.throwError(s.Messages.InvalidModuleSpecifier);var t=this.nextToken(),n=this.getTokenRaw(t);return this.finalize(e,new o.Literal(t.value,n))},e.prototype.parseImportSpecifier=function(){var e,t,n=this.createNode();return 3===this.lookahead.type?(t=e=this.parseVariableIdentifier(),this.matchContextualKeyword("as")&&(this.nextToken(),t=this.parseVariableIdentifier())):(t=e=this.parseIdentifierName(),this.matchContextualKeyword("as")?(this.nextToken(),t=this.parseVariableIdentifier()):this.throwUnexpectedToken(this.nextToken())),this.finalize(n,new o.ImportSpecifier(t,e))},e.prototype.parseNamedImports=function(){this.expect("{");for(var e=[];!this.match("}");)e.push(this.parseImportSpecifier()),this.match("}")||this.expect(",");return this.expect("}"),e},e.prototype.parseImportDefaultSpecifier=function(){var e=this.createNode(),t=this.parseIdentifierName();return this.finalize(e,new o.ImportDefaultSpecifier(t))},e.prototype.parseImportNamespaceSpecifier=function(){var e=this.createNode();this.expect("*"),this.matchContextualKeyword("as")||this.throwError(s.Messages.NoAsAfterImportNamespace),this.nextToken();var t=this.parseIdentifierName();return this.finalize(e,new o.ImportNamespaceSpecifier(t))},e.prototype.parseImportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalImportDeclaration);var e,t=this.createNode();this.expectKeyword("import");var n=[];if(8===this.lookahead.type)e=this.parseModuleSpecifier();else{if(this.match("{")?n=n.concat(this.parseNamedImports()):this.match("*")?n.push(this.parseImportNamespaceSpecifier()):this.isIdentifierName(this.lookahead)&&!this.matchKeyword("default")?(n.push(this.parseImportDefaultSpecifier()),this.match(",")&&(this.nextToken(),this.match("*")?n.push(this.parseImportNamespaceSpecifier()):this.match("{")?n=n.concat(this.parseNamedImports()):this.throwUnexpectedToken(this.lookahead))):this.throwUnexpectedToken(this.nextToken()),!this.matchContextualKeyword("from")){var r=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}this.nextToken(),e=this.parseModuleSpecifier()}return this.consumeSemicolon(),this.finalize(t,new o.ImportDeclaration(n,e))},e.prototype.parseExportSpecifier=function(){var e=this.createNode(),t=this.parseIdentifierName(),n=t;return this.matchContextualKeyword("as")&&(this.nextToken(),n=this.parseIdentifierName()),this.finalize(e,new o.ExportSpecifier(t,n))},e.prototype.parseExportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalExportDeclaration);var e,t=this.createNode();if(this.expectKeyword("export"),this.matchKeyword("default"))if(this.nextToken(),this.matchKeyword("function")){var n=this.parseFunctionDeclaration(!0);e=this.finalize(t,new o.ExportDefaultDeclaration(n))}else this.matchKeyword("class")?(n=this.parseClassDeclaration(!0),e=this.finalize(t,new o.ExportDefaultDeclaration(n))):this.matchContextualKeyword("async")?(n=this.matchAsyncFunction()?this.parseFunctionDeclaration(!0):this.parseAssignmentExpression(),e=this.finalize(t,new o.ExportDefaultDeclaration(n))):(this.matchContextualKeyword("from")&&this.throwError(s.Messages.UnexpectedToken,this.lookahead.value),n=this.match("{")?this.parseObjectInitializer():this.match("[")?this.parseArrayInitializer():this.parseAssignmentExpression(),this.consumeSemicolon(),e=this.finalize(t,new o.ExportDefaultDeclaration(n)));else if(this.match("*")){if(this.nextToken(),!this.matchContextualKeyword("from")){var r=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}this.nextToken();var i=this.parseModuleSpecifier();this.consumeSemicolon(),e=this.finalize(t,new o.ExportAllDeclaration(i))}else if(4===this.lookahead.type){switch(n=void 0,this.lookahead.value){case"let":case"const":n=this.parseLexicalDeclaration({inFor:!1});break;case"var":case"class":case"function":n=this.parseStatementListItem();break;default:this.throwUnexpectedToken(this.lookahead)}e=this.finalize(t,new o.ExportNamedDeclaration(n,[],null))}else if(this.matchAsyncFunction())n=this.parseFunctionDeclaration(),e=this.finalize(t,new o.ExportNamedDeclaration(n,[],null));else{var a=[],u=null,c=!1;for(this.expect("{");!this.match("}");)c=c||this.matchKeyword("default"),a.push(this.parseExportSpecifier()),this.match("}")||this.expect(",");this.expect("}"),this.matchContextualKeyword("from")?(this.nextToken(),u=this.parseModuleSpecifier(),this.consumeSemicolon()):c?(r=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause,this.throwError(r,this.lookahead.value)):this.consumeSemicolon(),e=this.finalize(t,new o.ExportNamedDeclaration(null,a,u))}return e},e}();t.Parser=h},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=function(e,t){if(!e)throw new Error("ASSERT: "+t)}},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){this.errors=[],this.tolerant=!1}return e.prototype.recordError=function(e){this.errors.push(e)},e.prototype.tolerate=function(e){if(!this.tolerant)throw e;this.recordError(e)},e.prototype.constructError=function(e,t){var n=new Error(e);try{throw n}catch(e){Object.create&&Object.defineProperty&&(n=Object.create(e),Object.defineProperty(n,"column",{value:t}))}return n},e.prototype.createError=function(e,t,n,r){var i="Line "+t+": "+r,s=this.constructError(i,n);return s.index=e,s.lineNumber=t,s.description=r,s},e.prototype.throwError=function(e,t,n,r){throw this.createError(e,t,n,r)},e.prototype.tolerateError=function(e,t,n,r){var i=this.createError(e,t,n,r);if(!this.tolerant)throw i;this.recordError(i)},e}();t.ErrorHandler=n},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Messages={BadGetterArity:"Getter must not have any formal parameters",BadSetterArity:"Setter must have exactly one formal parameter",BadSetterRestParameter:"Setter function argument must not be a rest parameter",ConstructorIsAsync:"Class constructor may not be an async method",ConstructorSpecialMethod:"Class constructor may not be an accessor",DeclarationMissingInitializer:"Missing initializer in %0 declaration",DefaultRestParameter:"Unexpected token =",DuplicateBinding:"Duplicate binding %0",DuplicateConstructor:"A class may only have one constructor",DuplicateProtoProperty:"Duplicate __proto__ fields are not allowed in object literals",ForInOfLoopInitializer:"%0 loop variable declaration may not have an initializer",GeneratorInLegacyContext:"Generator declarations are not allowed in legacy contexts",IllegalBreak:"Illegal break statement",IllegalContinue:"Illegal continue statement",IllegalExportDeclaration:"Unexpected token",IllegalImportDeclaration:"Unexpected token",IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list",IllegalReturn:"Illegal return statement",InvalidEscapedReservedWord:"Keyword must not contain escaped characters",InvalidHexEscapeSequence:"Invalid hexadecimal escape sequence",InvalidLHSInAssignment:"Invalid left-hand side in assignment",InvalidLHSInForIn:"Invalid left-hand side in for-in",InvalidLHSInForLoop:"Invalid left-hand side in for-loop",InvalidModuleSpecifier:"Unexpected token",InvalidRegExp:"Invalid regular expression",LetInLexicalBinding:"let is disallowed as a lexically bound name",MissingFromClause:"Unexpected token",MultipleDefaultsInSwitch:"More than one default clause in switch statement",NewlineAfterThrow:"Illegal newline after throw",NoAsAfterImportNamespace:"Unexpected token",NoCatchOrFinally:"Missing catch or finally after try",ParameterAfterRestParameter:"Rest parameter must be last formal parameter",Redeclaration:"%0 '%1' has already been declared",StaticPrototype:"Classes may not have static property named prototype",StrictCatchVariable:"Catch variable may not be eval or arguments in strict mode",StrictDelete:"Delete of an unqualified identifier in strict mode.",StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block",StrictFunctionName:"Function name may not be eval or arguments in strict mode",StrictLHSAssignment:"Assignment to eval or arguments is not allowed in strict mode",StrictLHSPostfix:"Postfix increment/decrement may not have eval or arguments operand in strict mode",StrictLHSPrefix:"Prefix increment/decrement may not have eval or arguments operand in strict mode",StrictModeWith:"Strict mode code may not include a with statement",StrictOctalLiteral:"Octal literals are not allowed in strict mode.",StrictParamDupe:"Strict mode function may not have duplicate parameter names",StrictParamName:"Parameter name eval or arguments is not allowed in strict mode",StrictReservedWord:"Use of future reserved word in strict mode",StrictVarName:"Variable name may not be eval or arguments in strict mode",TemplateOctalLiteral:"Octal literals are not allowed in template strings.",UnexpectedEOS:"Unexpected end of input",UnexpectedIdentifier:"Unexpected identifier",UnexpectedNumber:"Unexpected number",UnexpectedReserved:"Unexpected reserved word",UnexpectedString:"Unexpected string",UnexpectedTemplate:"Unexpected quasi %0",UnexpectedToken:"Unexpected token %0",UnexpectedTokenIllegal:"Unexpected token ILLEGAL",UnknownLabel:"Undefined label '%0'",UnterminatedRegExp:"Invalid regular expression: missing /"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(9),i=n(4),s=n(11);function o(e){return"0123456789abcdef".indexOf(e.toLowerCase())}function a(e){return"01234567".indexOf(e)}var u=function(){function e(e,t){this.source=e,this.errorHandler=t,this.trackComment=!1,this.isModule=!1,this.length=e.length,this.index=0,this.lineNumber=e.length>0?1:0,this.lineStart=0,this.curlyStack=[]}return e.prototype.saveState=function(){return{index:this.index,lineNumber:this.lineNumber,lineStart:this.lineStart}},e.prototype.restoreState=function(e){this.index=e.index,this.lineNumber=e.lineNumber,this.lineStart=e.lineStart},e.prototype.eof=function(){return this.index>=this.length},e.prototype.throwUnexpectedToken=function(e){return void 0===e&&(e=s.Messages.UnexpectedTokenIllegal),this.errorHandler.throwError(this.index,this.lineNumber,this.index-this.lineStart+1,e)},e.prototype.tolerateUnexpectedToken=function(e){void 0===e&&(e=s.Messages.UnexpectedTokenIllegal),this.errorHandler.tolerateError(this.index,this.lineNumber,this.index-this.lineStart+1,e)},e.prototype.skipSingleLineComment=function(e){var t,n,r=[];for(this.trackComment&&(r=[],t=this.index-e,n={start:{line:this.lineNumber,column:this.index-this.lineStart-e},end:{}});!this.eof();){var s=this.source.charCodeAt(this.index);if(++this.index,i.Character.isLineTerminator(s)){if(this.trackComment){n.end={line:this.lineNumber,column:this.index-this.lineStart-1};var o={multiLine:!1,slice:[t+e,this.index-1],range:[t,this.index-1],loc:n};r.push(o)}return 13===s&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,r}}return this.trackComment&&(n.end={line:this.lineNumber,column:this.index-this.lineStart},o={multiLine:!1,slice:[t+e,this.index],range:[t,this.index],loc:n},r.push(o)),r},e.prototype.skipMultiLineComment=function(){var e,t,n=[];for(this.trackComment&&(n=[],e=this.index-2,t={start:{line:this.lineNumber,column:this.index-this.lineStart-2},end:{}});!this.eof();){var r=this.source.charCodeAt(this.index);if(i.Character.isLineTerminator(r))13===r&&10===this.source.charCodeAt(this.index+1)&&++this.index,++this.lineNumber,++this.index,this.lineStart=this.index;else if(42===r){if(47===this.source.charCodeAt(this.index+1)){if(this.index+=2,this.trackComment){t.end={line:this.lineNumber,column:this.index-this.lineStart};var s={multiLine:!0,slice:[e+2,this.index-2],range:[e,this.index],loc:t};n.push(s)}return n}++this.index}else++this.index}return this.trackComment&&(t.end={line:this.lineNumber,column:this.index-this.lineStart},s={multiLine:!0,slice:[e+2,this.index],range:[e,this.index],loc:t},n.push(s)),this.tolerateUnexpectedToken(),n},e.prototype.scanComments=function(){var e;this.trackComment&&(e=[]);for(var t=0===this.index;!this.eof();){var n=this.source.charCodeAt(this.index);if(i.Character.isWhiteSpace(n))++this.index;else if(i.Character.isLineTerminator(n))++this.index,13===n&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,t=!0;else if(47===n)if(47===(n=this.source.charCodeAt(this.index+1))){this.index+=2;var r=this.skipSingleLineComment(2);this.trackComment&&(e=e.concat(r)),t=!0}else{if(42!==n)break;this.index+=2,r=this.skipMultiLineComment(),this.trackComment&&(e=e.concat(r))}else if(t&&45===n){if(45!==this.source.charCodeAt(this.index+1)||62!==this.source.charCodeAt(this.index+2))break;this.index+=3,r=this.skipSingleLineComment(3),this.trackComment&&(e=e.concat(r))}else{if(60!==n||this.isModule)break;if("!--"!==this.source.slice(this.index+1,this.index+4))break;this.index+=4,r=this.skipSingleLineComment(4),this.trackComment&&(e=e.concat(r))}}return e},e.prototype.isFutureReservedWord=function(e){switch(e){case"enum":case"export":case"import":case"super":return!0;default:return!1}},e.prototype.isStrictModeReservedWord=function(e){switch(e){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"yield":case"let":return!0;default:return!1}},e.prototype.isRestrictedWord=function(e){return"eval"===e||"arguments"===e},e.prototype.isKeyword=function(e){switch(e.length){case 2:return"if"===e||"in"===e||"do"===e;case 3:return"var"===e||"for"===e||"new"===e||"try"===e||"let"===e;case 4:return"this"===e||"else"===e||"case"===e||"void"===e||"with"===e||"enum"===e;case 5:return"while"===e||"break"===e||"catch"===e||"throw"===e||"const"===e||"yield"===e||"class"===e||"super"===e;case 6:return"return"===e||"typeof"===e||"delete"===e||"switch"===e||"export"===e||"import"===e;case 7:return"default"===e||"finally"===e||"extends"===e;case 8:return"function"===e||"continue"===e||"debugger"===e;case 10:return"instanceof"===e;default:return!1}},e.prototype.codePointAt=function(e){var t=this.source.charCodeAt(e);if(t>=55296&&t<=56319){var n=this.source.charCodeAt(e+1);n>=56320&&n<=57343&&(t=1024*(t-55296)+n-56320+65536)}return t},e.prototype.scanHexEscape=function(e){for(var t="u"===e?4:2,n=0,r=0;r<t;++r){if(this.eof()||!i.Character.isHexDigit(this.source.charCodeAt(this.index)))return null;n=16*n+o(this.source[this.index++])}return String.fromCharCode(n)},e.prototype.scanUnicodeCodePointEscape=function(){var e=this.source[this.index],t=0;for("}"===e&&this.throwUnexpectedToken();!this.eof()&&(e=this.source[this.index++],i.Character.isHexDigit(e.charCodeAt(0)));)t=16*t+o(e);return(t>1114111||"}"!==e)&&this.throwUnexpectedToken(),i.Character.fromCodePoint(t)},e.prototype.getIdentifier=function(){for(var e=this.index++;!this.eof();){var t=this.source.charCodeAt(this.index);if(92===t)return this.index=e,this.getComplexIdentifier();if(t>=55296&&t<57343)return this.index=e,this.getComplexIdentifier();if(!i.Character.isIdentifierPart(t))break;++this.index}return this.source.slice(e,this.index)},e.prototype.getComplexIdentifier=function(){var e,t=this.codePointAt(this.index),n=i.Character.fromCodePoint(t);for(this.index+=n.length,92===t&&(117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,e=this.scanUnicodeCodePointEscape()):null!==(e=this.scanHexEscape("u"))&&"\\"!==e&&i.Character.isIdentifierStart(e.charCodeAt(0))||this.throwUnexpectedToken(),n=e);!this.eof()&&(t=this.codePointAt(this.index),i.Character.isIdentifierPart(t));)n+=e=i.Character.fromCodePoint(t),this.index+=e.length,92===t&&(n=n.substr(0,n.length-1),117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,e=this.scanUnicodeCodePointEscape()):null!==(e=this.scanHexEscape("u"))&&"\\"!==e&&i.Character.isIdentifierPart(e.charCodeAt(0))||this.throwUnexpectedToken(),n+=e);return n},e.prototype.octalToDecimal=function(e){var t="0"!==e,n=a(e);return!this.eof()&&i.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(t=!0,n=8*n+a(this.source[this.index++]),"0123".indexOf(e)>=0&&!this.eof()&&i.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(n=8*n+a(this.source[this.index++]))),{code:n,octal:t}},e.prototype.scanIdentifier=function(){var e,t=this.index,n=92===this.source.charCodeAt(t)?this.getComplexIdentifier():this.getIdentifier();if(3!=(e=1===n.length?3:this.isKeyword(n)?4:"null"===n?5:"true"===n||"false"===n?1:3)&&t+n.length!==this.index){var r=this.index;this.index=t,this.tolerateUnexpectedToken(s.Messages.InvalidEscapedReservedWord),this.index=r}return{type:e,value:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},e.prototype.scanPunctuator=function(){var e=this.index,t=this.source[this.index];switch(t){case"(":case"{":"{"===t&&this.curlyStack.push("{"),++this.index;break;case".":++this.index,"."===this.source[this.index]&&"."===this.source[this.index+1]&&(this.index+=2,t="...");break;case"}":++this.index,this.curlyStack.pop();break;case")":case";":case",":case"[":case"]":case":":case"?":case"~":++this.index;break;default:">>>="===(t=this.source.substr(this.index,4))?this.index+=4:"==="===(t=t.substr(0,3))||"!=="===t||">>>"===t||"<<="===t||">>="===t||"**="===t?this.index+=3:"&&"===(t=t.substr(0,2))||"||"===t||"=="===t||"!="===t||"+="===t||"-="===t||"*="===t||"/="===t||"++"===t||"--"===t||"<<"===t||">>"===t||"&="===t||"|="===t||"^="===t||"%="===t||"<="===t||">="===t||"=>"===t||"**"===t?this.index+=2:(t=this.source[this.index],"<>=!+-*%&|^/".indexOf(t)>=0&&++this.index)}return this.index===e&&this.throwUnexpectedToken(),{type:7,value:t,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanHexLiteral=function(e){for(var t="";!this.eof()&&i.Character.isHexDigit(this.source.charCodeAt(this.index));)t+=this.source[this.index++];return 0===t.length&&this.throwUnexpectedToken(),i.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseInt("0x"+t,16),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanBinaryLiteral=function(e){for(var t,n="";!this.eof()&&("0"===(t=this.source[this.index])||"1"===t);)n+=this.source[this.index++];return 0===n.length&&this.throwUnexpectedToken(),this.eof()||(t=this.source.charCodeAt(this.index),(i.Character.isIdentifierStart(t)||i.Character.isDecimalDigit(t))&&this.throwUnexpectedToken()),{type:6,value:parseInt(n,2),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanOctalLiteral=function(e,t){var n="",r=!1;for(i.Character.isOctalDigit(e.charCodeAt(0))?(r=!0,n="0"+this.source[this.index++]):++this.index;!this.eof()&&i.Character.isOctalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];return r||0!==n.length||this.throwUnexpectedToken(),(i.Character.isIdentifierStart(this.source.charCodeAt(this.index))||i.Character.isDecimalDigit(this.source.charCodeAt(this.index)))&&this.throwUnexpectedToken(),{type:6,value:parseInt(n,8),octal:r,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},e.prototype.isImplicitOctalLiteral=function(){for(var e=this.index+1;e<this.length;++e){var t=this.source[e];if("8"===t||"9"===t)return!1;if(!i.Character.isOctalDigit(t.charCodeAt(0)))return!0}return!0},e.prototype.scanNumericLiteral=function(){var e=this.index,t=this.source[e];r.assert(i.Character.isDecimalDigit(t.charCodeAt(0))||"."===t,"Numeric literal must start with a decimal digit or a decimal point");var n="";if("."!==t){if(n=this.source[this.index++],t=this.source[this.index],"0"===n){if("x"===t||"X"===t)return++this.index,this.scanHexLiteral(e);if("b"===t||"B"===t)return++this.index,this.scanBinaryLiteral(e);if("o"===t||"O"===t)return this.scanOctalLiteral(t,e);if(t&&i.Character.isOctalDigit(t.charCodeAt(0))&&this.isImplicitOctalLiteral())return this.scanOctalLiteral(t,e)}for(;i.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];t=this.source[this.index]}if("."===t){for(n+=this.source[this.index++];i.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];t=this.source[this.index]}if("e"===t||"E"===t)if(n+=this.source[this.index++],"+"!==(t=this.source[this.index])&&"-"!==t||(n+=this.source[this.index++]),i.Character.isDecimalDigit(this.source.charCodeAt(this.index)))for(;i.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];else this.throwUnexpectedToken();return i.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseFloat(n),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanStringLiteral=function(){var e=this.index,t=this.source[e];r.assert("'"===t||'"'===t,"String literal must starts with a quote"),++this.index;for(var n=!1,o="";!this.eof();){var a=this.source[this.index++];if(a===t){t="";break}if("\\"===a)if((a=this.source[this.index++])&&i.Character.isLineTerminator(a.charCodeAt(0)))++this.lineNumber,"\r"===a&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(a){case"u":if("{"===this.source[this.index])++this.index,o+=this.scanUnicodeCodePointEscape();else{var u=this.scanHexEscape(a);null===u&&this.throwUnexpectedToken(),o+=u}break;case"x":var c=this.scanHexEscape(a);null===c&&this.throwUnexpectedToken(s.Messages.InvalidHexEscapeSequence),o+=c;break;case"n":o+="\n";break;case"r":o+="\r";break;case"t":o+="\t";break;case"b":o+="\b";break;case"f":o+="\f";break;case"v":o+="\v";break;case"8":case"9":o+=a,this.tolerateUnexpectedToken();break;default:if(a&&i.Character.isOctalDigit(a.charCodeAt(0))){var l=this.octalToDecimal(a);n=l.octal||n,o+=String.fromCharCode(l.code)}else o+=a}else{if(i.Character.isLineTerminator(a.charCodeAt(0)))break;o+=a}}return""!==t&&(this.index=e,this.throwUnexpectedToken()),{type:8,value:o,octal:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanTemplate=function(){var e="",t=!1,n=this.index,r="`"===this.source[n],o=!1,a=2;for(++this.index;!this.eof();){var u=this.source[this.index++];if("`"===u){a=1,o=!0,t=!0;break}if("$"===u){if("{"===this.source[this.index]){this.curlyStack.push("${"),++this.index,t=!0;break}e+=u}else if("\\"===u)if(u=this.source[this.index++],i.Character.isLineTerminator(u.charCodeAt(0)))++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(u){case"n":e+="\n";break;case"r":e+="\r";break;case"t":e+="\t";break;case"u":if("{"===this.source[this.index])++this.index,e+=this.scanUnicodeCodePointEscape();else{var c=this.index,l=this.scanHexEscape(u);null!==l?e+=l:(this.index=c,e+=u)}break;case"x":var h=this.scanHexEscape(u);null===h&&this.throwUnexpectedToken(s.Messages.InvalidHexEscapeSequence),e+=h;break;case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:"0"===u?(i.Character.isDecimalDigit(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral),e+="\0"):i.Character.isOctalDigit(u.charCodeAt(0))?this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral):e+=u}else i.Character.isLineTerminator(u.charCodeAt(0))?(++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index,e+="\n"):e+=u}return t||this.throwUnexpectedToken(),r||this.curlyStack.pop(),{type:10,value:this.source.slice(n+1,this.index-a),cooked:e,head:r,tail:o,lineNumber:this.lineNumber,lineStart:this.lineStart,start:n,end:this.index}},e.prototype.testRegExp=function(e,t){var n=e,r=this;t.indexOf("u")>=0&&(n=n.replace(/\\u\{([0-9a-fA-F]+)\}|\\u([a-fA-F0-9]{4})/g,function(e,t,n){var i=parseInt(t||n,16);return i>1114111&&r.throwUnexpectedToken(s.Messages.InvalidRegExp),i<=65535?String.fromCharCode(i):"￿"}).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"￿"));try{RegExp(n)}catch(e){this.throwUnexpectedToken(s.Messages.InvalidRegExp)}try{return new RegExp(e,t)}catch(e){return null}},e.prototype.scanRegExpBody=function(){var e=this.source[this.index];r.assert("/"===e,"Regular expression literal must start with a slash");for(var t=this.source[this.index++],n=!1,o=!1;!this.eof();)if(t+=e=this.source[this.index++],"\\"===e)e=this.source[this.index++],i.Character.isLineTerminator(e.charCodeAt(0))&&this.throwUnexpectedToken(s.Messages.UnterminatedRegExp),t+=e;else if(i.Character.isLineTerminator(e.charCodeAt(0)))this.throwUnexpectedToken(s.Messages.UnterminatedRegExp);else if(n)"]"===e&&(n=!1);else{if("/"===e){o=!0;break}"["===e&&(n=!0)}return o||this.throwUnexpectedToken(s.Messages.UnterminatedRegExp),t.substr(1,t.length-2)},e.prototype.scanRegExpFlags=function(){for(var e="";!this.eof();){var t=this.source[this.index];if(!i.Character.isIdentifierPart(t.charCodeAt(0)))break;if(++this.index,"\\"!==t||this.eof())e+=t;else if("u"===(t=this.source[this.index])){++this.index;var n=this.index,r=this.scanHexEscape("u");if(null!==r)for(e+=r;n<this.index;++n)this.source[n];else this.index=n,e+="u";this.tolerateUnexpectedToken()}else this.tolerateUnexpectedToken()}return e},e.prototype.scanRegExp=function(){var e=this.index,t=this.scanRegExpBody(),n=this.scanRegExpFlags();return{type:9,value:"",pattern:t,flags:n,regex:this.testRegExp(t,n),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.lex=function(){if(this.eof())return{type:2,value:"",lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index};var e=this.source.charCodeAt(this.index);return i.Character.isIdentifierStart(e)?this.scanIdentifier():40===e||41===e||59===e?this.scanPunctuator():39===e||34===e?this.scanStringLiteral():46===e?i.Character.isDecimalDigit(this.source.charCodeAt(this.index+1))?this.scanNumericLiteral():this.scanPunctuator():i.Character.isDecimalDigit(e)?this.scanNumericLiteral():96===e||125===e&&"${"===this.curlyStack[this.curlyStack.length-1]?this.scanTemplate():e>=55296&&e<57343&&i.Character.isIdentifierStart(this.codePointAt(this.index))?this.scanIdentifier():this.scanPunctuator()},e}();t.Scanner=u},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TokenName={},t.TokenName[1]="Boolean",t.TokenName[2]="<end>",t.TokenName[3]="Identifier",t.TokenName[4]="Keyword",t.TokenName[5]="Null",t.TokenName[6]="Numeric",t.TokenName[7]="Punctuator",t.TokenName[8]="String",t.TokenName[9]="RegularExpression",t.TokenName[10]="Template"},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.XHTMLEntities={quot:'"',amp:"&",apos:"'",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",lang:"⟨",rang:"⟩"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(10),i=n(12),s=n(13),o=function(){function e(){this.values=[],this.curly=this.paren=-1}return e.prototype.beforeFunctionExpression=function(e){return["(","{","[","in","typeof","instanceof","new","return","case","delete","throw","void","=","+=","-=","*=","**=","/=","%=","<<=",">>=",">>>=","&=","|=","^=",",","+","-","*","**","/","%","++","--","<<",">>",">>>","&","|","^","!","~","&&","||","?",":","===","==",">=","<=","<",">","!=","!=="].indexOf(e)>=0},e.prototype.isRegexStart=function(){var e=this.values[this.values.length-1],t=null!==e;switch(e){case"this":case"]":t=!1;break;case")":var n=this.values[this.paren-1];t="if"===n||"while"===n||"for"===n||"with"===n;break;case"}":if(t=!1,"function"===this.values[this.curly-3])t=!!(r=this.values[this.curly-4])&&!this.beforeFunctionExpression(r);else if("function"===this.values[this.curly-4]){var r;t=!(r=this.values[this.curly-5])||!this.beforeFunctionExpression(r)}}return t},e.prototype.push=function(e){7===e.type||4===e.type?("{"===e.value?this.curly=this.values.length:"("===e.value&&(this.paren=this.values.length),this.values.push(e.value)):this.values.push(null)},e}(),a=function(){function e(e,t){this.errorHandler=new r.ErrorHandler,this.errorHandler.tolerant=!!t&&"boolean"==typeof t.tolerant&&t.tolerant,this.scanner=new i.Scanner(e,this.errorHandler),this.scanner.trackComment=!!t&&"boolean"==typeof t.comment&&t.comment,this.trackRange=!!t&&"boolean"==typeof t.range&&t.range,this.trackLoc=!!t&&"boolean"==typeof t.loc&&t.loc,this.buffer=[],this.reader=new o}return e.prototype.errors=function(){return this.errorHandler.errors},e.prototype.getNextToken=function(){if(0===this.buffer.length){var e=this.scanner.scanComments();if(this.scanner.trackComment)for(var t=0;t<e.length;++t){var n=e[t],r=this.scanner.source.slice(n.slice[0],n.slice[1]),i={type:n.multiLine?"BlockComment":"LineComment",value:r};this.trackRange&&(i.range=n.range),this.trackLoc&&(i.loc=n.loc),this.buffer.push(i)}if(!this.scanner.eof()){var o=void 0;this.trackLoc&&(o={start:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},end:{}});var a="/"===this.scanner.source[this.scanner.index]&&this.reader.isRegexStart()?this.scanner.scanRegExp():this.scanner.lex();this.reader.push(a);var u={type:s.TokenName[a.type],value:this.scanner.source.slice(a.start,a.end)};if(this.trackRange&&(u.range=[a.start,a.end]),this.trackLoc&&(o.end={line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},u.loc=o),9===a.type){var c=a.pattern,l=a.flags;u.regex={pattern:c,flags:l}}this.buffer.push(u)}}return this.buffer.shift()},e}();t.Tokenizer=a}])},e.exports=t()},1283:e=>{"use strict";e.exports=function(e){if(null===e||"object"!=typeof e)return e;if(e instanceof Object)var t={__proto__:e.__proto__};else t=Object.create(null);return Object.getOwnPropertyNames(e).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}),t}},1398:e=>{"use strict";e.exports=require("vscode")},1573:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:merge",{kind:"scalar",resolve:function(e){return"<<"===e||null===e}})},1621:(e,t,n)=>{"use strict";var r=n(2111),i=n(8725),s=n(6274);function o(e,t,n){var r=[];return e.include.forEach(function(e){n=o(e,t,n)}),e[t].forEach(function(e){n.forEach(function(t,n){t.tag===e.tag&&t.kind===e.kind&&r.push(n)}),n.push(e)}),n.filter(function(e,t){return-1===r.indexOf(t)})}function a(e){this.include=e.include||[],this.implicit=e.implicit||[],this.explicit=e.explicit||[],this.implicit.forEach(function(e){if(e.loadKind&&"scalar"!==e.loadKind)throw new i("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}),this.compiledImplicit=o(this,"implicit",[]),this.compiledExplicit=o(this,"explicit",[]),this.compiledTypeMap=function(){var e,t,n={scalar:{},sequence:{},mapping:{},fallback:{}};function r(e){n[e.kind][e.tag]=n.fallback[e.tag]=e}for(e=0,t=arguments.length;e<t;e+=1)arguments[e].forEach(r);return n}(this.compiledImplicit,this.compiledExplicit)}a.DEFAULT=null,a.create=function(){var e,t;switch(arguments.length){case 1:e=a.DEFAULT,t=arguments[0];break;case 2:e=arguments[0],t=arguments[1];break;default:throw new i("Wrong number of arguments for Schema.create function")}if(e=r.toArray(e),t=r.toArray(t),!e.every(function(e){return e instanceof a}))throw new i("Specified list of super schemas (or a single Schema object) contains a non-Schema object.");if(!t.every(function(e){return e instanceof s}))throw new i("Specified list of YAML types (or a single Type object) contains a non-Type object.");return new a({include:e,explicit:t})},e.exports=a},1929:e=>{"use strict";e.exports=function(e){return"number"==typeof e?e-e===0:"string"==typeof e&&""!==e.trim()&&(Number.isFinite?Number.isFinite(+e):isFinite(+e))}},1995:(e,t,n)=>{var r=n(2203).Stream;e.exports=function(e){return{ReadStream:function t(n,i){if(!(this instanceof t))return new t(n,i);r.call(this);var s=this;this.path=n,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=65536,i=i||{};for(var o=Object.keys(i),a=0,u=o.length;a<u;a++){var c=o[a];this[c]=i[c]}if(this.encoding&&this.setEncoding(this.encoding),void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(void 0===this.end)this.end=1/0;else if("number"!=typeof this.end)throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}null===this.fd?e.open(this.path,this.flags,this.mode,function(e,t){if(e)return s.emit("error",e),void(s.readable=!1);s.fd=t,s.emit("open",t),s._read()}):process.nextTick(function(){s._read()})},WriteStream:function t(n,i){if(!(this instanceof t))return new t(n,i);r.call(this),this.path=n,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,i=i||{};for(var s=Object.keys(i),o=0,a=s.length;o<a;o++){var u=s[o];this[u]=i[u]}if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],null===this.fd&&(this._open=e.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}}},2111:e=>{"use strict";function t(e){return null==e}e.exports.isNothing=t,e.exports.isObject=function(e){return"object"==typeof e&&null!==e},e.exports.toArray=function(e){return Array.isArray(e)?e:t(e)?[]:[e]},e.exports.repeat=function(e,t){var n,r="";for(n=0;n<t;n+=1)r+=e;return r},e.exports.isNegativeZero=function(e){return 0===e&&Number.NEGATIVE_INFINITY===1/e},e.exports.extend=function(e,t){var n,r,i,s;if(t)for(n=0,r=(s=Object.keys(t)).length;n<r;n+=1)e[i=s[n]]=t[i];return e}},2203:e=>{"use strict";e.exports=require("stream")},2470:(e,t,n)=>{"use strict";var r=n(1621);e.exports=new r({include:[n(7913)],implicit:[n(4216),n(6769),n(5112),n(599)]})},2562:e=>{"use strict";e.exports=e=>{if("string"!=typeof e)throw new TypeError("Expected a string, got "+typeof e);return 65279===e.charCodeAt(0)?e.slice(1):e}},2598:(e,t,n)=>{"use strict";const r=n(3720),{MAX_LENGTH:i,CHAR_BACKSLASH:s,CHAR_BACKTICK:o,CHAR_COMMA:a,CHAR_DOT:u,CHAR_LEFT_PARENTHESES:c,CHAR_RIGHT_PARENTHESES:l,CHAR_LEFT_CURLY_BRACE:h,CHAR_RIGHT_CURLY_BRACE:p,CHAR_LEFT_SQUARE_BRACKET:f,CHAR_RIGHT_SQUARE_BRACKET:d,CHAR_DOUBLE_QUOTE:m,CHAR_SINGLE_QUOTE:g,CHAR_NO_BREAK_SPACE:x,CHAR_ZERO_WIDTH_NOBREAK_SPACE:y}=n(8776);e.exports=(e,t={})=>{if("string"!=typeof e)throw new TypeError("Expected a string");const n=t||{},v="number"==typeof n.maxLength?Math.min(i,n.maxLength):i;if(e.length>v)throw new SyntaxError(`Input length (${e.length}), exceeds max characters (${v})`);const E={type:"root",input:e,nodes:[]},C=[E];let D=E,A=E,S=0;const b=e.length;let w,k=0,F=0;const T=()=>e[k++],_=e=>{if("text"===e.type&&"dot"===A.type&&(A.type="text"),!A||"text"!==A.type||"text"!==e.type)return D.nodes.push(e),e.parent=D,e.prev=A,A=e,e;A.value+=e.value};for(_({type:"bos"});k<b;)if(D=C[C.length-1],w=T(),w!==y&&w!==x)if(w!==s)if(w!==d){if(w===f){let e;for(S++;k<b&&(e=T());)if(w+=e,e!==f)if(e!==s){if(e===d&&(S--,0===S))break}else w+=T();else S++;_({type:"text",value:w});continue}if(w!==c)if(w!==l){if(w===m||w===g||w===o){const e=w;let n;for(!0!==t.keepQuotes&&(w="");k<b&&(n=T());)if(n!==s){if(n===e){!0===t.keepQuotes&&(w+=n);break}w+=n}else w+=n+T();_({type:"text",value:w});continue}if(w===h){F++;const e=A.value&&"$"===A.value.slice(-1)||!0===D.dollar;D=_({type:"brace",open:!0,close:!1,dollar:e,depth:F,commas:0,ranges:0,nodes:[]}),C.push(D),_({type:"open",value:w});continue}if(w===p){if("brace"!==D.type){_({type:"text",value:w});continue}const e="close";D=C.pop(),D.close=!0,_({type:e,value:w}),F--,D=C[C.length-1];continue}if(w===a&&F>0){if(D.ranges>0){D.ranges=0;const e=D.nodes.shift();D.nodes=[e,{type:"text",value:r(D)}]}_({type:"comma",value:w}),D.commas++}else{if(w===u&&F>0&&0===D.commas){const e=D.nodes;if(0===F||0===e.length){_({type:"text",value:w});continue}if("dot"===A.type){if(D.range=[],A.value+=w,A.type="range",3!==D.nodes.length&&5!==D.nodes.length){D.invalid=!0,D.ranges=0,A.type="text";continue}D.ranges++,D.args=[];continue}if("range"===A.type){e.pop();const t=e[e.length-1];t.value+=A.value+w,A=t,D.ranges--;continue}_({type:"dot",value:w});continue}_({type:"text",value:w})}}else{if("paren"!==D.type){_({type:"text",value:w});continue}D=C.pop(),_({type:"text",value:w}),D=C[C.length-1]}else D=_({type:"paren",nodes:[]}),C.push(D),_({type:"text",value:w})}else _({type:"text",value:"\\"+w});else _({type:"text",value:(t.keepEscaping?w:"")+T()});do{if(D=C.pop(),"root"!==D.type){D.nodes.forEach(e=>{e.nodes||("open"===e.type&&(e.isOpen=!0),"close"===e.type&&(e.isClose=!0),e.nodes||(e.type="text"),e.invalid=!0)});const e=C[C.length-1],t=e.nodes.indexOf(D);e.nodes.splice(t,1,...D.nodes)}}while(C.length>0);return _({type:"eos"}),E}},2608:(e,t,n)=>{"use strict";const r=n(280),{CHAR_ASTERISK:i,CHAR_AT:s,CHAR_BACKWARD_SLASH:o,CHAR_COMMA:a,CHAR_DOT:u,CHAR_EXCLAMATION_MARK:c,CHAR_FORWARD_SLASH:l,CHAR_LEFT_CURLY_BRACE:h,CHAR_LEFT_PARENTHESES:p,CHAR_LEFT_SQUARE_BRACKET:f,CHAR_PLUS:d,CHAR_QUESTION_MARK:m,CHAR_RIGHT_CURLY_BRACE:g,CHAR_RIGHT_PARENTHESES:x,CHAR_RIGHT_SQUARE_BRACKET:y}=n(3940),v=e=>e===l||e===o,E=e=>{!0!==e.isPrefix&&(e.depth=e.isGlobstar?1/0:1)};e.exports=(e,t)=>{const n=t||{},C=e.length-1,D=!0===n.parts||!0===n.scanToEnd,A=[],S=[],b=[];let w,k,F=e,T=-1,_=0,O=0,P=!1,B=!1,N=!1,I=!1,R=!1,M=!1,L=!1,j=!1,U=!1,H=!1,$=0,J={value:"",depth:0,isGlob:!1};const X=()=>T>=C,z=()=>F.charCodeAt(T+1),K=()=>(w=k,F.charCodeAt(++T));for(;T<C;){let e;if(k=K(),k!==o){if(!0===M||k===h){for($++;!0!==X()&&(k=K());)if(k!==o)if(k!==h){if(!0!==M&&k===u&&(k=K())===u){if(P=J.isBrace=!0,N=J.isGlob=!0,H=!0,!0===D)continue;break}if(!0!==M&&k===a){if(P=J.isBrace=!0,N=J.isGlob=!0,H=!0,!0===D)continue;break}if(k===g&&($--,0===$)){M=!1,P=J.isBrace=!0,H=!0;break}}else $++;else L=J.backslashes=!0,K();if(!0===D)continue;break}if(k!==l){if(!0!==n.noext&&!0==(k===d||k===s||k===i||k===m||k===c)&&z()===p){if(N=J.isGlob=!0,I=J.isExtglob=!0,H=!0,k===c&&T===_&&(U=!0),!0===D){for(;!0!==X()&&(k=K());)if(k!==o){if(k===x){N=J.isGlob=!0,H=!0;break}}else L=J.backslashes=!0,k=K();continue}break}if(k===i){if(w===i&&(R=J.isGlobstar=!0),N=J.isGlob=!0,H=!0,!0===D)continue;break}if(k===m){if(N=J.isGlob=!0,H=!0,!0===D)continue;break}if(k===f){for(;!0!==X()&&(e=K());)if(e!==o){if(e===y){B=J.isBracket=!0,N=J.isGlob=!0,H=!0;break}}else L=J.backslashes=!0,K();if(!0===D)continue;break}if(!0===n.nonegate||k!==c||T!==_){if(!0!==n.noparen&&k===p){if(N=J.isGlob=!0,!0===D){for(;!0!==X()&&(k=K());)if(k!==p){if(k===x){H=!0;break}}else L=J.backslashes=!0,k=K();continue}break}if(!0===N){if(H=!0,!0===D)continue;break}}else j=J.negated=!0,_++}else{if(A.push(T),S.push(J),J={value:"",depth:0,isGlob:!1},!0===H)continue;if(w===u&&T===_+1){_+=2;continue}O=T+1}}else L=J.backslashes=!0,k=K(),k===h&&(M=!0)}!0===n.noext&&(I=!1,N=!1);let q=F,G="",W="";_>0&&(G=F.slice(0,_),F=F.slice(_),O-=_),q&&!0===N&&O>0?(q=F.slice(0,O),W=F.slice(O)):!0===N?(q="",W=F):q=F,q&&""!==q&&"/"!==q&&q!==F&&v(q.charCodeAt(q.length-1))&&(q=q.slice(0,-1)),!0===n.unescape&&(W&&(W=r.removeBackslashes(W)),q&&!0===L&&(q=r.removeBackslashes(q)));const V={prefix:G,input:e,start:_,base:q,glob:W,isBrace:P,isBracket:B,isGlob:N,isExtglob:I,isGlobstar:R,negated:j,negatedExtglob:U};if(!0===n.tokens&&(V.maxDepth=0,v(k)||S.push(J),V.tokens=S),!0===n.parts||!0===n.tokens){let t;for(let r=0;r<A.length;r++){const i=t?t+1:_,s=A[r],o=e.slice(i,s);n.tokens&&(0===r&&0!==_?(S[r].isPrefix=!0,S[r].value=G):S[r].value=o,E(S[r]),V.maxDepth+=S[r].depth),0===r&&""===o||b.push(o),t=s}if(t&&t+1<e.length){const r=e.slice(t+1);b.push(r),n.tokens&&(S[S.length-1].value=r,E(S[S.length-1]),V.maxDepth+=S[S.length-1].depth)}V.slashes=A,V.parts=b}return V}},2613:e=>{"use strict";e.exports=require("assert")},3106:e=>{"use strict";e.exports=require("zlib")},3112:e=>{const t="object"==typeof process&&process&&"win32"===process.platform;e.exports=t?{sep:"\\"}:{sep:"/"}},3360:(e,t,n)=>{"use strict";const r=n(6928),i=n(9997),s=n(7201),o=Symbol("findUp.stop");e.exports=async(e,t={})=>{let n=r.resolve(t.cwd||"");const{root:s}=r.parse(n),a=[].concat(e),u=async t=>{if("function"!=typeof e)return i(a,t);const n=await e(t.cwd);return"string"==typeof n?i([n],t):n};for(;;){const e=await u({...t,cwd:n});if(e===o)return;if(e)return r.resolve(n,e);if(n===s)return;n=r.dirname(n)}},e.exports.sync=(e,t={})=>{let n=r.resolve(t.cwd||"");const{root:s}=r.parse(n),a=[].concat(e),u=t=>{if("function"!=typeof e)return i.sync(a,t);const n=e(t.cwd);return"string"==typeof n?i.sync([n],t):n};for(;;){const e=u({...t,cwd:n});if(e===o)return;if(e)return r.resolve(n,e);if(n===s)return;n=r.dirname(n)}},e.exports.exists=s,e.exports.sync.exists=s.sync,e.exports.stop=o},3649:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.readScripts=void 0;const r=n(5887),i=n(1398);t.readScripts=(e,t=e.getText())=>{let n,s,o,a=!1,u=0;const c=[],l={onError(){},onObjectBegin(){u++},onObjectEnd(t){a&&(s=e.positionAt(t),a=!1),u--},onLiteralValue(t,n,r){o&&"string"==typeof t&&(c.push({...o,value:t,valueRange:new i.Range(e.positionAt(n),e.positionAt(n+r))}),o=void 0)},onObjectProperty(t,r,s){1===u&&"scripts"===t?(a=!0,n=e.positionAt(r)):a&&(o={name:t,nameRange:new i.Range(e.positionAt(r),e.positionAt(r+s))})}};if((0,r.visit)(t,l),void 0!==n)return{location:new i.Location(e.uri,new i.Range(n,s??n)),scripts:c}}},3668:(e,t,n)=>{"use strict";var r=n(6274),i=Object.prototype.hasOwnProperty,s=Object.prototype.toString;e.exports=new r("tag:yaml.org,2002:omap",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,n,r,o,a,u=[],c=e;for(t=0,n=c.length;t<n;t+=1){if(r=c[t],a=!1,"[object Object]"!==s.call(r))return!1;for(o in r)if(i.call(r,o)){if(a)return!1;a=!0}if(!a)return!1;if(-1!==u.indexOf(o))return!1;u.push(o)}return!0},construct:function(e){return null!==e?e:[]}})},3720:(e,t,n)=>{"use strict";const r=n(7612);e.exports=(e,t={})=>{const n=(e,i={})=>{const s=t.escapeInvalid&&r.isInvalidBrace(i),o=!0===e.invalid&&!0===t.escapeInvalid;let a="";if(e.value)return(s||o)&&r.isOpenOrClose(e)?"\\"+e.value:e.value;if(e.value)return e.value;if(e.nodes)for(const t of e.nodes)a+=n(t);return a};return n(e)}},3735:(e,t,n)=>{var r,i,s=n(9896),o=n(9106),a=n(1995),u=n(1283),c=n(9023);function l(e,t){Object.defineProperty(e,r,{get:function(){return t}})}"function"==typeof Symbol&&"function"==typeof Symbol.for?(r=Symbol.for("graceful-fs.queue"),i=Symbol.for("graceful-fs.previous")):(r="___graceful-fs.queue",i="___graceful-fs.previous");var h=function(){};if(c.debuglog?h=c.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(h=function(){var e=c.format.apply(c,arguments);e="GFS4: "+e.split(/\n/).join("\nGFS4: "),console.error(e)}),!s[r]){var p=global[r]||[];l(s,p),s.close=function(e){function t(t,n){return e.call(s,t,function(e){e||m(),"function"==typeof n&&n.apply(this,arguments)})}return Object.defineProperty(t,i,{value:e}),t}(s.close),s.closeSync=function(e){function t(t){e.apply(s,arguments),m()}return Object.defineProperty(t,i,{value:e}),t}(s.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){h(s[r]),n(2613).equal(s[r].length,0)})}function f(e){o(e),e.gracefulify=f,e.createReadStream=function(t,n){return new e.ReadStream(t,n)},e.createWriteStream=function(t,n){return new e.WriteStream(t,n)};var t=e.readFile;e.readFile=function(e,n,r){return"function"==typeof n&&(r=n,n=null),function e(n,r,i){return t(n,r,function(t){!t||"EMFILE"!==t.code&&"ENFILE"!==t.code?("function"==typeof i&&i.apply(this,arguments),m()):d([e,[n,r,i]])})}(e,n,r)};var n=e.writeFile;e.writeFile=function(e,t,r,i){return"function"==typeof r&&(i=r,r=null),function e(t,r,i,s){return n(t,r,i,function(n){!n||"EMFILE"!==n.code&&"ENFILE"!==n.code?("function"==typeof s&&s.apply(this,arguments),m()):d([e,[t,r,i,s]])})}(e,t,r,i)};var r=e.appendFile;r&&(e.appendFile=function(e,t,n,i){return"function"==typeof n&&(i=n,n=null),function e(t,n,i,s){return r(t,n,i,function(r){!r||"EMFILE"!==r.code&&"ENFILE"!==r.code?("function"==typeof s&&s.apply(this,arguments),m()):d([e,[t,n,i,s]])})}(e,t,n,i)});var i=e.readdir;function s(t){return i.apply(e,t)}if(e.readdir=function(e,t,n){var r=[e];return"function"!=typeof t?r.push(t):n=t,r.push(function(e,t){t&&t.sort&&t.sort(),!e||"EMFILE"!==e.code&&"ENFILE"!==e.code?("function"==typeof n&&n.apply(this,arguments),m()):d([s,[r]])}),s(r)},"v0.8"===process.version.substr(0,4)){var u=a(e);g=u.ReadStream,x=u.WriteStream}var c=e.ReadStream;c&&(g.prototype=Object.create(c.prototype),g.prototype.open=function(){var e=this;v(e.path,e.flags,e.mode,function(t,n){t?(e.autoClose&&e.destroy(),e.emit("error",t)):(e.fd=n,e.emit("open",n),e.read())})});var l=e.WriteStream;l&&(x.prototype=Object.create(l.prototype),x.prototype.open=function(){var e=this;v(e.path,e.flags,e.mode,function(t,n){t?(e.destroy(),e.emit("error",t)):(e.fd=n,e.emit("open",n))})}),Object.defineProperty(e,"ReadStream",{get:function(){return g},set:function(e){g=e},enumerable:!0,configurable:!0}),Object.defineProperty(e,"WriteStream",{get:function(){return x},set:function(e){x=e},enumerable:!0,configurable:!0});var h=g;Object.defineProperty(e,"FileReadStream",{get:function(){return h},set:function(e){h=e},enumerable:!0,configurable:!0});var p=x;function g(e,t){return this instanceof g?(c.apply(this,arguments),this):g.apply(Object.create(g.prototype),arguments)}function x(e,t){return this instanceof x?(l.apply(this,arguments),this):x.apply(Object.create(x.prototype),arguments)}Object.defineProperty(e,"FileWriteStream",{get:function(){return p},set:function(e){p=e},enumerable:!0,configurable:!0});var y=e.open;function v(e,t,n,r){return"function"==typeof n&&(r=n,n=null),function e(t,n,r,i){return y(t,n,r,function(s,o){!s||"EMFILE"!==s.code&&"ENFILE"!==s.code?("function"==typeof i&&i.apply(this,arguments),m()):d([e,[t,n,r,i]])})}(e,t,n,r)}return e.open=v,e}function d(e){h("ENQUEUE",e[0].name,e[1]),s[r].push(e)}function m(){var e=s[r].shift();e&&(h("RETRY",e[0].name,e[1]),e[0].apply(null,e[1]))}global[r]||l(global,s[r]),e.exports=f(u(s)),process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!s.__patched&&(e.exports=f(s),s.__patched=!0)},3940:(e,t,n)=>{"use strict";const r=n(6928),i="\\\\/",s=`[^${i}]`,o="\\.",a="\\/",u="[^/]",c=`(?:${a}|$)`,l=`(?:^|${a})`,h=`${o}{1,2}${c}`,p={DOT_LITERAL:o,PLUS_LITERAL:"\\+",QMARK_LITERAL:"\\?",SLASH_LITERAL:a,ONE_CHAR:"(?=.)",QMARK:u,END_ANCHOR:c,DOTS_SLASH:h,NO_DOT:`(?!${o})`,NO_DOTS:`(?!${l}${h})`,NO_DOT_SLASH:`(?!${o}{0,1}${c})`,NO_DOTS_SLASH:`(?!${h})`,QMARK_NO_DOT:`[^.${a}]`,STAR:`${u}*?`,START_ANCHOR:l},f={...p,SLASH_LITERAL:`[${i}]`,QMARK:s,STAR:`${s}*?`,DOTS_SLASH:`${o}{1,2}(?:[${i}]|$)`,NO_DOT:`(?!${o})`,NO_DOTS:`(?!(?:^|[${i}])${o}{1,2}(?:[${i}]|$))`,NO_DOT_SLASH:`(?!${o}{0,1}(?:[${i}]|$))`,NO_DOTS_SLASH:`(?!${o}{1,2}(?:[${i}]|$))`,QMARK_NO_DOT:`[^.${i}]`,START_ANCHOR:`(?:^|[${i}])`,END_ANCHOR:`(?:[${i}]|$)`};e.exports={MAX_LENGTH:65536,POSIX_REGEX_SOURCE:{alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"},REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:r.sep,extglobChars:e=>({"!":{type:"negate",open:"(?:(?!(?:",close:`))${e.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}),globChars:e=>!0===e?f:p}},3977:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fromNow=function e(t,n,l,h){"number"!=typeof t&&(t=t.getTime());const p=Math.round(((new Date).getTime()-t)/1e3);if(p<-30)return r.l10n.t("in {0}",e((new Date).getTime()+1e3*p,!1));if(!h&&p<30)return r.l10n.t("now");let f;return p<i?(f=p,n?1===f?l?r.l10n.t("{0} second ago",f):r.l10n.t("{0} sec ago",f):l?r.l10n.t("{0} seconds ago",f):r.l10n.t("{0} secs ago",f):1===f?l?r.l10n.t("{0} second",f):r.l10n.t("{0} sec",f):l?r.l10n.t("{0} seconds",f):r.l10n.t("{0} secs",f)):p<s?(f=Math.floor(p/i),n?1===f?l?r.l10n.t("{0} minute ago",f):r.l10n.t("{0} min ago",f):l?r.l10n.t("{0} minutes ago",f):r.l10n.t("{0} mins ago",f):1===f?l?r.l10n.t("{0} minute",f):r.l10n.t("{0} min",f):l?r.l10n.t("{0} minutes",f):r.l10n.t("{0} mins",f)):p<o?(f=Math.floor(p/s),n?1===f?l?r.l10n.t("{0} hour ago",f):r.l10n.t("{0} hr ago",f):l?r.l10n.t("{0} hours ago",f):r.l10n.t("{0} hrs ago",f):1===f?l?r.l10n.t("{0} hour",f):r.l10n.t("{0} hr",f):l?r.l10n.t("{0} hours",f):r.l10n.t("{0} hrs",f)):p<a?(f=Math.floor(p/o),n?1===f?r.l10n.t("{0} day ago",f):r.l10n.t("{0} days ago",f):1===f?r.l10n.t("{0} day",f):r.l10n.t("{0} days",f)):p<u?(f=Math.floor(p/a),n?1===f?l?r.l10n.t("{0} week ago",f):r.l10n.t("{0} wk ago",f):l?r.l10n.t("{0} weeks ago",f):r.l10n.t("{0} wks ago",f):1===f?l?r.l10n.t("{0} week",f):r.l10n.t("{0} wk",f):l?r.l10n.t("{0} weeks",f):r.l10n.t("{0} wks",f)):p<c?(f=Math.floor(p/u),n?1===f?l?r.l10n.t("{0} month ago",f):r.l10n.t("{0} mo ago",f):l?r.l10n.t("{0} months ago",f):r.l10n.t("{0} mos ago",f):1===f?l?r.l10n.t("{0} month",f):r.l10n.t("{0} mo",f):l?r.l10n.t("{0} months",f):r.l10n.t("{0} mos",f)):(f=Math.floor(p/c),n?1===f?l?r.l10n.t("{0} year ago",f):r.l10n.t("{0} yr ago",f):l?r.l10n.t("{0} years ago",f):r.l10n.t("{0} yrs ago",f):1===f?l?r.l10n.t("{0} year",f):r.l10n.t("{0} yr",f):l?r.l10n.t("{0} years",f):r.l10n.t("{0} yrs",f))};const r=n(1398),i=60,s=60*i,o=24*s,a=7*o,u=30*o,c=365*o},4027:(e,t,n)=>{const r=e.exports=(e,t,n={})=>(g(t),!(!n.nocomment&&"#"===t.charAt(0))&&new E(t,n).match(e));e.exports=r;const i=n(3112);r.sep=i.sep;const s=Symbol("globstar **");r.GLOBSTAR=s;const o=n(8928),a={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},u="[^/]",c=u+"*?",l=e=>e.split("").reduce((e,t)=>(e[t]=!0,e),{}),h=l("().*{}+?[]^$\\!"),p=l("[.("),f=/\/+/;r.filter=(e,t={})=>(n,i,s)=>r(n,e,t);const d=(e,t={})=>{const n={};return Object.keys(e).forEach(t=>n[t]=e[t]),Object.keys(t).forEach(e=>n[e]=t[e]),n};r.defaults=e=>{if(!e||"object"!=typeof e||!Object.keys(e).length)return r;const t=r,n=(n,r,i)=>t(n,r,d(e,i));return(n.Minimatch=class extends t.Minimatch{constructor(t,n){super(t,d(e,n))}}).defaults=n=>t.defaults(d(e,n)).Minimatch,n.filter=(n,r)=>t.filter(n,d(e,r)),n.defaults=n=>t.defaults(d(e,n)),n.makeRe=(n,r)=>t.makeRe(n,d(e,r)),n.braceExpand=(n,r)=>t.braceExpand(n,d(e,r)),n.match=(n,r,i)=>t.match(n,r,d(e,i)),n},r.braceExpand=(e,t)=>m(e,t);const m=(e,t={})=>(g(e),t.nobrace||!/\{(?:(?!\{).)*\}/.test(e)?[e]:o(e)),g=e=>{if("string"!=typeof e)throw new TypeError("invalid pattern");if(e.length>65536)throw new TypeError("pattern is too long")},x=Symbol("subparse");r.makeRe=(e,t)=>new E(e,t||{}).makeRe(),r.match=(e,t,n={})=>{const r=new E(t,n);return e=e.filter(e=>r.match(e)),r.options.nonull&&!e.length&&e.push(t),e};const y=e=>e.replace(/\\([^-\]])/g,"$1"),v=e=>e.replace(/[[\]\\]/g,"\\$&");class E{constructor(e,t){g(e),t||(t={}),this.options=t,this.set=[],this.pattern=e,this.windowsPathsNoEscape=!!t.windowsPathsNoEscape||!1===t.allowWindowsEscape,this.windowsPathsNoEscape&&(this.pattern=this.pattern.replace(/\\/g,"/")),this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!t.partial,this.make()}debug(){}make(){const e=this.pattern,t=this.options;if(!t.nocomment&&"#"===e.charAt(0))return void(this.comment=!0);if(!e)return void(this.empty=!0);this.parseNegate();let n=this.globSet=this.braceExpand();t.debug&&(this.debug=(...e)=>console.error(...e)),this.debug(this.pattern,n),n=this.globParts=n.map(e=>e.split(f)),this.debug(this.pattern,n),n=n.map((e,t,n)=>e.map(this.parse,this)),this.debug(this.pattern,n),n=n.filter(e=>-1===e.indexOf(!1)),this.debug(this.pattern,n),this.set=n}parseNegate(){if(this.options.nonegate)return;const e=this.pattern;let t=!1,n=0;for(let r=0;r<e.length&&"!"===e.charAt(r);r++)t=!t,n++;n&&(this.pattern=e.slice(n)),this.negate=t}matchOne(e,t,n){var r=this.options;this.debug("matchOne",{this:this,file:e,pattern:t}),this.debug("matchOne",e.length,t.length);for(var i=0,o=0,a=e.length,u=t.length;i<a&&o<u;i++,o++){this.debug("matchOne loop");var c,l=t[o],h=e[i];if(this.debug(t,l,h),!1===l)return!1;if(l===s){this.debug("GLOBSTAR",[t,l,h]);var p=i,f=o+1;if(f===u){for(this.debug("** at the end");i<a;i++)if("."===e[i]||".."===e[i]||!r.dot&&"."===e[i].charAt(0))return!1;return!0}for(;p<a;){var d=e[p];if(this.debug("\nglobstar while",e,p,t,f,d),this.matchOne(e.slice(p),t.slice(f),n))return this.debug("globstar found match!",p,a,d),!0;if("."===d||".."===d||!r.dot&&"."===d.charAt(0)){this.debug("dot detected!",e,p,t,f);break}this.debug("globstar swallow a segment, and continue"),p++}return!(!n||(this.debug("\n>>> no match, partial?",e,p,t,f),p!==a))}if("string"==typeof l?(c=h===l,this.debug("string match",l,h,c)):(c=h.match(l),this.debug("pattern match",l,h,c)),!c)return!1}if(i===a&&o===u)return!0;if(i===a)return n;if(o===u)return i===a-1&&""===e[i];throw new Error("wtf?")}braceExpand(){return m(this.pattern,this.options)}parse(e,t){g(e);const n=this.options;if("**"===e){if(!n.noglobstar)return s;e="*"}if(""===e)return"";let r="",i=!1,o=!1;const l=[],f=[];let d,m,E,C,D=!1,A=-1,S=-1,b="."===e.charAt(0),w=n.dot||b;const k=e=>"."===e.charAt(0)?"":n.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",F=()=>{if(d){switch(d){case"*":r+=c,i=!0;break;case"?":r+=u,i=!0;break;default:r+="\\"+d}this.debug("clearStateChar %j %j",d,r),d=!1}};for(let t,s=0;s<e.length&&(t=e.charAt(s));s++)if(this.debug("%s\t%s %s %j",e,s,r,t),o){if("/"===t)return!1;h[t]&&(r+="\\"),r+=t,o=!1}else switch(t){case"/":return!1;case"\\":if(D&&"-"===e.charAt(s+1)){r+=t;continue}F(),o=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s\t%s %s %j <-- stateChar",e,s,r,t),D){this.debug("  in class"),"!"===t&&s===S+1&&(t="^"),r+=t;continue}this.debug("call clearStateChar %j",d),F(),d=t,n.noext&&F();continue;case"(":{if(D){r+="(";continue}if(!d){r+="\\(";continue}const t={type:d,start:s-1,reStart:r.length,open:a[d].open,close:a[d].close};this.debug(this.pattern,"\t",t),l.push(t),r+=t.open,0===t.start&&"!"!==t.type&&(b=!0,r+=k(e.slice(s+1))),this.debug("plType %j %j",d,r),d=!1;continue}case")":{const e=l[l.length-1];if(D||!e){r+="\\)";continue}l.pop(),F(),i=!0,E=e,r+=E.close,"!"===E.type&&f.push(Object.assign(E,{reEnd:r.length}));continue}case"|":{const t=l[l.length-1];if(D||!t){r+="\\|";continue}F(),r+="|",0===t.start&&"!"!==t.type&&(b=!0,r+=k(e.slice(s+1)));continue}case"[":if(F(),D){r+="\\"+t;continue}D=!0,S=s,A=r.length,r+=t;continue;case"]":if(s===S+1||!D){r+="\\"+t;continue}m=e.substring(S+1,s);try{RegExp("["+v(y(m))+"]"),r+=t}catch(e){r=r.substring(0,A)+"(?:$.)"}i=!0,D=!1;continue;default:F(),!h[t]||"^"===t&&D||(r+="\\"),r+=t}for(D&&(m=e.slice(S+1),C=this.parse(m,x),r=r.substring(0,A)+"\\["+C[0],i=i||C[1]),E=l.pop();E;E=l.pop()){let e;e=r.slice(E.reStart+E.open.length),this.debug("setting tail",r,E),e=e.replace(/((?:\\{2}){0,64})(\\?)\|/g,(e,t,n)=>(n||(n="\\"),t+t+n+"|")),this.debug("tail=%j\n   %s",e,e,E,r);const t="*"===E.type?c:"?"===E.type?u:"\\"+E.type;i=!0,r=r.slice(0,E.reStart)+t+"\\("+e}F(),o&&(r+="\\\\");const T=p[r.charAt(0)];for(let e=f.length-1;e>-1;e--){const n=f[e],i=r.slice(0,n.reStart),s=r.slice(n.reStart,n.reEnd-8);let o=r.slice(n.reEnd);const a=r.slice(n.reEnd-8,n.reEnd)+o,u=i.split(")").length,c=i.split("(").length-u;let l=o;for(let e=0;e<c;e++)l=l.replace(/\)[+*?]?/,"");o=l,r=i+s+o+(""===o&&t!==x?"(?:$|\\/)":"")+a}if(""!==r&&i&&(r="(?=.)"+r),T&&(r=(b?"":w?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)")+r),t===x)return[r,i];if(n.nocase&&!i&&(i=e.toUpperCase()!==e.toLowerCase()),!i)return e.replace(/\\(.)/g,"$1");const _=n.nocase?"i":"";try{return Object.assign(new RegExp("^"+r+"$",_),{_glob:e,_src:r})}catch(e){return new RegExp("$.")}}makeRe(){if(this.regexp||!1===this.regexp)return this.regexp;const e=this.set;if(!e.length)return this.regexp=!1,this.regexp;const t=this.options,n=t.noglobstar?c:t.dot?"(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?":"(?:(?!(?:\\/|^)\\.).)*?",r=t.nocase?"i":"";let i=e.map(e=>(e=e.map(e=>"string"==typeof e?e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):e===s?s:e._src).reduce((e,t)=>(e[e.length-1]===s&&t===s||e.push(t),e),[]),e.forEach((t,r)=>{t===s&&e[r-1]!==s&&(0===r?e.length>1?e[r+1]="(?:\\/|"+n+"\\/)?"+e[r+1]:e[r]=n:r===e.length-1?e[r-1]+="(?:\\/|"+n+")?":(e[r-1]+="(?:\\/|\\/"+n+"\\/)"+e[r+1],e[r+1]=s))}),e.filter(e=>e!==s).join("/"))).join("|");i="^(?:"+i+")$",this.negate&&(i="^(?!"+i+").*$");try{this.regexp=new RegExp(i,r)}catch(e){this.regexp=!1}return this.regexp}match(e,t=this.partial){if(this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return""===e;if("/"===e&&t)return!0;const n=this.options;"/"!==i.sep&&(e=e.split(i.sep).join("/")),e=e.split(f),this.debug(this.pattern,"split",e);const r=this.set;let s;this.debug(this.pattern,"set",r);for(let t=e.length-1;t>=0&&(s=e[t],!s);t--);for(let i=0;i<r.length;i++){const o=r[i];let a=e;if(n.matchBase&&1===o.length&&(a=[s]),this.matchOne(a,o,t))return!!n.flipNegate||!this.negate}return!n.flipNegate&&this.negate}static defaults(e){return r.defaults(e).Minimatch}}r.Minimatch=E},4124:(e,t,n)=>{"use strict";const r=n(3735),i=n(6867),s=n(2562),o=n(7210),a=e=>o.safeLoad(s(e));e.exports=e=>i(r.readFile)(e,"utf8").then(e=>a(e)),e.exports.sync=e=>a(r.readFileSync(e,"utf8"))},4216:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:null",{kind:"scalar",resolve:function(e){if(null===e)return!0;var t=e.length;return 1===t&&"~"===e||4===t&&("null"===e||"Null"===e||"NULL"===e)},construct:function(){return null},predicate:function(e){return null===e},represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"}},defaultStyle:"lowercase"})},4376:(e,t,n)=>{"use strict";var r=n(9001),i=n(7943);function s(e){return function(){throw new Error("Function "+e+" is deprecated and cannot be used.")}}e.exports.Type=n(6274),e.exports.Schema=n(1621),e.exports.FAILSAFE_SCHEMA=n(7913),e.exports.JSON_SCHEMA=n(2470),e.exports.CORE_SCHEMA=n(5491),e.exports.DEFAULT_SAFE_SCHEMA=n(919),e.exports.DEFAULT_FULL_SCHEMA=n(7459),e.exports.load=r.load,e.exports.loadAll=r.loadAll,e.exports.safeLoad=r.safeLoad,e.exports.safeLoadAll=r.safeLoadAll,e.exports.dump=i.dump,e.exports.safeDump=i.safeDump,e.exports.YAMLException=n(8725),e.exports.MINIMAL_SCHEMA=n(7913),e.exports.SAFE_SCHEMA=n(919),e.exports.DEFAULT_SCHEMA=n(7459),e.exports.scan=s("scan"),e.exports.parse=s("parse"),e.exports.compose=s("compose"),e.exports.addConstructor=s("addConstructor")},4434:e=>{"use strict";e.exports=require("events")},4459:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&i(t,e,n[o]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.runSelectedScript=function(e){const t=a.window.activeTextEditor;if(!t)return;const n=t.document,r=n.getText(),i=(0,u.findScriptAtPosition)(t.document,r,t.selection.anchor);if(i)(0,u.runScript)(e,i,n);else{const e=a.l10n.t("Could not find a valid npm script at the selection.");a.window.showErrorMessage(e)}},t.selectAndRunScriptFromFolder=async function(e,t){if(0===t.length)return;const n=t[0],r=await(0,u.detectNpmScriptsForFolder)(e,n);if(r&&r.length>0){const e=a.window.createQuickPick();e.placeholder="Select an npm script to run in folder",e.items=r;const t=[],n=new Promise(n=>{t.push(e.onDidAccept(()=>{t.forEach(e=>e.dispose()),n(e.selectedItems[0])})),t.push(e.onDidHide(()=>{t.forEach(e=>e.dispose()),n(void 0)}))});e.show();const i=await n;e.dispose(),i&&a.tasks.executeTask(i.task)}else a.window.showInformationMessage(`No npm scripts found in ${n.fsPath}`,{modal:!0})};const a=o(n(1398)),u=n(7511)},4537:(e,t,n)=>{"use strict";const r=n(3720),i=n(8712),s=n(8697),o=n(2598),a=(e,t={})=>{let n=[];if(Array.isArray(e))for(const r of e){const e=a.create(r,t);Array.isArray(e)?n.push(...e):n.push(e)}else n=[].concat(a.create(e,t));return t&&!0===t.expand&&!0===t.nodupes&&(n=[...new Set(n)]),n};a.parse=(e,t={})=>o(e,t),a.stringify=(e,t={})=>r("string"==typeof e?a.parse(e,t):e,t),a.compile=(e,t={})=>("string"==typeof e&&(e=a.parse(e,t)),i(e,t)),a.expand=(e,t={})=>{"string"==typeof e&&(e=a.parse(e,t));let n=s(e,t);return!0===t.noempty&&(n=n.filter(Boolean)),!0===t.nodupes&&(n=[...new Set(n)]),n},a.create=(e,t={})=>""===e||e.length<3?[e]:!0!==t.expand?a.compile(e,t):a.expand(e,t),e.exports=a},4693:e=>{class t{constructor(e){this.value=e,this.next=void 0}}class n{constructor(){this.clear()}enqueue(e){const n=new t(e);this._head?(this._tail.next=n,this._tail=n):(this._head=n,this._tail=n),this._size++}dequeue(){const e=this._head;if(e)return this._head=this._head.next,this._size--,e.value}clear(){this._head=void 0,this._tail=void 0,this._size=0}get size(){return this._size}*[Symbol.iterator](){let e=this._head;for(;e;)yield e.value,e=e.next}}e.exports=n},4756:e=>{"use strict";e.exports=require("tls")},5090:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return null!==e?e:[]}})},5095:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&i(t,e,n[o]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.NpmScriptLensProvider=void 0;const a=o(n(6928)),u=n(1398),c=n(3649),l=n(7511),h=()=>u.workspace.getConfiguration().get("debug.javascript.codelens.npmScripts");t.NpmScriptLensProvider=class{constructor(){this.lensLocation=h(),this.changeEmitter=new u.EventEmitter,this.subscriptions=[],this.onDidChangeCodeLenses=this.changeEmitter.event,this.subscriptions.push(this.changeEmitter,u.workspace.onDidChangeConfiguration(e=>{e.affectsConfiguration("debug.javascript.codelens.npmScripts")&&(this.lensLocation=h(),this.changeEmitter.fire())}),u.languages.registerCodeLensProvider({language:"json",pattern:"**/package.json"},this))}async provideCodeLenses(e){if("never"===this.lensLocation)return[];const t=(0,c.readScripts)(e);if(!t)return[];const n="$(debug-start) "+u.l10n.t("Debug"),r=a.dirname(e.uri.fsPath);if("top"===this.lensLocation)return[new u.CodeLens(t.location.range,{title:n,command:"extension.js-debug.npmScript",arguments:[r]})];if("all"===this.lensLocation){const i=u.Uri.joinPath(e.uri,"..");return Promise.all(t.scripts.map(async({name:t,nameRange:s})=>{const o=await(0,l.getRunScriptCommand)(t,i);return new u.CodeLens(s,{title:n,command:"extension.js-debug.createDebuggerTerminal",arguments:[o.join(" "),u.workspace.getWorkspaceFolder(e.uri),{cwd:r}]})}))}return[]}dispose(){this.subscriptions.forEach(e=>e.dispose())}}},5112:(e,t,n)=>{"use strict";var r=n(2111),i=n(6274);function s(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function o(e){return 48<=e&&e<=55}function a(e){return 48<=e&&e<=57}e.exports=new i("tag:yaml.org,2002:int",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t,n=e.length,r=0,i=!1;if(!n)return!1;if("-"!==(t=e[r])&&"+"!==t||(t=e[++r]),"0"===t){if(r+1===n)return!0;if("b"===(t=e[++r])){for(r++;r<n;r++)if("_"!==(t=e[r])){if("0"!==t&&"1"!==t)return!1;i=!0}return i&&"_"!==t}if("x"===t){for(r++;r<n;r++)if("_"!==(t=e[r])){if(!s(e.charCodeAt(r)))return!1;i=!0}return i&&"_"!==t}for(;r<n;r++)if("_"!==(t=e[r])){if(!o(e.charCodeAt(r)))return!1;i=!0}return i&&"_"!==t}if("_"===t)return!1;for(;r<n;r++)if("_"!==(t=e[r])){if(":"===t)break;if(!a(e.charCodeAt(r)))return!1;i=!0}return!(!i||"_"===t)&&(":"!==t||/^(:[0-5]?[0-9])+$/.test(e.slice(r)))},construct:function(e){var t,n,r=e,i=1,s=[];return-1!==r.indexOf("_")&&(r=r.replace(/_/g,"")),"-"!==(t=r[0])&&"+"!==t||("-"===t&&(i=-1),t=(r=r.slice(1))[0]),"0"===r?0:"0"===t?"b"===r[1]?i*parseInt(r.slice(2),2):"x"===r[1]?i*parseInt(r,16):i*parseInt(r,8):-1!==r.indexOf(":")?(r.split(":").forEach(function(e){s.unshift(parseInt(e,10))}),r=0,n=1,s.forEach(function(e){r+=e*n,n*=60}),i*r):i*parseInt(r,10)},predicate:function(e){return"[object Number]"===Object.prototype.toString.call(e)&&e%1==0&&!r.isNegativeZero(e)},represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0"+e.toString(8):"-0"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},5157:(e,t,n)=>{"use strict";e.exports=n(6675)},5317:e=>{"use strict";e.exports=require("child_process")},5491:(e,t,n)=>{"use strict";var r=n(1621);e.exports=new r({include:[n(2470)]})},5692:e=>{"use strict";e.exports=require("https")},5887:(e,t,n)=>{"use strict";function r(e,t=!1){const n=e.length;let r=0,a="",u=0,c=16,l=0,h=0,p=0,f=0,d=0;function m(t,n){let i=0,s=0;for(;i<t||!n;){let t=e.charCodeAt(r);if(t>=48&&t<=57)s=16*s+t-48;else if(t>=65&&t<=70)s=16*s+t-65+10;else{if(!(t>=97&&t<=102))break;s=16*s+t-97+10}r++,i++}return i<t&&(s=-1),s}function g(){if(a="",d=0,u=r,h=l,f=p,r>=n)return u=n,c=17;let t=e.charCodeAt(r);if(i(t)){do{r++,a+=String.fromCharCode(t),t=e.charCodeAt(r)}while(i(t));return c=15}if(s(t))return r++,a+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,a+="\n"),l++,p=r,c=14;switch(t){case 123:return r++,c=1;case 125:return r++,c=2;case 91:return r++,c=3;case 93:return r++,c=4;case 58:return r++,c=6;case 44:return r++,c=5;case 34:return r++,a=function(){let t="",i=r;for(;;){if(r>=n){t+=e.substring(i,r),d=2;break}const o=e.charCodeAt(r);if(34===o){t+=e.substring(i,r),r++;break}if(92!==o){if(o>=0&&o<=31){if(s(o)){t+=e.substring(i,r),d=2;break}d=6}r++}else{if(t+=e.substring(i,r),r++,r>=n){d=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:const e=m(4,!0);e>=0?t+=String.fromCharCode(e):d=4;break;default:d=5}i=r}}return t}(),c=10;case 47:const i=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!s(e.charCodeAt(r));)r++;return a=e.substring(i,r),c=12}if(42===e.charCodeAt(r+1)){r+=2;const t=n-1;let o=!1;for(;r<t;){const t=e.charCodeAt(r);if(42===t&&47===e.charCodeAt(r+1)){r+=2,o=!0;break}r++,s(t)&&(13===t&&10===e.charCodeAt(r)&&r++,l++,p=r)}return o||(r++,d=1),a=e.substring(i,r),c=13}return a+=String.fromCharCode(t),r++,c=16;case 45:if(a+=String.fromCharCode(t),r++,r===n||!o(e.charCodeAt(r)))return c=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return a+=function(){let t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&o(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(r++,!(r<e.length&&o(e.charCodeAt(r))))return d=3,e.substring(t,r);for(r++;r<e.length&&o(e.charCodeAt(r));)r++}let n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if(r++,(r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&o(e.charCodeAt(r))){for(r++;r<e.length&&o(e.charCodeAt(r));)r++;n=r}else d=3;return e.substring(t,n)}(),c=11;default:for(;r<n&&x(t);)r++,t=e.charCodeAt(r);if(u!==r){switch(a=e.substring(u,r),a){case"true":return c=8;case"false":return c=9;case"null":return c=7}return c=16}return a+=String.fromCharCode(t),r++,c=16}}function x(e){if(i(e)||s(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,a="",u=0,c=16,d=0},getPosition:()=>r,scan:t?function(){let e;do{e=g()}while(e>=12&&e<=15);return e}:g,getToken:()=>c,getTokenValue:()=>a,getTokenOffset:()=>u,getTokenLength:()=>r-u,getTokenStartLine:()=>h,getTokenStartCharacter:()=>u-f,getTokenError:()=>d}}function i(e){return 32===e||9===e}function s(e){return 10===e||13===e}function o(e){return e>=48&&e<=57}var a,u;function c(e,t,n){let i,s,o,a,u;if(t){for(a=t.offset,u=a+t.length,o=a;o>0&&!h(e,o-1);)o--;let r=u;for(;r<e.length&&!h(e,r);)r++;s=e.substring(o,r),i=function(e,t){let n=0,r=0;const i=t.tabSize||4;for(;n<e.length;){let t=e.charAt(n);if(" "===t)r++;else{if("\t"!==t)break;r+=i}n++}return Math.floor(r/i)}(s,n)}else s=e,i=0,o=0,a=0,u=e.length;const c=function(e,t){for(let e=0;e<t.length;e++){const n=t.charAt(e);if("\r"===n)return e+1<t.length&&"\n"===t.charAt(e+1)?"\r\n":"\r";if("\n"===n)return"\n"}return e&&e.eol||"\n"}(n,e);let p,f=0,d=0;p=n.insertSpaces?l(" ",n.tabSize||4):"\t";let m=r(s,!1),g=!1;function x(){return f>1?l(c,f)+l(p,i+d):c+l(p,i+d)}function y(){let e=m.scan();for(f=0;15===e||14===e;)14===e&&n.keepLines?f+=1:14===e&&(f=1),e=m.scan();return g=16===e||0!==m.getTokenError(),e}const v=[];function E(n,r,i){g||t&&!(r<u&&i>a)||e.substring(r,i)===n||v.push({offset:r,length:i-r,content:n})}let C=y();if(n.keepLines&&f>0&&E(l(c,f),0,0),17!==C){let e=m.getTokenOffset()+o;E(l(p,i),o,e)}for(;17!==C;){let e=m.getTokenOffset()+m.getTokenLength()+o,t=y(),r="",i=!1;for(;0===f&&(12===t||13===t);)E(" ",e,m.getTokenOffset()+o),e=m.getTokenOffset()+m.getTokenLength()+o,i=12===t,r=i?x():"",t=y();if(2===t)1!==C&&d--,n.keepLines&&f>0||!n.keepLines&&1!==C?r=x():n.keepLines&&(r=" ");else if(4===t)3!==C&&d--,n.keepLines&&f>0||!n.keepLines&&3!==C?r=x():n.keepLines&&(r=" ");else{switch(C){case 3:case 1:d++,r=n.keepLines&&f>0||!n.keepLines?x():" ";break;case 5:r=n.keepLines&&f>0||!n.keepLines?x():" ";break;case 12:r=x();break;case 13:f>0?r=x():i||(r=" ");break;case 6:n.keepLines&&f>0?r=x():i||(r=" ");break;case 10:n.keepLines&&f>0?r=x():6!==t||i||(r="");break;case 7:case 8:case 9:case 11:case 2:case 4:n.keepLines&&f>0?r=x():12!==t&&13!==t||i?5!==t&&17!==t&&(g=!0):r=" ";break;case 16:g=!0}f>0&&(12===t||13===t)&&(r=x())}17===t&&(r=n.keepLines&&f>0?x():n.insertFinalNewline?c:""),E(r,e,m.getTokenOffset()+o),C=t}return v}function l(e,t){let n="";for(let r=0;r<t;r++)n+=e;return n}function h(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}function p(e,t=[],n=u.DEFAULT){let r={type:"array",offset:-1,length:-1,children:[],parent:void 0};function i(e){"property"===r.type&&(r.length=e-r.offset,r=r.parent)}function s(e){return r.children.push(e),e}d(e,{onObjectBegin:e=>{r=s({type:"object",offset:e,length:-1,parent:r,children:[]})},onObjectProperty:(e,t,n)=>{r=s({type:"property",offset:t,length:-1,parent:r,children:[]}),r.children.push({type:"string",value:e,offset:t,length:n,parent:r})},onObjectEnd:(e,t)=>{i(e+t),r.length=e+t-r.offset,r=r.parent,i(e+t)},onArrayBegin:(e,t)=>{r=s({type:"array",offset:e,length:-1,parent:r,children:[]})},onArrayEnd:(e,t)=>{r.length=e+t-r.offset,r=r.parent,i(e+t)},onLiteralValue:(e,t,n)=>{s({type:m(e),offset:t,length:n,parent:r,value:e}),i(t+n)},onSeparator:(e,t,n)=>{"property"===r.type&&(":"===e?r.colonOffset=t:","===e&&i(t))},onError:(e,n,r)=>{t.push({error:e,offset:n,length:r})}},n);const o=r.children[0];return o&&delete o.parent,o}function f(e,t){if(!e)return;let n=e;for(let e of t)if("string"==typeof e){if("object"!==n.type||!Array.isArray(n.children))return;let t=!1;for(const r of n.children)if(Array.isArray(r.children)&&r.children[0].value===e&&2===r.children.length){n=r.children[1],t=!0;break}if(!t)return}else{const t=e;if("array"!==n.type||t<0||!Array.isArray(n.children)||t>=n.children.length)return;n=n.children[t]}return n}function d(e,t,n=u.DEFAULT){const i=r(e,!1),s=[];function o(e){return e?()=>e(i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter()):()=>!0}function a(e){return e?()=>e(i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter(),()=>s.slice()):()=>!0}function c(e){return e?t=>e(t,i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter()):()=>!0}function l(e){return e?t=>e(t,i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter(),()=>s.slice()):()=>!0}const h=a(t.onObjectBegin),p=l(t.onObjectProperty),f=o(t.onObjectEnd),d=a(t.onArrayBegin),m=o(t.onArrayEnd),g=l(t.onLiteralValue),x=c(t.onSeparator),y=o(t.onComment),v=c(t.onError),E=n&&n.disallowComments,C=n&&n.allowTrailingComma;function D(){for(;;){const e=i.scan();switch(i.getTokenError()){case 4:A(14);break;case 5:A(15);break;case 3:A(13);break;case 1:E||A(11);break;case 2:A(12);break;case 6:A(16)}switch(e){case 12:case 13:E?A(10):y();break;case 16:A(1);break;case 15:case 14:break;default:return e}}}function A(e,t=[],n=[]){if(v(e),t.length+n.length>0){let e=i.getToken();for(;17!==e;){if(-1!==t.indexOf(e)){D();break}if(-1!==n.indexOf(e))break;e=D()}}}function S(e){const t=i.getTokenValue();return e?g(t):(p(t),s.push(t)),D(),!0}function b(){return 10!==i.getToken()?(A(3,[],[2,5]),!1):(S(!1),6===i.getToken()?(x(":"),D(),w()||A(4,[],[2,5])):A(5,[],[2,5]),s.pop(),!0)}function w(){switch(i.getToken()){case 3:return function(){d(),D();let e=!0,t=!1;for(;4!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(t||A(4,[],[]),x(","),D(),4===i.getToken()&&C)break}else t&&A(6,[],[]);e?(s.push(0),e=!1):s[s.length-1]++,w()||A(4,[],[4,5]),t=!0}return m(),e||s.pop(),4!==i.getToken()?A(8,[4],[]):D(),!0}();case 1:return function(){h(),D();let e=!1;for(;2!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(e||A(4,[],[]),x(","),D(),2===i.getToken()&&C)break}else e&&A(6,[],[]);b()||A(4,[],[2,5]),e=!0}return f(),2!==i.getToken()?A(7,[2],[]):D(),!0}();case 10:return S(!0);default:return function(){switch(i.getToken()){case 11:const e=i.getTokenValue();let t=Number(e);isNaN(t)&&(A(2),t=0),g(t);break;case 7:g(null);break;case 8:g(!0);break;case 9:g(!1);break;default:return!1}return D(),!0}()}}return D(),17===i.getToken()?!!n.allowEmptyContent||(A(4,[],[]),!1):w()?(17!==i.getToken()&&A(9,[],[]),!0):(A(4,[],[]),!1)}function m(e){switch(typeof e){case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"object":return e?Array.isArray(e)?"array":"object":"null";default:return"null"}}function g(e,t,n){if(!n.formattingOptions)return[t];let r=x(e,t),i=t.offset,s=t.offset+t.content.length;if(0===t.length||0===t.content.length){for(;i>0&&!h(r,i-1);)i--;for(;s<r.length&&!h(r,s);)s++}const o=c(r,{offset:i,length:s-i},{...n.formattingOptions,keepLines:!1});for(let e=o.length-1;e>=0;e--){const t=o[e];r=x(r,t),i=Math.min(i,t.offset),s=Math.max(s,t.offset+t.length),s+=t.content.length-t.length}return[{offset:i,length:e.length-(r.length-s)-i,content:r.substring(i,s)}]}function x(e,t){return e.substring(0,t.offset)+t.content+e.substring(t.offset+t.length)}n.r(t),n.d(t,{ParseErrorCode:()=>_,ScanError:()=>v,SyntaxKind:()=>E,applyEdits:()=>N,createScanner:()=>y,findNodeAtLocation:()=>S,findNodeAtOffset:()=>b,format:()=>P,getLocation:()=>C,getNodePath:()=>w,getNodeValue:()=>k,modify:()=>B,parse:()=>D,parseTree:()=>A,printParseErrorCode:()=>O,stripComments:()=>T,visit:()=>F}),function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"}(a||(a={})),function(e){e.DEFAULT={allowTrailingComma:!1}}(u||(u={}));const y=r;var v,E;!function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"}(v||(v={})),function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"}(E||(E={}));const C=function(e,t){const n=[],r=new Object;let i;const s={value:{},offset:0,length:0,type:"object",parent:void 0};let o=!1;function a(e,t,n,r){s.value=e,s.offset=t,s.length=n,s.type=r,s.colonOffset=void 0,i=s}try{d(e,{onObjectBegin:(e,s)=>{if(t<=e)throw r;i=void 0,o=t>e,n.push("")},onObjectProperty:(e,i,s)=>{if(t<i)throw r;if(a(e,i,s,"property"),n[n.length-1]=e,t<=i+s)throw r},onObjectEnd:(e,s)=>{if(t<=e)throw r;i=void 0,n.pop()},onArrayBegin:(e,s)=>{if(t<=e)throw r;i=void 0,n.push(0)},onArrayEnd:(e,s)=>{if(t<=e)throw r;i=void 0,n.pop()},onLiteralValue:(e,n,i)=>{if(t<n)throw r;if(a(e,n,i,m(e)),t<=n+i)throw r},onSeparator:(e,s,a)=>{if(t<=s)throw r;if(":"===e&&i&&"property"===i.type)i.colonOffset=s,o=!1,i=void 0;else if(","===e){const e=n[n.length-1];"number"==typeof e?n[n.length-1]=e+1:(o=!0,n[n.length-1]=""),i=void 0}}})}catch(e){if(e!==r)throw e}return{path:n,previousNode:i,isAtPropertyKey:o,matches:e=>{let t=0;for(let r=0;t<e.length&&r<n.length;r++)if(e[t]===n[r]||"*"===e[t])t++;else if("**"!==e[t])return!1;return t===e.length}}},D=function(e,t=[],n=u.DEFAULT){let r=null,i=[];const s=[];function o(e){Array.isArray(i)?i.push(e):null!==r&&(i[r]=e)}return d(e,{onObjectBegin:()=>{const e={};o(e),s.push(i),i=e,r=null},onObjectProperty:e=>{r=e},onObjectEnd:()=>{i=s.pop()},onArrayBegin:()=>{const e=[];o(e),s.push(i),i=e,r=null},onArrayEnd:()=>{i=s.pop()},onLiteralValue:o,onError:(e,n,r)=>{t.push({error:e,offset:n,length:r})}},n),i[0]},A=p,S=f,b=function e(t,n,r=!1){if(function(e,t,n=!1){return t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){const i=t.children;if(Array.isArray(i))for(let t=0;t<i.length&&i[t].offset<=n;t++){const s=e(i[t],n,r);if(s)return s}return t}},w=function e(t){if(!t.parent||!t.parent.children)return[];const n=e(t.parent);if("property"===t.parent.type){const e=t.parent.children[0].value;n.push(e)}else if("array"===t.parent.type){const e=t.parent.children.indexOf(t);-1!==e&&n.push(e)}return n},k=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":const n=Object.create(null);for(let r of t.children){const t=r.children[1];t&&(n[r.children[0].value]=e(t))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}},F=d,T=function(e,t){let n,i,s=r(e),o=[],a=0;do{switch(i=s.getPosition(),n=s.scan(),n){case 12:case 13:case 17:a!==i&&o.push(e.substring(a,i)),void 0!==t&&o.push(s.getTokenValue().replace(/[^\r\n]/g,t)),a=s.getPosition()}}while(17!==n);return o.join("")};var _;function O(e){switch(e){case 1:return"InvalidSymbol";case 2:return"InvalidNumberFormat";case 3:return"PropertyNameExpected";case 4:return"ValueExpected";case 5:return"ColonExpected";case 6:return"CommaExpected";case 7:return"CloseBraceExpected";case 8:return"CloseBracketExpected";case 9:return"EndOfFileExpected";case 10:return"InvalidCommentToken";case 11:return"UnexpectedEndOfComment";case 12:return"UnexpectedEndOfString";case 13:return"UnexpectedEndOfNumber";case 14:return"InvalidUnicode";case 15:return"InvalidEscapeCharacter";case 16:return"InvalidCharacter"}return"<unknown ParseErrorCode>"}function P(e,t,n){return c(e,t,n)}function B(e,t,n,r){return function(e,t,n,r){const i=t.slice(),s=p(e,[]);let o,a;for(;i.length>0&&(a=i.pop(),o=f(s,i),void 0===o&&void 0!==n);)n="string"==typeof a?{[a]:n}:[n];if(o){if("object"===o.type&&"string"==typeof a&&Array.isArray(o.children)){const t=f(o,[a]);if(void 0!==t){if(void 0===n){if(!t.parent)throw new Error("Malformed AST");const n=o.children.indexOf(t.parent);let i,s=t.parent.offset+t.parent.length;if(n>0){let e=o.children[n-1];i=e.offset+e.length}else i=o.offset+1,o.children.length>1&&(s=o.children[1].offset);return g(e,{offset:i,length:s-i,content:""},r)}return g(e,{offset:t.offset,length:t.length,content:JSON.stringify(n)},r)}{if(void 0===n)return[];const t=`${JSON.stringify(a)}: ${JSON.stringify(n)}`,i=r.getInsertionIndex?r.getInsertionIndex(o.children.map(e=>e.children[0].value)):o.children.length;let s;if(i>0){let e=o.children[i-1];s={offset:e.offset+e.length,length:0,content:","+t}}else s=0===o.children.length?{offset:o.offset+1,length:0,content:t}:{offset:o.offset+1,length:0,content:t+","};return g(e,s,r)}}if("array"===o.type&&"number"==typeof a&&Array.isArray(o.children)){const t=a;if(-1===t){const t=`${JSON.stringify(n)}`;let i;if(0===o.children.length)i={offset:o.offset+1,length:0,content:t};else{const e=o.children[o.children.length-1];i={offset:e.offset+e.length,length:0,content:","+t}}return g(e,i,r)}if(void 0===n&&o.children.length>=0){const t=a,n=o.children[t];let i;if(1===o.children.length)i={offset:o.offset+1,length:o.length-2,content:""};else if(o.children.length-1===t){let e=o.children[t-1],n=e.offset+e.length;i={offset:n,length:o.offset+o.length-2-n,content:""}}else i={offset:n.offset,length:o.children[t+1].offset-n.offset,content:""};return g(e,i,r)}if(void 0!==n){let t;const i=`${JSON.stringify(n)}`;if(!r.isArrayInsertion&&o.children.length>a){const e=o.children[a];t={offset:e.offset,length:e.length,content:i}}else if(0===o.children.length||0===a)t={offset:o.offset+1,length:0,content:0===o.children.length?i:i+","};else{const e=a>o.children.length?o.children.length:a,n=o.children[e-1];t={offset:n.offset+n.length,length:0,content:","+i}}return g(e,t,r)}throw new Error(`Can not ${void 0===n?"remove":r.isArrayInsertion?"insert":"modify"} Array index ${t} as length is not sufficient`)}throw new Error(`Can not add ${"number"!=typeof a?"index":"property"} to parent of type ${o.type}`)}if(void 0===n)throw new Error("Can not delete in empty document");return g(e,{offset:s?s.offset:0,length:s?s.length:0,content:JSON.stringify(n)},r)}(e,t,n,r)}function N(e,t){let n=t.slice(0).sort((e,t)=>{const n=e.offset-t.offset;return 0===n?e.length-t.length:n}),r=e.length;for(let t=n.length-1;t>=0;t--){let i=n[t];if(!(i.offset+i.length<=r))throw new Error("Overlapping edit");e=x(e,i),r=i.offset}return e}!function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"}(_||(_={}))},6120:(e,t,n)=>{"use strict";const r=n(165);class i extends Error{constructor(e){super(),this.value=e}}const s=async(e,t)=>t(await e),o=async e=>{const t=await Promise.all(e);if(!0===t[1])throw new i(t[0]);return!1};e.exports=async(e,t,n)=>{n={concurrency:1/0,preserveOrder:!0,...n};const a=r(n.concurrency),u=[...e].map(e=>[e,a(s,e,t)]),c=r(n.preserveOrder?1:1/0);try{await Promise.all(u.map(e=>c(o,e)))}catch(e){if(e instanceof i)return e.value;throw e}}},6126:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BowerJSONContribution=void 0;const r=n(1398),i="Visual Studio Code";t.BowerJSONContribution=class{constructor(e){this.topRanked=["twitter","bootstrap","angular-1.1.6","angular-latest","angulerjs","d3","myjquery","jq","abcdef1234567890","jQuery","jquery-1.11.1","jquery","sushi-vanilla-x-data","font-awsome","Font-Awesome","font-awesome","fontawesome","html5-boilerplate","impress.js","homebrew","backbone","moment1","momentjs","moment","linux","animate.css","animate-css","reveal.js","jquery-file-upload","blueimp-file-upload","threejs","express","chosen","normalize-css","normalize.css","semantic","semantic-ui","Semantic-UI","modernizr","underscore","underscore1","material-design-icons","ionic","chartjs","Chart.js","nnnick-chartjs","select2-ng","select2-dist","phantom","skrollr","scrollr","less.js","leancss","parser-lib","hui","bootstrap-languages","async","gulp","jquery-pjax","coffeescript","hammer.js","ace","leaflet","jquery-mobile","sweetalert","typeahead.js","soup","typehead.js","sails","codeigniter2"],this.xhr=e}getDocumentSelector(){return[{language:"json",scheme:"*",pattern:"**/bower.json"},{language:"json",scheme:"*",pattern:"**/.bower.json"}]}isEnabled(){return!!r.workspace.getConfiguration("npm").get("fetchOnlinePackageInfo")}collectDefaultSuggestions(e,t){const n=new r.CompletionItem(r.l10n.t("Default bower.json"));return n.kind=r.CompletionItemKind.Class,n.insertText=new r.SnippetString(JSON.stringify({name:"${1:name}",description:"${2:description}",authors:["${3:author}"],version:"${4:1.0.0}",main:"${5:pathToMain}",dependencies:{}},null,"\t")),t.add(n),Promise.resolve(null)}collectPropertySuggestions(e,t,n,s,o,a){if(!this.isEnabled())return null;if(t.matches(["dependencies"])||t.matches(["devDependencies"])){if(n.length>0){const e="https://registry.bower.io/packages/search/"+encodeURIComponent(n);return this.xhr({url:e,headers:{agent:i}}).then(e=>{if(200!==e.status)return a.error(r.l10n.t("Request to the bower repository failed: {0}",e.responseText)),0;try{const t=JSON.parse(e.responseText);if(Array.isArray(t)){const e=t;for(const t of e){const e=t.name,n=t.description||"",i=(new r.SnippetString).appendText(JSON.stringify(e));s&&(i.appendText(": ").appendPlaceholder("latest"),o||i.appendText(","));const u=new r.CompletionItem(e);u.kind=r.CompletionItemKind.Property,u.insertText=i,u.filterText=JSON.stringify(e),u.documentation=n,a.add(u)}a.setAsIncomplete()}}catch(e){}},e=>(a.error(r.l10n.t("Request to the bower repository failed: {0}",e.responseText)),0))}return this.topRanked.forEach(e=>{const t=(new r.SnippetString).appendText(JSON.stringify(e));s&&(t.appendText(": ").appendPlaceholder("latest"),o||t.appendText(","));const n=new r.CompletionItem(e);n.kind=r.CompletionItemKind.Property,n.insertText=t,n.filterText=JSON.stringify(e),n.documentation="",a.add(n)}),a.setAsIncomplete(),Promise.resolve(null)}return null}collectValueSuggestions(e,t,n){if(!this.isEnabled())return null;if(t.matches(["dependencies","*"])||t.matches(["devDependencies","*"])){const e=new r.CompletionItem(r.l10n.t("latest"));e.insertText=new r.SnippetString('"${1:latest}"'),e.filterText='""',e.kind=r.CompletionItemKind.Value,e.documentation="The latest version of the package",n.add(e)}return null}resolveSuggestion(e,t){if(t.kind===r.CompletionItemKind.Property&&""===t.documentation){let e=t.label;return"string"!=typeof e&&(e=e.label),this.getInfo(e).then(e=>e?(t.documentation=e,t):null)}return null}getInfo(e){const t="https://registry.bower.io/packages/"+encodeURIComponent(e);return this.xhr({url:t,headers:{agent:i}}).then(e=>{try{const t=JSON.parse(e.responseText);if(t&&t.url){let e=t.url;return 0===e.indexOf("git://")&&(e=e.substring(6)),e.length>=4&&".git"===e.substr(e.length-4)&&(e=e.substring(0,e.length-4)),e}}catch(e){}},()=>{})}getInfoContribution(e,t){if(!this.isEnabled())return null;if(t.matches(["dependencies","*"])||t.matches(["devDependencies","*"])){const e=t.path[t.path.length-1];if("string"==typeof e)return this.getInfo(e).then(e=>{if(e){const t=new r.MarkdownString;return t.appendText(e),[t]}return null})}return null}}},6274:(e,t,n)=>{"use strict";var r=n(8725),i=["kind","resolve","construct","instanceOf","predicate","represent","defaultStyle","styleAliases"],s=["scalar","sequence","mapping"];e.exports=function(e,t){var n,o;if(t=t||{},Object.keys(t).forEach(function(t){if(-1===i.indexOf(t))throw new r('Unknown option "'+t+'" is met in definition of "'+e+'" YAML type.')}),this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(e){return e},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.defaultStyle=t.defaultStyle||null,this.styleAliases=(n=t.styleAliases||null,o={},null!==n&&Object.keys(n).forEach(function(e){n[e].forEach(function(t){o[String(t)]=e})}),o),-1===s.indexOf(this.kind))throw new r('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}},6675:(e,t,n)=>{"use strict";const r=n(6928),i=n(2608),s=n(1154),o=n(280),a=n(3940),u=(e,t,n=!1)=>{if(Array.isArray(e)){const r=e.map(e=>u(e,t,n)),i=e=>{for(const t of r){const n=t(e);if(n)return n}return!1};return i}const r=(i=e)&&"object"==typeof i&&!Array.isArray(i)&&e.tokens&&e.input;var i;if(""===e||"string"!=typeof e&&!r)throw new TypeError("Expected pattern to be a non-empty string");const s=t||{},a=o.isWindows(t),c=r?u.compileRe(e,t):u.makeRe(e,t,!1,!0),l=c.state;delete c.state;let h=()=>!1;if(s.ignore){const e={...t,ignore:null,onMatch:null,onResult:null};h=u(s.ignore,e,n)}const p=(n,r=!1)=>{const{isMatch:i,match:o,output:p}=u.test(n,c,t,{glob:e,posix:a}),f={glob:e,state:l,regex:c,posix:a,input:n,output:p,match:o,isMatch:i};return"function"==typeof s.onResult&&s.onResult(f),!1===i?(f.isMatch=!1,!!r&&f):h(n)?("function"==typeof s.onIgnore&&s.onIgnore(f),f.isMatch=!1,!!r&&f):("function"==typeof s.onMatch&&s.onMatch(f),!r||f)};return n&&(p.state=l),p};u.test=(e,t,n,{glob:r,posix:i}={})=>{if("string"!=typeof e)throw new TypeError("Expected input to be a string");if(""===e)return{isMatch:!1,output:""};const s=n||{},a=s.format||(i?o.toPosixSlashes:null);let c=e===r,l=c&&a?a(e):e;return!1===c&&(l=a?a(e):e,c=l===r),!1!==c&&!0!==s.capture||(c=!0===s.matchBase||!0===s.basename?u.matchBase(e,t,n,i):t.exec(l)),{isMatch:Boolean(c),match:c,output:l}},u.matchBase=(e,t,n,i=o.isWindows(n))=>(t instanceof RegExp?t:u.makeRe(t,n)).test(r.basename(e)),u.isMatch=(e,t,n)=>u(t,n)(e),u.parse=(e,t)=>Array.isArray(e)?e.map(e=>u.parse(e,t)):s(e,{...t,fastpaths:!1}),u.scan=(e,t)=>i(e,t),u.compileRe=(e,t,n=!1,r=!1)=>{if(!0===n)return e.output;const i=t||{},s=i.contains?"":"^",o=i.contains?"":"$";let a=`${s}(?:${e.output})${o}`;e&&!0===e.negated&&(a=`^(?!${a}).*$`);const c=u.toRegex(a,t);return!0===r&&(c.state=e),c},u.makeRe=(e,t={},n=!1,r=!1)=>{if(!e||"string"!=typeof e)throw new TypeError("Expected a non-empty string");let i={negated:!1,fastpaths:!0};return!1===t.fastpaths||"."!==e[0]&&"*"!==e[0]||(i.output=s.fastpaths(e,t)),i.output||(i=s(e,t)),u.compileRe(i,t,n,r)},u.toRegex=(e,t)=>{try{const n=t||{};return new RegExp(e,n.flags||(n.nocase?"i":""))}catch(e){if(t&&!0===t.debug)throw e;return/$^/}},u.constants=a,e.exports=u},6769:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:bool",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t=e.length;return 4===t&&("true"===e||"True"===e||"TRUE"===e)||5===t&&("false"===e||"False"===e||"FALSE"===e)},construct:function(e){return"true"===e||"True"===e||"TRUE"===e},predicate:function(e){return"[object Boolean]"===Object.prototype.toString.call(e)},represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"})},6867:e=>{"use strict";const t=(e,t)=>function(...n){return new(0,t.promiseModule)((r,i)=>{t.multiArgs?n.push((...e)=>{t.errorFirst?e[0]?i(e):(e.shift(),r(e)):r(e)}):t.errorFirst?n.push((e,t)=>{e?i(e):r(t)}):n.push(r),e.apply(this,n)})};e.exports=(e,n)=>{n=Object.assign({exclude:[/.+(Sync|Stream)$/],errorFirst:!0,promiseModule:Promise},n);const r=typeof e;if(null===e||"object"!==r&&"function"!==r)throw new TypeError(`Expected \`input\` to be a \`Function\` or \`Object\`, got \`${null===e?"null":r}\``);const i=e=>{const t=t=>"string"==typeof t?e===t:t.test(e);return n.include?n.include.some(t):!n.exclude.some(t)};let s;s="function"===r?function(...r){return n.excludeMain?e(...r):t(e,n).apply(this,r)}:Object.create(Object.getPrototypeOf(e));for(const r in e){const o=e[r];s[r]="function"==typeof o&&i(r)?t(o,n):o}return s}},6928:e=>{"use strict";e.exports=require("path")},6974:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NpmScriptHoverProvider=void 0,t.invalidateHoverScriptsCache=c;const r=n(6928),i=n(1398),s=n(3649),o=n(7511);let a,u;function c(e){e?e.uri===a&&(a=void 0):a=void 0}t.NpmScriptHoverProvider=class{constructor(e){this.context=e,e.subscriptions.push(i.commands.registerCommand("npm.runScriptFromHover",this.runScriptFromHover,this)),e.subscriptions.push(i.commands.registerCommand("npm.debugScriptFromHover",this.debugScriptFromHover,this)),e.subscriptions.push(i.workspace.onDidChangeTextDocument(e=>{c(e.document)}));const t=()=>i.workspace.getConfiguration("npm").get("scriptHover",!0);this.enabled=t(),e.subscriptions.push(i.workspace.onDidChangeConfiguration(e=>{e.affectsConfiguration("npm.scriptHover")&&(this.enabled=t())}))}provideHover(e,t,n){if(!this.enabled)return;let r;return a&&a.fsPath===e.uri.fsPath||(u=(0,s.readScripts)(e),a=e.uri),u?.scripts.forEach(({name:n,nameRange:s})=>{if(s.contains(t)){const t=new i.MarkdownString;t.isTrusted=!0,t.appendMarkdown(this.createRunScriptMarkdown(n,e.uri)),t.appendMarkdown(this.createDebugScriptMarkdown(n,e.uri)),r=new i.Hover(t)}}),r}createRunScriptMarkdown(e,t){const n={documentUri:t,script:e};return this.createMarkdownLink(i.l10n.t("Run Script"),"npm.runScriptFromHover",n,i.l10n.t("Run the script as a task"))}createDebugScriptMarkdown(e,t){const n={documentUri:t,script:e};return this.createMarkdownLink(i.l10n.t("Debug Script"),"npm.debugScriptFromHover",n,i.l10n.t("Runs the script under the debugger"),"|")}createMarkdownLink(e,t,n,r,i){let s="";return i&&(s=` ${i} `),`${s}[${e}](command:${t}?${encodeURIComponent(JSON.stringify(n))} "${r}")`}async runScriptFromHover(e){const t=e.script,n=e.documentUri,r=i.workspace.getWorkspaceFolder(n);if(r){const e=await(0,o.createScriptRunnerTask)(this.context,t,r,n);await i.tasks.executeTask(e)}}debugScriptFromHover(e){const t=e.script,n=e.documentUri,s=i.workspace.getWorkspaceFolder(n);s&&(0,o.startDebugging)(this.context,t,(0,r.dirname)(n.fsPath),s)}}},7016:e=>{"use strict";e.exports=require("url")},7028:(e,t,n)=>{"use strict";const r=n(9023),i=n(54),s=e=>null!==e&&"object"==typeof e&&!Array.isArray(e),o=e=>"number"==typeof e||"string"==typeof e&&""!==e,a=e=>Number.isInteger(+e),u=e=>{let t=`${e}`,n=-1;if("-"===t[0]&&(t=t.slice(1)),"0"===t)return!1;for(;"0"===t[++n];);return n>0},c=(e,t,n)=>{if(t>0){let n="-"===e[0]?"-":"";n&&(e=e.slice(1)),e=n+e.padStart(n?t-1:t,"0")}return!1===n?String(e):e},l=(e,t)=>{let n="-"===e[0]?"-":"";for(n&&(e=e.slice(1),t--);e.length<t;)e="0"+e;return n?"-"+e:e},h=(e,t,n,r)=>{if(n)return i(e,t,{wrap:!1,...r});let s=String.fromCharCode(e);return e===t?s:`[${s}-${String.fromCharCode(t)}]`},p=(e,t,n)=>{if(Array.isArray(e)){let t=!0===n.wrap,r=n.capture?"":"?:";return t?`(${r}${e.join("|")})`:e.join("|")}return i(e,t,n)},f=(...e)=>new RangeError("Invalid range arguments: "+r.inspect(...e)),d=(e,t,n)=>{if(!0===n.strictRanges)throw f([e,t]);return[]},m=(e,t,n,r={})=>{if(null==t&&o(e))return[e];if(!o(e)||!o(t))return d(e,t,r);if("function"==typeof n)return m(e,t,1,{transform:n});if(s(n))return m(e,t,0,n);let i={...r};return!0===i.capture&&(i.wrap=!0),n=n||i.step||1,a(n)?a(e)&&a(t)?((e,t,n=1,r={})=>{let i=Number(e),s=Number(t);if(!Number.isInteger(i)||!Number.isInteger(s)){if(!0===r.strictRanges)throw f([e,t]);return[]}0===i&&(i=0),0===s&&(s=0);let o=i>s,a=String(e),d=String(t),m=String(n);n=Math.max(Math.abs(n),1);let g=u(a)||u(d)||u(m),x=g?Math.max(a.length,d.length,m.length):0,y=!1===g&&!1===((e,t,n)=>"string"==typeof e||"string"==typeof t||!0===n.stringify)(e,t,r),v=r.transform||(e=>t=>!0===e?Number(t):String(t))(y);if(r.toRegex&&1===n)return h(l(e,x),l(t,x),!0,r);let E={negatives:[],positives:[]},C=e=>E[e<0?"negatives":"positives"].push(Math.abs(e)),D=[],A=0;for(;o?i>=s:i<=s;)!0===r.toRegex&&n>1?C(i):D.push(c(v(i,A),x,y)),i=o?i-n:i+n,A++;return!0===r.toRegex?n>1?((e,t,n)=>{e.negatives.sort((e,t)=>e<t?-1:e>t?1:0),e.positives.sort((e,t)=>e<t?-1:e>t?1:0);let r,i=t.capture?"":"?:",s="",o="";return e.positives.length&&(s=e.positives.map(e=>l(String(e),n)).join("|")),e.negatives.length&&(o=`-(${i}${e.negatives.map(e=>l(String(e),n)).join("|")})`),r=s&&o?`${s}|${o}`:s||o,t.wrap?`(${i}${r})`:r})(E,r,x):p(D,null,{wrap:!1,...r}):D})(e,t,n,i):((e,t,n=1,r={})=>{if(!a(e)&&e.length>1||!a(t)&&t.length>1)return d(e,t,r);let i=r.transform||(e=>String.fromCharCode(e)),s=`${e}`.charCodeAt(0),o=`${t}`.charCodeAt(0),u=s>o,c=Math.min(s,o),l=Math.max(s,o);if(r.toRegex&&1===n)return h(c,l,!1,r);let f=[],m=0;for(;u?s>=o:s<=o;)f.push(i(s,m)),s=u?s-n:s+n,m++;return!0===r.toRegex?p(f,null,{wrap:!1,options:r}):f})(e,t,Math.max(Math.abs(n),1),i):null==n||s(n)?m(e,t,1,n):((e,t)=>{if(!0===t.strictRanges)throw new TypeError(`Expected step "${e}" to be a number`);return[]})(n,i)};e.exports=m},7105:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&i(t,e,n[o]);return s(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.activate=async function(e){E(),e.subscriptions.push(c.workspace.onDidChangeConfiguration(e=>{(e.affectsConfiguration("http.proxy")||e.affectsConfiguration("http.proxyStrictSSL"))&&E()}));const t=await async function(){if(c.workspace.isTrusted&&c.workspace.workspaceFolders&&c.workspace.workspaceFolders.some(e=>"file"===e.uri.scheme))try{return await(0,g.default)("win32"===process.platform?"npm.cmd":"npm")}catch(e){return}}();e.subscriptions.push((0,l.addJSONProviders)(u.xhr,t)),function(e){if(c.workspace.workspaceFolders){const t=c.workspace.createFileSystemWatcher("**/package.json");t.onDidChange(e=>v()),t.onDidDelete(e=>v()),t.onDidCreate(e=>v()),e.subscriptions.push(t);const n=c.workspace.onDidChangeWorkspaceFolders(e=>v());e.subscriptions.push(n),y=new f.NpmTaskProvider(e);const r=c.tasks.registerTaskProvider("npm",y);e.subscriptions.push(r)}}(e),x=function(e){if(c.workspace.workspaceFolders){const t=new p.NpmScriptsTreeDataProvider(e,y),n=c.window.createTreeView("npm",{treeDataProvider:t,showCollapseAll:!0});return e.subscriptions.push(n),t}}(e),e.subscriptions.push(c.workspace.onDidChangeConfiguration(e=>{(e.affectsConfiguration("npm.exclude")||e.affectsConfiguration("npm.autoDetect")||e.affectsConfiguration("npm.scriptExplorerExclude")||e.affectsConfiguration("npm.runSilent")||e.affectsConfiguration("npm.packageManager")||e.affectsConfiguration("npm.scriptRunner"))&&((0,f.invalidateTasksCache)(),x&&x.refresh()),e.affectsConfiguration("npm.scriptExplorerAction")&&x&&x.refresh()})),function(e){if(c.workspace.workspaceFolders){const t={language:"json",scheme:"file",pattern:"**/package.json"},n=new d.NpmScriptHoverProvider(e);e.subscriptions.push(c.languages.registerHoverProvider(t,n))}}(e),e.subscriptions.push(c.commands.registerCommand("npm.runSelectedScript",h.runSelectedScript)),await(0,f.hasPackageJson)()&&c.commands.executeCommand("setContext","npm:showScriptExplorer",!0),e.subscriptions.push(c.commands.registerCommand("npm.runScriptFromFolder",h.selectAndRunScriptFromFolder)),e.subscriptions.push(c.commands.registerCommand("npm.refresh",()=>{v()})),e.subscriptions.push(c.commands.registerCommand("npm.scriptRunner",t=>t instanceof c.Uri?(0,f.getScriptRunner)(t,e,!0):"")),e.subscriptions.push(c.commands.registerCommand("npm.packageManager",t=>t instanceof c.Uri?(0,f.getPackageManager)(t,e,!0):"")),e.subscriptions.push(new m.NpmScriptLensProvider),e.subscriptions.push(c.window.registerTerminalQuickFixProvider("ms-vscode.npm-command",{provideTerminalQuickFixes({outputMatch:e}){if(!e)return;const t=e.regexMatch[1],n=[];for(const e of t.split("\n")){const t=e.indexOf("npm",1);if(-1===t)continue;const r=e.lastIndexOf("#");n.push({terminalCommand:e.slice(t,-1===r?void 0:r-1)})}return n}}))},t.deactivate=function(){};const u=o(n(9323)),c=o(n(1398)),l=n(8508),h=n(4459),p=n(7409),f=n(7511),d=n(6974),m=n(5095),g=a(n(7242));let x,y;function v(){(0,d.invalidateHoverScriptsCache)(),(0,f.invalidateTasksCache)(),x&&x.refresh()}function E(){const e=c.workspace.getConfiguration("http");u.configure(e.get("proxy",""),e.get("proxyStrictSSL",!0))}},7201:(e,t,n)=>{"use strict";const r=n(9896),{promisify:i}=n(9023),s=i(r.access);e.exports=async e=>{try{return await s(e),!0}catch(e){return!1}},e.exports.sync=e=>{try{return r.accessSync(e),!0}catch(e){return!1}}},7210:(e,t,n)=>{"use strict";var r=n(4376);e.exports=r},7242:(e,t,n)=>{const{isexe:r,sync:i}=n(842),{join:s,delimiter:o,sep:a,posix:u}=n(6928),c="win32"===process.platform,l=new RegExp(`[${u.sep}${a===u.sep?"":a}]`.replace(/(\\)/g,"\\$1")),h=new RegExp(`^\\.${l.source}`),p=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),f=(e,{path:t=process.env.PATH,pathExt:n=process.env.PATHEXT,delimiter:r=o})=>{const i=e.match(l)?[""]:[...c?[process.cwd()]:[],...(t||"").split(r)];if(c){const t=n||[".EXE",".CMD",".BAT",".COM"].join(r),s=t.split(r).flatMap(e=>[e,e.toLowerCase()]);return e.includes(".")&&""!==s[0]&&s.unshift(""),{pathEnv:i,pathExt:s,pathExtExe:t}}return{pathEnv:i,pathExt:[""]}},d=(e,t)=>{const n=/^".*"$/.test(e)?e.slice(1,-1):e;return(!n&&h.test(t)?t.slice(0,2):"")+s(n,t)},m=async(e,t={})=>{const{pathEnv:n,pathExt:i,pathExtExe:s}=f(e,t),o=[];for(const a of n){const n=d(a,e);for(const e of i){const i=n+e;if(await r(i,{pathExt:s,ignoreErrors:!0})){if(!t.all)return i;o.push(i)}}}if(t.all&&o.length)return o;if(t.nothrow)return null;throw p(e)};e.exports=m,m.sync=(e,t={})=>{const{pathEnv:n,pathExt:r,pathExtExe:s}=f(e,t),o=[];for(const a of n){const n=d(a,e);for(const e of r){const r=n+e;if(i(r,{pathExt:s,ignoreErrors:!0})){if(!t.all)return r;o.push(r)}}}if(t.all&&o.length)return o;if(t.nothrow)return null;throw p(e)}},7409:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&i(t,e,n[o]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.NpmScriptsTreeDataProvider=void 0;const a=o(n(6928)),u=n(1398),c=n(3649),l=n(7511);class h extends u.TreeItem{constructor(e){super(e.name,u.TreeItemCollapsibleState.Expanded),this.packages=[],this.contextValue="folder",this.resourceUri=e.uri,this.workspaceFolder=e,this.iconPath=u.ThemeIcon.Folder}addPackage(e){this.packages.push(e)}}const p="package.json";class f extends u.TreeItem{static getLabel(e){return e.length>0?a.join(e,p):p}constructor(e,t){super(f.getLabel(t),u.TreeItemCollapsibleState.Expanded),this.scripts=[],this.folder=e,this.path=t,this.contextValue="packageJSON",this.resourceUri=t?u.Uri.file(a.join(e.resourceUri.fsPath,t,p)):u.Uri.file(a.join(e.resourceUri.fsPath,p)),this.iconPath=u.ThemeIcon.File}addScript(e){this.scripts.push(e)}}class d extends u.TreeItem{constructor(e,t,n){const r=t.path.length>0?n.task.name.substring(0,n.task.name.length-t.path.length-2):n.task.name;super(r,u.TreeItemCollapsibleState.None),this.taskLocation=n.location;const i=r===`${l.INSTALL_SCRIPT} `?"run":u.workspace.getConfiguration("npm").get("scriptExplorerAction")||"open",s={open:{title:"Edit Script",command:"vscode.open",arguments:[this.taskLocation?.uri,this.taskLocation?{selection:new u.Range(this.taskLocation.range.start,this.taskLocation.range.start)}:void 0]},run:{title:"Run Script",command:"npm.runScript",arguments:[this]}};this.contextValue="script",this.package=t,this.task=n.task,this.command=s[i],this.task.group&&this.task.group===u.TaskGroup.Clean?this.iconPath=new u.ThemeIcon("wrench-subaction"):this.iconPath=new u.ThemeIcon("wrench"),this.task.detail&&(this.tooltip=this.task.detail,this.description=this.task.detail)}getFolder(){return this.package.folder.workspaceFolder}}class m extends u.TreeItem{constructor(e){super(e,u.TreeItemCollapsibleState.None),this.contextValue="noscripts"}}t.NpmScriptsTreeDataProvider=class{constructor(e,t){this.context=e,this.taskProvider=t,this.taskTree=null,this._onDidChangeTreeData=new u.EventEmitter,this.onDidChangeTreeData=this._onDidChangeTreeData.event;const n=e.subscriptions;this.extensionContext=e,n.push(u.commands.registerCommand("npm.runScript",this.runScript,this)),n.push(u.commands.registerCommand("npm.debugScript",this.debugScript,this)),n.push(u.commands.registerCommand("npm.openScript",this.openScript,this)),n.push(u.commands.registerCommand("npm.runInstall",this.runInstall,this))}async runScript(e){await(0,l.detectPackageManager)(e.getFolder().uri,this.context,!0),u.tasks.executeTask(e.task)}async debugScript(e){(0,l.startDebugging)(this.extensionContext,e.task.definition.script,a.dirname(e.package.resourceUri.fsPath),e.getFolder())}findScriptPosition(e,t){const n=(0,c.readScripts)(e);if(!n)return;if(!t)return n.location.range.start;const r=n.scripts.find(e=>(0,l.getTaskName)(e.name,t.task.definition.path)===t.task.name);return r?.nameRange.start}async runInstall(e){let t;if(e instanceof f&&(t=e.resourceUri),!t)return;const n=await(0,l.createInstallationTask)(this.context,e.folder.workspaceFolder,t);u.tasks.executeTask(n)}async openScript(e){let t;if(e instanceof f?t=e.resourceUri:e instanceof d&&(t=e.package.resourceUri),!t)return;const n=await u.workspace.openTextDocument(t),r=this.findScriptPosition(n,e instanceof d?e:void 0)||new u.Position(0,0);await u.window.showTextDocument(n,{preserveFocus:!0,selection:new u.Selection(r,r)})}refresh(){this.taskTree=null,this._onDidChangeTreeData.fire(null)}getTreeItem(e){return e}getParent(e){return e instanceof h?null:e instanceof f?e.folder:e instanceof d?e.package:null}async getChildren(e){if(!this.taskTree){const e=await this.taskProvider.tasksWithLocation;if(e){const t=this.buildTaskTree(e);if(this.taskTree=this.sortTaskTree(t),0===this.taskTree.length){let e=u.l10n.t("No scripts found.");(0,l.isAutoDetectionEnabled)()||(e=u.l10n.t('The setting "npm.autoDetect" is "off".')),this.taskTree=[new m(e)]}}}return e instanceof h?e.packages:e instanceof f?e.scripts:e instanceof d||e instanceof m?[]:!e&&this.taskTree?this.taskTree:[]}isInstallTask(e){return(0,l.getTaskName)("install",e.definition.path)===e.name}getTaskTreeItemLabel(e){return void 0===e?"":"string"==typeof e?e:e.label}sortTaskTree(e){return e.sort((e,t)=>{const n=this.getTaskTreeItemLabel(e.label),r=this.getTaskTreeItemLabel(t.label);return n.localeCompare(r)})}buildTaskTree(e){const t=new Map,n=new Map;let r=null,i=null;const s=new Map;return e.forEach(e=>{const o=e.location;if(o&&!s.has(o.uri.toString())){const e=u.workspace.getConfiguration("npm",o.uri).get("scriptExplorerExclude",[]);s.set(o.uri.toString(),e?.map(e=>RegExp(e)))}const c=o&&s.has(o.uri.toString())?s.get(o.uri.toString()):void 0;if((!c||!c.some(t=>e.task.definition.script.match(t)))&&(0,l.isWorkspaceFolder)(e.task.scope)&&!this.isInstallTask(e.task)){r=t.get(e.task.scope.name),r||(r=new h(e.task.scope),t.set(e.task.scope.name,r));const s=e.task.definition,o=s.path?s.path:"",u=a.join(e.task.scope.name,o);i=n.get(u),i||(i=new f(r,o),r.addPackage(i),n.set(u,i));const c=new d(this.extensionContext,i,e);i.addScript(c)}}),1===t.size?[...n.values()]:[...t.values()]}}},7459:(e,t,n)=>{"use strict";var r=n(1621);e.exports=r.DEFAULT=new r({include:[n(919)],explicit:[n(9057),n(9924),n(585)]})},7511:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&i(t,e,n[o]);return s(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.NpmTaskProvider=t.INSTALL_SCRIPT=void 0,t.invalidateTasksCache=function(){g=void 0},t.isWorkspaceFolder=C,t.getScriptRunner=D,t.getPackageManager=A,t.detectPackageManager=S,t.hasNpmScripts=async function(){const e=u.workspace.workspaceFolders;if(!e)return!1;for(const t of e)if(w(t)&&!m.test(p.Utils.basename(t.uri))){const e=new u.RelativePattern(t,"**/package.json");if((await u.workspace.findFiles(e,"**/node_modules/**")).length>0)return!0}return!1},t.detectNpmScriptsForFolder=async function(e,t){const n=[];if(m.test(p.Utils.basename(t)))return n;const r=new u.RelativePattern(t.fsPath,"**/package.json"),i=await u.workspace.findFiles(r,"**/node_modules/**"),s=new Set;for(const t of i)if(!s.has(t.fsPath)){const r=await F(e,t,!0);s.add(t.fsPath),n.push(...r.map(e=>({label:e.task.name,task:e.task})))}return n},t.provideNpmScripts=b,t.isAutoDetectionEnabled=w,t.getTaskName=T,t.getRunScriptCommand=P,t.createScriptRunnerTask=B,t.createInstallationTask=N,t.getPackageJsonUriFromTask=function(e){return C(e.scope)?e.definition.path?u.Uri.file(c.join(e.scope.uri.fsPath,e.definition.path,"package.json")):u.Uri.file(c.join(e.scope.uri.fsPath,"package.json")):null},t.hasPackageJson=async function(){if(await async function(){const e=u.workspace.workspaceFolders;if(!e)return!1;for(const t of e)if("file"===t.uri.scheme){const e=c.join(t.uri.fsPath,"package.json");if(await I(e))return!0}return!1}())return!0;const e=new u.CancellationTokenSource,t=setTimeout(()=>e.cancel(),1e3),n=await u.workspace.findFiles("**/package.json",void 0,1,e.token);return clearTimeout(t),n.length>0},t.runScript=async function(e,t,n){const r=n.uri,i=u.workspace.getWorkspaceFolder(r);if(i){const n=await B(e,t,i,r);u.tasks.executeTask(n)}},t.startDebugging=async function(e,t,n,r){const i=await P(t,r.uri,e,!0);u.commands.executeCommand("extension.js-debug.createDebuggerTerminal",i.join(" "),r,{cwd:n})},t.findScriptAtPosition=function(e,t,n){const r=(0,d.readScripts)(e,t);if(r)for(const e of r.scripts)if(e.nameRange.start.isBeforeOrEqual(n)&&e.valueRange.end.isAfterOrEqual(n))return e.name},t.getScripts=R;const u=n(1398),c=o(n(6928)),l=o(n(9896)),h=a(n(4027)),p=n(7608),f=n(810),d=n(3649),m=new RegExp("^(node_modules|.vscode-test)$","i");let g;t.INSTALL_SCRIPT="install",t.NpmTaskProvider=class{constructor(e){this.context=e}get tasksWithLocation(){return b(this.context,!1)}async provideTasks(){return(await b(this.context,!0)).map(e=>e.task)}async resolveTask(e){if(e.definition.script){const n=e.definition;let r,i;if(void 0===e.scope||e.scope===u.TaskScope.Global||e.scope===u.TaskScope.Workspace)return;return r=n.path?e.scope.uri.with({path:e.scope.uri.path+"/"+n.path+(n.path.endsWith("/")?"":"/")+"package.json"}):e.scope.uri.with({path:e.scope.uri.path+"/package.json"}),i=n.script===t.INSTALL_SCRIPT?await N(this.context,e.scope,r):await B(this.context,n.script,e.scope,r),i.definition=n,i}}};const x=["build","compile","watch"],y=["test"],v=new Set(["install","pack","pack","publish","restart","shrinkwrap","stop","test","uninstall","version"]),E=new Set(["install","pack","pack","publish","publishOnly","restart","shrinkwrap","stop","test","uninstall","version"]);function C(e){return e&&"number"!=typeof e}async function D(e,t,n){let r=u.workspace.getConfiguration("npm",e).get("scriptRunner","npm");return"auto"===r&&(r=await S(e,t,n)),r}async function A(e,t,n){let r=u.workspace.getConfiguration("npm",e).get("packageManager","npm");return"auto"===r&&(r=await S(e,t,n)),r}async function S(e,t,n=!1){const{name:r,multipleLockFilesDetected:i}=await(0,f.findPreferredPM)(e.fsPath),s="npm.multiplePMWarning.neverShow";if(n&&i&&t&&!t.globalState.get(s)){const n=u.l10n.t('Using {0} as the preferred package manager. Found multiple lockfiles for {1}.  To resolve this issue, delete the lockfiles that don\'t match your preferred package manager or change the setting "npm.packageManager" to a value other than "auto".',r,e.fsPath),i=u.l10n.t("Do not show again"),o=u.l10n.t("Learn more");u.window.showInformationMessage(n,o,i).then(e=>{switch(e){case i:t.globalState.update(s,!0);break;case o:u.env.openExternal(u.Uri.parse("https://docs.npmjs.com/cli/v9/configuring-npm/package-lock-json"))}})}return r}async function b(e,t){if(!g){const n=[];for await(const r of async function*(){const e=new Set,t=u.workspace.workspaceFolders;if(t)for(const n of t)if(w(n)&&!m.test(p.Utils.basename(n.uri))){const t=new u.RelativePattern(n,"**/package.json"),r=await u.workspace.findFiles(t,"**/{node_modules,.vscode-test}/**");for(const t of r)k(n,t)||e.has(t.fsPath)||(yield t,e.add(t.fsPath))}}()){const i=await F(e,r,t);n.push(...i)}g=n}return g}function w(e){return"on"===u.workspace.getConfiguration("npm",e?.uri).get("autoDetect")}function k(e,t){function n(e,t){return(0,h.default)(e,t,{dot:!0})}const r=u.workspace.getConfiguration("npm",e.uri).get("exclude"),i=c.dirname(t.fsPath);if(r)if(Array.isArray(r)){for(const e of r)if(n(i,e))return!0}else if(n(i,r))return!0;return!1}async function F(e,n,r){const i=[],s=u.workspace.getWorkspaceFolder(n);if(!s)return i;const o=await R(n);if(!o)return i;const a=[];for(const{name:t,value:i,nameRange:c}of o.scripts){const o=await B(e,t,s,n,i,r);a.push({task:o,location:new u.Location(n,c)})}return u.workspace.getConfiguration("npm",s).get("scriptExplorerExclude",[]).find(e=>e.includes(t.INSTALL_SCRIPT))||a.push({task:await N(e,s,n,"install dependencies from package",r)}),a}function T(e,t){return t&&t.length?`${e} - ${t.substring(0,t.length-1)}`:e}function _(e){return e.map(e=>/\s/.test(e)?{value:e,quoting:e.includes("--")?u.ShellQuoting.Weak:u.ShellQuoting.Strong}:e)}function O(e,t){return t.path.substring(0,t.path.length-12).substring(e.path.length+1)}async function P(e,t,n,r=!0){const i=await D(t,n,r);if("node"===i)return["node","--run",e];{const n=[i,"run"];return u.workspace.getConfiguration("npm",t).get("runSilent")&&n.push("--silent"),n.push(e),n}}async function B(e,t,n,r,i,s){const o={type:"npm",script:t},a=O(n.uri,r);a.length&&!o.path&&(o.path=a.substring(0,a.length-1));const l=T(t,a),h=c.dirname(r.fsPath),p=await P(t,n.uri,e,s),f=p.shift(),d=new u.Task(o,n,l,"npm",new u.ShellExecution(f,_(p),{cwd:h}));d.detail=i;const m=t.toLowerCase();var g;return function(e){for(const t of x)if(-1!==e.indexOf(t))return!0;return!1}(m)?d.group=u.TaskGroup.Build:function(e){for(const t of y)if(e===t)return!0;return!1}(m)?d.group=u.TaskGroup.Test:(g=m,v.has(g)||E.has(g)?d.group=u.TaskGroup.Clean:i&&function(e){return null!==e.match(/--(inspect|debug)(-brk)?(=((\[[0-9a-fA-F:]*\]|[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+|[a-zA-Z0-9\.]*):)?(\d+))?/)}(i)&&(d.group=u.TaskGroup.Rebuild)),d}async function N(e,n,r,i,s){const o={type:"npm",script:t.INSTALL_SCRIPT},a=O(n.uri,r);a.length&&!o.path&&(o.path=a.substring(0,a.length-1));const l=T(t.INSTALL_SCRIPT,a),h=c.dirname(r.fsPath),p=await async function(e,n,r=!0){const i=[await A(e,n,r),t.INSTALL_SCRIPT];return u.workspace.getConfiguration("npm",e).get("runSilent")&&i.push("--silent"),i}(n.uri,e,s),f=p.shift(),d=new u.Task(o,n,l,"npm",new u.ShellExecution(f,_(p),{cwd:h}));return d.detail=i,d.group=u.TaskGroup.Clean,d}async function I(e){return new Promise((t,n)=>{l.exists(e,e=>{t(e)})})}async function R(e){if("file"!==e.scheme)return;const t=e.fsPath;if(await I(t))try{const t=await u.workspace.openTextDocument(e);return(0,d.readScripts)(t)}catch(t){const n=u.l10n.t("Npm task detection: failed to parse the file {0}",e.fsPath);throw new Error(n)}}},7608:(e,t,n)=>{"use strict";var r;n.r(t),n.d(t,{URI:()=>i,Utils:()=>s}),(()=>{var e={470:e=>{function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,s=-1,o=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(s===a-1||1===o);else if(s!==a-1&&2===o){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var u=r.lastIndexOf("/");if(u!==r.length-1){-1===u?(r="",i=0):i=(r=r.slice(0,u)).length-1-r.lastIndexOf("/"),s=a,o=0;continue}}else if(2===r.length||1===r.length){r="",i=0,s=a,o=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(s+1,a):r=e.slice(s+1,a),i=a-s-1;s=a,o=0}else 46===n&&-1!==o?++o:o=-1}return r}var r={resolve:function(){for(var e,r="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var o;s>=0?o=arguments[s]:(void 0===e&&(e=process.cwd()),o=e),t(o),0!==o.length&&(r=o+"/"+r,i=47===o.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var s=e.length,o=s-i,a=1;a<n.length&&47===n.charCodeAt(a);++a);for(var u=n.length-a,c=o<u?o:u,l=-1,h=0;h<=c;++h){if(h===c){if(u>c){if(47===n.charCodeAt(a+h))return n.slice(a+h+1);if(0===h)return n.slice(a+h)}else o>c&&(47===e.charCodeAt(i+h)?l=h:0===h&&(l=0));break}var p=e.charCodeAt(i+h);if(p!==n.charCodeAt(a+h))break;47===p&&(l=h)}var f="";for(h=i+l+1;h<=s;++h)h!==s&&47!==e.charCodeAt(h)||(0===f.length?f+="..":f+="/..");return f.length>0?f+n.slice(a+l):(a+=l,47===n.charCodeAt(a)&&++a,n.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,s=!0,o=e.length-1;o>=1;--o)if(47===(n=e.charCodeAt(o))){if(!s){i=o;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,s=-1,o=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var a=n.length-1,u=-1;for(r=e.length-1;r>=0;--r){var c=e.charCodeAt(r);if(47===c){if(!o){i=r+1;break}}else-1===u&&(o=!1,u=r+1),a>=0&&(c===n.charCodeAt(a)?-1==--a&&(s=r):(a=-1,s=u))}return i===s?s=u:-1===s&&(s=e.length),e.slice(i,s)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!o){i=r+1;break}}else-1===s&&(o=!1,s=r+1);return-1===s?"":e.slice(i,s)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,s=!0,o=0,a=e.length-1;a>=0;--a){var u=e.charCodeAt(a);if(47!==u)-1===i&&(s=!1,i=a+1),46===u?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1);else if(!s){r=a+1;break}}return-1===n||-1===i||0===o||1===o&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var o=-1,a=0,u=-1,c=!0,l=e.length-1,h=0;l>=r;--l)if(47!==(i=e.charCodeAt(l)))-1===u&&(c=!1,u=l+1),46===i?-1===o?o=l:1!==h&&(h=1):-1!==o&&(h=-1);else if(!c){a=l+1;break}return-1===o||-1===u||0===h||1===h&&o===u-1&&o===a+1?-1!==u&&(n.base=n.name=0===a&&s?e.slice(1,u):e.slice(a,u)):(0===a&&s?(n.name=e.slice(1,o),n.base=e.slice(1,u)):(n.name=e.slice(a,o),n.base=e.slice(a,u)),n.ext=e.slice(o,u)),a>0?n.dir=e.slice(0,a-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{let e;if(n.r(i),n.d(i,{URI:()=>l,Utils:()=>S}),"object"==typeof process)e="win32"===process.platform;else if("object"==typeof navigator){let t=navigator.userAgent;e=t.indexOf("Windows")>=0}const t=/^\w[\w\d+.-]*$/,r=/^\//,s=/^\/\//;function o(e,n){if(!e.scheme&&n)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!r.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(s.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const a="",u="/",c=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class l{static isUri(e){return e instanceof l||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,n,r,i,s=!1){"object"==typeof e?(this.scheme=e.scheme||a,this.authority=e.authority||a,this.path=e.path||a,this.query=e.query||a,this.fragment=e.fragment||a):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||a,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==u&&(t=u+t):t=u}return t}(this.scheme,n||a),this.query=r||a,this.fragment=i||a,o(this,s))}get fsPath(){return g(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=a),void 0===n?n=this.authority:null===n&&(n=a),void 0===r?r=this.path:null===r&&(r=a),void 0===i?i=this.query:null===i&&(i=a),void 0===s?s=this.fragment:null===s&&(s=a),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&s===this.fragment?this:new p(t,n,r,i,s)}static parse(e,t=!1){const n=c.exec(e);return n?new p(n[2]||a,E(n[4]||a),E(n[5]||a),E(n[7]||a),E(n[9]||a),t):new p(a,a,a,a,a)}static file(t){let n=a;if(e&&(t=t.replace(/\\/g,u)),t[0]===u&&t[1]===u){const e=t.indexOf(u,2);-1===e?(n=t.substring(2),t=u):(n=t.substring(2,e),t=t.substring(e)||u)}return new p("file",n,t,a,a)}static from(e){const t=new p(e.scheme,e.authority,e.path,e.query,e.fragment);return o(t,!0),t}toString(e=!1){return x(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof l)return e;{const t=new p(e);return t._formatted=e.external,t._fsPath=e._sep===h?e.fsPath:null,t}}return e}}const h=e?1:void 0;class p extends l{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=g(this,!1)),this._fsPath}toString(e=!1){return e?x(this,!0):(this._formatted||(this._formatted=x(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=h),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const f={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function d(e,t,n){let r,i=-1;for(let s=0;s<e.length;s++){const o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o||n&&91===o||n&&93===o||n&&58===o)-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),void 0!==r&&(r+=e.charAt(s));else{void 0===r&&(r=e.substr(0,s));const t=f[o];void 0!==t?(-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r+=t):-1===i&&(i=s)}}return-1!==i&&(r+=encodeURIComponent(e.substring(i))),void 0!==r?r:e}function m(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=f[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function g(t,n){let r;return r=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&58===t.path.charCodeAt(2)?n?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(r=r.replace(/\//g,"\\")),r}function x(e,t){const n=t?m:d;let r="",{scheme:i,authority:s,path:o,query:a,fragment:c}=e;if(i&&(r+=i,r+=":"),(s||"file"===i)&&(r+=u,r+=u),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.lastIndexOf(":"),-1===e?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=":",r+=n(t.substr(e+1),!1,!0)),r+="@"}s=s.toLowerCase(),e=s.lastIndexOf(":"),-1===e?r+=n(s,!1,!0):(r+=n(s.substr(0,e),!1,!0),r+=s.substr(e))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2)){const e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&58===o.charCodeAt(1)){const e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return a&&(r+="?",r+=n(a,!1,!1)),c&&(r+="#",r+=t?c:d(c,!1,!1)),r}function y(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+y(e.substr(3)):e}}const v=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function E(e){return e.match(v)?e.replace(v,e=>y(e)):e}var C=n(470);const D=C.posix||C,A="/";var S;!function(e){e.joinPath=function(e,...t){return e.with({path:D.join(e.path,...t)})},e.resolvePath=function(e,...t){let n=e.path,r=!1;n[0]!==A&&(n=A+n,r=!0);let i=D.resolve(n,...t);return r&&i[0]===A&&!e.authority&&(i=i.substring(1)),e.with({path:i})},e.dirname=function(e){if(0===e.path.length||e.path===A)return e;let t=D.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},e.basename=function(e){return D.basename(e.path)},e.extname=function(e){return D.extname(e.path)}}(S||(S={}))})(),r=i})();const{URI:i,Utils:s}=r},7612:(e,t)=>{"use strict";t.isInteger=e=>"number"==typeof e?Number.isInteger(e):"string"==typeof e&&""!==e.trim()&&Number.isInteger(Number(e)),t.find=(e,t)=>e.nodes.find(e=>e.type===t),t.exceedsLimit=(e,n,r=1,i)=>!1!==i&&!(!t.isInteger(e)||!t.isInteger(n))&&(Number(n)-Number(e))/Number(r)>=i,t.escapeNode=(e,t=0,n)=>{const r=e.nodes[t];r&&(n&&r.type===n||"open"===r.type||"close"===r.type)&&!0!==r.escaped&&(r.value="\\"+r.value,r.escaped=!0)},t.encloseBrace=e=>!("brace"!==e.type||e.commas>>0+e.ranges|0||(e.invalid=!0,0)),t.isInvalidBrace=e=>!("brace"!==e.type||!0!==e.invalid&&!e.dollar&&(e.commas>>0+e.ranges|0&&!0===e.open&&!0===e.close||(e.invalid=!0,0))),t.isOpenOrClose=e=>"open"===e.type||"close"===e.type||!0===e.open||!0===e.close,t.reduce=e=>e.reduce((e,t)=>("text"===t.type&&e.push(t.value),"range"===t.type&&(t.type="text"),e),[]),t.flatten=(...e)=>{const t=[],n=e=>{for(let r=0;r<e.length;r++){const i=e[r];Array.isArray(i)?n(i):void 0!==i&&t.push(i)}return t};return n(e),t}},7635:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return null!==e?e:{}}})},7913:(e,t,n)=>{"use strict";var r=n(1621);e.exports=new r({explicit:[n(8998),n(5090),n(7635)]})},7933:(e,t,n)=>{"use strict";var r=n(6274),i=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),s=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");e.exports=new r("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:function(e){return null!==e&&(null!==i.exec(e)||null!==s.exec(e))},construct:function(e){var t,n,r,o,a,u,c,l,h=0,p=null;if(null===(t=i.exec(e))&&(t=s.exec(e)),null===t)throw new Error("Date resolve error");if(n=+t[1],r=+t[2]-1,o=+t[3],!t[4])return new Date(Date.UTC(n,r,o));if(a=+t[4],u=+t[5],c=+t[6],t[7]){for(h=t[7].slice(0,3);h.length<3;)h+="0";h=+h}return t[9]&&(p=6e4*(60*+t[10]+ +(t[11]||0)),"-"===t[9]&&(p=-p)),l=new Date(Date.UTC(n,r,o,a,u,c,h)),p&&l.setTime(l.getTime()-p),l},instanceOf:Date,represent:function(e){return e.toISOString()}})},7943:(e,t,n)=>{"use strict";var r=n(2111),i=n(8725),s=n(7459),o=n(919),a=Object.prototype.toString,u=Object.prototype.hasOwnProperty,c={0:"\\0",7:"\\a",8:"\\b",9:"\\t",10:"\\n",11:"\\v",12:"\\f",13:"\\r",27:"\\e",34:'\\"',92:"\\\\",133:"\\N",160:"\\_",8232:"\\L",8233:"\\P"},l=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"];function h(e){var t,n,s;if(t=e.toString(16).toUpperCase(),e<=255)n="x",s=2;else if(e<=65535)n="u",s=4;else{if(!(e<=4294967295))throw new i("code point within a string may not be greater than 0xFFFFFFFF");n="U",s=8}return"\\"+n+r.repeat("0",s-t.length)+t}function p(e){this.schema=e.schema||s,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=r.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=function(e,t){var n,r,i,s,o,a,c;if(null===t)return{};for(n={},i=0,s=(r=Object.keys(t)).length;i<s;i+=1)o=r[i],a=String(t[o]),"!!"===o.slice(0,2)&&(o="tag:yaml.org,2002:"+o.slice(2)),(c=e.compiledTypeMap.fallback[o])&&u.call(c.styleAliases,a)&&(a=c.styleAliases[a]),n[o]=a;return n}(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function f(e,t){for(var n,i=r.repeat(" ",t),s=0,o=-1,a="",u=e.length;s<u;)-1===(o=e.indexOf("\n",s))?(n=e.slice(s),s=u):(n=e.slice(s,o+1),s=o+1),n.length&&"\n"!==n&&(a+=i),a+=n;return a}function d(e,t){return"\n"+r.repeat(" ",e.indent*t)}function m(e){return 32===e||9===e}function g(e){return 32<=e&&e<=126||161<=e&&e<=55295&&8232!==e&&8233!==e||57344<=e&&e<=65533&&65279!==e||65536<=e&&e<=1114111}function x(e,t){return g(e)&&65279!==e&&44!==e&&91!==e&&93!==e&&123!==e&&125!==e&&58!==e&&(35!==e||t&&function(e){return g(e)&&!m(e)&&65279!==e&&13!==e&&10!==e}(t))}function y(e){return/^\n* /.test(e)}function v(e,t,n,r){e.dump=function(){if(0===t.length)return"''";if(!e.noCompatMode&&-1!==l.indexOf(t))return"'"+t+"'";var s=e.indent*Math.max(1,n),o=-1===e.lineWidth?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-s),a=r||e.flowLevel>-1&&n>=e.flowLevel;switch(function(e,t,n,r,i){var s,o,a,u,c=!1,l=!1,h=-1!==r,p=-1,f=g(u=e.charCodeAt(0))&&65279!==u&&!m(u)&&45!==u&&63!==u&&58!==u&&44!==u&&91!==u&&93!==u&&123!==u&&125!==u&&35!==u&&38!==u&&42!==u&&33!==u&&124!==u&&61!==u&&62!==u&&39!==u&&34!==u&&37!==u&&64!==u&&96!==u&&!m(e.charCodeAt(e.length-1));if(t)for(s=0;s<e.length;s++){if(!g(o=e.charCodeAt(s)))return 5;a=s>0?e.charCodeAt(s-1):null,f=f&&x(o,a)}else{for(s=0;s<e.length;s++){if(10===(o=e.charCodeAt(s)))c=!0,h&&(l=l||s-p-1>r&&" "!==e[p+1],p=s);else if(!g(o))return 5;a=s>0?e.charCodeAt(s-1):null,f=f&&x(o,a)}l=l||h&&s-p-1>r&&" "!==e[p+1]}return c||l?n>9&&y(e)?5:l?4:3:f&&!i(e)?1:2}(t,a,e.indent,o,function(t){return function(e,t){var n,r;for(n=0,r=e.implicitTypes.length;n<r;n+=1)if(e.implicitTypes[n].resolve(t))return!0;return!1}(e,t)})){case 1:return t;case 2:return"'"+t.replace(/'/g,"''")+"'";case 3:return"|"+E(t,e.indent)+C(f(t,s));case 4:return">"+E(t,e.indent)+C(f(function(e,t){for(var n,r,i,s=/(\n+)([^\n]*)/g,o=(i=-1!==(i=e.indexOf("\n"))?i:e.length,s.lastIndex=i,D(e.slice(0,i),t)),a="\n"===e[0]||" "===e[0];r=s.exec(e);){var u=r[1],c=r[2];n=" "===c[0],o+=u+(a||n||""===c?"":"\n")+D(c,t),a=n}return o}(t,o),s));case 5:return'"'+function(e){for(var t,n,r,i="",s=0;s<e.length;s++)(t=e.charCodeAt(s))>=55296&&t<=56319&&(n=e.charCodeAt(s+1))>=56320&&n<=57343?(i+=h(1024*(t-55296)+n-56320+65536),s++):i+=!(r=c[t])&&g(t)?e[s]:r||h(t);return i}(t)+'"';default:throw new i("impossible error: invalid scalar style")}}()}function E(e,t){var n=y(e)?String(t):"",r="\n"===e[e.length-1];return n+(!r||"\n"!==e[e.length-2]&&"\n"!==e?r?"":"-":"+")+"\n"}function C(e){return"\n"===e[e.length-1]?e.slice(0,-1):e}function D(e,t){if(""===e||" "===e[0])return e;for(var n,r,i=/ [^ ]/g,s=0,o=0,a=0,u="";n=i.exec(e);)(a=n.index)-s>t&&(r=o>s?o:a,u+="\n"+e.slice(s,r),s=r+1),o=a;return u+="\n",e.length-s>t&&o>s?u+=e.slice(s,o)+"\n"+e.slice(o+1):u+=e.slice(s),u.slice(1)}function A(e,t,n){var r,s,o,c,l,h;for(o=0,c=(s=n?e.explicitTypes:e.implicitTypes).length;o<c;o+=1)if(((l=s[o]).instanceOf||l.predicate)&&(!l.instanceOf||"object"==typeof t&&t instanceof l.instanceOf)&&(!l.predicate||l.predicate(t))){if(e.tag=n?l.tag:"?",l.represent){if(h=e.styleMap[l.tag]||l.defaultStyle,"[object Function]"===a.call(l.represent))r=l.represent(t,h);else{if(!u.call(l.represent,h))throw new i("!<"+l.tag+'> tag resolver accepts not "'+h+'" style');r=l.represent[h](t,h)}e.dump=r}return!0}return!1}function S(e,t,n,r,s,o){e.tag=null,e.dump=n,A(e,n,!1)||A(e,n,!0);var u=a.call(e.dump);r&&(r=e.flowLevel<0||e.flowLevel>t);var c,l,h="[object Object]"===u||"[object Array]"===u;if(h&&(l=-1!==(c=e.duplicates.indexOf(n))),(null!==e.tag&&"?"!==e.tag||l||2!==e.indent&&t>0)&&(s=!1),l&&e.usedDuplicates[c])e.dump="*ref_"+c;else{if(h&&l&&!e.usedDuplicates[c]&&(e.usedDuplicates[c]=!0),"[object Object]"===u)r&&0!==Object.keys(e.dump).length?(function(e,t,n,r){var s,o,a,u,c,l,h="",p=e.tag,f=Object.keys(n);if(!0===e.sortKeys)f.sort();else if("function"==typeof e.sortKeys)f.sort(e.sortKeys);else if(e.sortKeys)throw new i("sortKeys must be a boolean or a function");for(s=0,o=f.length;s<o;s+=1)l="",r&&0===s||(l+=d(e,t)),u=n[a=f[s]],S(e,t+1,a,!0,!0,!0)&&((c=null!==e.tag&&"?"!==e.tag||e.dump&&e.dump.length>1024)&&(e.dump&&10===e.dump.charCodeAt(0)?l+="?":l+="? "),l+=e.dump,c&&(l+=d(e,t)),S(e,t+1,u,!0,c)&&(e.dump&&10===e.dump.charCodeAt(0)?l+=":":l+=": ",h+=l+=e.dump));e.tag=p,e.dump=h||"{}"}(e,t,e.dump,s),l&&(e.dump="&ref_"+c+e.dump)):(function(e,t,n){var r,i,s,o,a,u="",c=e.tag,l=Object.keys(n);for(r=0,i=l.length;r<i;r+=1)a="",0!==r&&(a+=", "),e.condenseFlow&&(a+='"'),o=n[s=l[r]],S(e,t,s,!1,!1)&&(e.dump.length>1024&&(a+="? "),a+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),S(e,t,o,!1,!1)&&(u+=a+=e.dump));e.tag=c,e.dump="{"+u+"}"}(e,t,e.dump),l&&(e.dump="&ref_"+c+" "+e.dump));else if("[object Array]"===u){var p=e.noArrayIndent&&t>0?t-1:t;r&&0!==e.dump.length?(function(e,t,n,r){var i,s,o="",a=e.tag;for(i=0,s=n.length;i<s;i+=1)S(e,t+1,n[i],!0,!0)&&(r&&0===i||(o+=d(e,t)),e.dump&&10===e.dump.charCodeAt(0)?o+="-":o+="- ",o+=e.dump);e.tag=a,e.dump=o||"[]"}(e,p,e.dump,s),l&&(e.dump="&ref_"+c+e.dump)):(function(e,t,n){var r,i,s="",o=e.tag;for(r=0,i=n.length;r<i;r+=1)S(e,t,n[r],!1,!1)&&(0!==r&&(s+=","+(e.condenseFlow?"":" ")),s+=e.dump);e.tag=o,e.dump="["+s+"]"}(e,p,e.dump),l&&(e.dump="&ref_"+c+" "+e.dump))}else{if("[object String]"!==u){if(e.skipInvalid)return!1;throw new i("unacceptable kind of an object to dump "+u)}"?"!==e.tag&&v(e,e.dump,t,o)}null!==e.tag&&"?"!==e.tag&&(e.dump="!<"+e.tag+"> "+e.dump)}return!0}function b(e,t){var n,r,i=[],s=[];for(w(e,i,s),n=0,r=s.length;n<r;n+=1)t.duplicates.push(i[s[n]]);t.usedDuplicates=new Array(r)}function w(e,t,n){var r,i,s;if(null!==e&&"object"==typeof e)if(-1!==(i=t.indexOf(e)))-1===n.indexOf(i)&&n.push(i);else if(t.push(e),Array.isArray(e))for(i=0,s=e.length;i<s;i+=1)w(e[i],t,n);else for(i=0,s=(r=Object.keys(e)).length;i<s;i+=1)w(e[r[i]],t,n)}function k(e,t){var n=new p(t=t||{});return n.noRefs||b(e,n),S(n,0,e,!0,!0)?n.dump+"\n":""}e.exports.dump=k,e.exports.safeDump=function(e,t){return k(e,r.extend({schema:o},t))}},8146:(e,t,n)=>{"use strict";const r=n(6928),i=n(7201),s=n(4124);e.exports=async function(e){const t=r.join(e,"node_modules");if(await i(r.join(t,".yarn-integrity")))return{name:"yarn"};try{return function(e){if("@"===e[0]){const t=e.substr(1).split("@");return{name:`@${t[0]}`,version:t[1]}}const t=e.split("@");return{name:t[0],version:t[1]}}((await s(r.join(t,".modules.yaml"))).packageManager)}catch(e){if("ENOENT"!==e.code)throw e}return await i(t)?{name:"npm"}:null}},8480:(e,t,n)=>{"use strict";var r=n(6274),i=Object.prototype.toString;e.exports=new r("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,n,r,s,o,a=e;for(o=new Array(a.length),t=0,n=a.length;t<n;t+=1){if(r=a[t],"[object Object]"!==i.call(r))return!1;if(1!==(s=Object.keys(r)).length)return!1;o[t]=[s[0],r[s[0]]]}return!0},construct:function(e){if(null===e)return[];var t,n,r,i,s,o=e;for(s=new Array(o.length),t=0,n=o.length;t<n;t+=1)r=o[t],i=Object.keys(r),s[t]=[i[0],r[i[0]]];return s}})},8505:e=>{"use strict";function t(e,t,i){e instanceof RegExp&&(e=n(e,i)),t instanceof RegExp&&(t=n(t,i));var s=r(e,t,i);return s&&{start:s[0],end:s[1],pre:i.slice(0,s[0]),body:i.slice(s[0]+e.length,s[1]),post:i.slice(s[1]+t.length)}}function n(e,t){var n=t.match(e);return n?n[0]:null}function r(e,t,n){var r,i,s,o,a,u=n.indexOf(e),c=n.indexOf(t,u+1),l=u;if(u>=0&&c>0){for(r=[],s=n.length;l>=0&&!a;)l==u?(r.push(l),u=n.indexOf(e,l+1)):1==r.length?a=[r.pop(),c]:((i=r.pop())<s&&(s=i,o=c),c=n.indexOf(t,l+1)),l=u<c&&u>=0?u:c;r.length&&(a=[s,o])}return a}e.exports=t,t.range=r},8508:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.xhrDisabled=t.JSONCompletionItemProvider=t.JSONHoverProvider=void 0,t.addJSONProviders=function(e,t){const n=[new s.PackageJSONContribution(e,t),new i.BowerJSONContribution(e)],r=[];return n.forEach(e=>{const t=e.getDocumentSelector();r.push(o.languages.registerCompletionItemProvider(t,new u(e),'"',":")),r.push(o.languages.registerHoverProvider(t,new a(e)))}),o.Disposable.from(...r)};const r=n(5887),i=n(6126),s=n(9453),o=n(1398);class a{constructor(e){this.jsonContribution=e}provideHover(e,t,n){const i=e.offsetAt(t),s=(0,r.getLocation)(e.getText(),i);if(!s.previousNode)return null;const a=s.previousNode;if(a&&a.offset<=i&&i<=a.offset+a.length){const t=this.jsonContribution.getInfoContribution(e.uri,s);if(t)return t.then(t=>({contents:t||[],range:new o.Range(e.positionAt(a.offset),e.positionAt(a.offset+a.length))}))}return null}}t.JSONHoverProvider=a;class u{constructor(e){this.jsonContribution=e}resolveCompletionItem(e,t){if(this.jsonContribution.resolveSuggestion){const t=this.jsonContribution.resolveSuggestion(this.lastResource,e);if(t)return t}return Promise.resolve(e)}provideCompletionItems(e,t,n){this.lastResource=e.uri;const i=this.getCurrentWord(e,t);let s;const a=[];let u=!1;const c=e.offsetAt(t),l=(0,r.getLocation)(e.getText(),c),h=l.previousNode;s=h&&h.offset<=c&&c<=h.offset+h.length&&("property"===h.type||"string"===h.type||"number"===h.type||"boolean"===h.type||"null"===h.type)?new o.Range(e.positionAt(h.offset),e.positionAt(h.offset+h.length)):new o.Range(e.positionAt(c-i.length),t);const p={},f={add:e=>{const t="string"==typeof e.label?e.label:e.label.label;p[t]||(p[t]=!0,e.range={replacing:s,inserting:new o.Range(s.start,s.start)},a.push(e))},setAsIncomplete:()=>u=!0,error:e=>console.error(e),log:e=>console.log(e)};let d=null;if(l.isAtPropertyKey){const n=(0,r.createScanner)(e.getText(),!0),s=!l.previousNode||!this.hasColonAfter(n,l.previousNode.offset+l.previousNode.length),o=this.isLast(n,e.offsetAt(t));d=this.jsonContribution.collectPropertySuggestions(e.uri,l,i,s,o,f)}else d=0===l.path.length?this.jsonContribution.collectDefaultSuggestions(e.uri,f):this.jsonContribution.collectValueSuggestions(e.uri,l,f);return d?d.then(()=>a.length>0||u?new o.CompletionList(a,u):null):null}getCurrentWord(e,t){let n=t.character-1;const r=e.lineAt(t.line).text;for(;n>=0&&-1===' \t\n\r\v":{[,'.indexOf(r.charAt(n));)n--;return r.substring(n+1,t.character)}isLast(e,t){e.setPosition(t);let n=e.scan();return 10===n&&2===e.getTokenError()&&(n=e.scan()),2===n||17===n}hasColonAfter(e,t){return e.setPosition(t),6===e.scan()}}t.JSONCompletionItemProvider=u,t.xhrDisabled=()=>Promise.reject({responseText:"Use of online resources is disabled."})},8611:e=>{"use strict";e.exports=require("http")},8697:(e,t,n)=>{"use strict";const r=n(7028),i=n(3720),s=n(7612),o=(e="",t="",n=!1)=>{const r=[];if(e=[].concat(e),!(t=[].concat(t)).length)return e;if(!e.length)return n?s.flatten(t).map(e=>`{${e}}`):t;for(const i of e)if(Array.isArray(i))for(const e of i)r.push(o(e,t,n));else for(let e of t)!0===n&&"string"==typeof e&&(e=`{${e}}`),r.push(Array.isArray(e)?o(i,e,n):i+e);return s.flatten(r)};e.exports=(e,t={})=>{const n=void 0===t.rangeLimit?1e3:t.rangeLimit,a=(e,u={})=>{e.queue=[];let c=u,l=u.queue;for(;"brace"!==c.type&&"root"!==c.type&&c.parent;)c=c.parent,l=c.queue;if(e.invalid||e.dollar)return void l.push(o(l.pop(),i(e,t)));if("brace"===e.type&&!0!==e.invalid&&2===e.nodes.length)return void l.push(o(l.pop(),["{}"]));if(e.nodes&&e.ranges>0){const a=s.reduce(e.nodes);if(s.exceedsLimit(...a,t.step,n))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let u=r(...a,t);return 0===u.length&&(u=i(e,t)),l.push(o(l.pop(),u)),void(e.nodes=[])}const h=s.encloseBrace(e);let p=e.queue,f=e;for(;"brace"!==f.type&&"root"!==f.type&&f.parent;)f=f.parent,p=f.queue;for(let t=0;t<e.nodes.length;t++){const n=e.nodes[t];"comma"!==n.type||"brace"!==e.type?"close"!==n.type?n.value&&"open"!==n.type?p.push(o(p.pop(),n.value)):n.nodes&&a(n,e):l.push(o(l.pop(),p,h)):(1===t&&p.push(""),p.push(""))}return p};return s.flatten(a(e))}},8712:(e,t,n)=>{"use strict";const r=n(7028),i=n(7612);e.exports=(e,t={})=>{const n=(e,s={})=>{const o=i.isInvalidBrace(s),a=!0===e.invalid&&!0===t.escapeInvalid,u=!0===o||!0===a,c=!0===t.escapeInvalid?"\\":"";let l="";if(!0===e.isOpen)return c+e.value;if(!0===e.isClose)return console.log("node.isClose",c,e.value),c+e.value;if("open"===e.type)return u?c+e.value:"(";if("close"===e.type)return u?c+e.value:")";if("comma"===e.type)return"comma"===e.prev.type?"":u?e.value:"|";if(e.value)return e.value;if(e.nodes&&e.ranges>0){const n=i.reduce(e.nodes),s=r(...n,{...t,wrap:!1,toRegex:!0,strictZeros:!0});if(0!==s.length)return n.length>1&&s.length>1?`(${s})`:s}if(e.nodes)for(const t of e.nodes)l+=n(t,e);return l};return n(e)}},8725:e=>{"use strict";function t(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=(this.reason||"(unknown reason)")+(this.mark?" "+this.mark.toString():""),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||""}t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t.prototype.toString=function(e){var t=this.name+": ";return t+=this.reason||"(unknown reason)",!e&&this.mark&&(t+=" "+this.mark.toString()),t},e.exports=t},8776:e=>{"use strict";e.exports={MAX_LENGTH:1e4,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:"\n",CHAR_NO_BREAK_SPACE:" ",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"\t",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\ufeff"}},8928:(e,t,n)=>{var r=n(8505);e.exports=function(e){return e?("{}"===e.substr(0,2)&&(e="\\{\\}"+e.substr(2)),g(function(e){return e.split("\\\\").join(i).split("\\{").join(s).split("\\}").join(o).split("\\,").join(a).split("\\.").join(u)}(e),!0).map(l)):[]};var i="\0SLASH"+Math.random()+"\0",s="\0OPEN"+Math.random()+"\0",o="\0CLOSE"+Math.random()+"\0",a="\0COMMA"+Math.random()+"\0",u="\0PERIOD"+Math.random()+"\0";function c(e){return parseInt(e,10)==e?parseInt(e,10):e.charCodeAt(0)}function l(e){return e.split(i).join("\\").split(s).join("{").split(o).join("}").split(a).join(",").split(u).join(".")}function h(e){if(!e)return[""];var t=[],n=r("{","}",e);if(!n)return e.split(",");var i=n.pre,s=n.body,o=n.post,a=i.split(",");a[a.length-1]+="{"+s+"}";var u=h(o);return o.length&&(a[a.length-1]+=u.shift(),a.push.apply(a,u)),t.push.apply(t,a),t}function p(e){return"{"+e+"}"}function f(e){return/^-?0\d/.test(e)}function d(e,t){return e<=t}function m(e,t){return e>=t}function g(e,t){var n=[],i=r("{","}",e);if(!i)return[e];var s=i.pre,a=i.post.length?g(i.post,!1):[""];if(/\$$/.test(i.pre))for(var u=0;u<a.length;u++){var l=s+"{"+i.body+"}"+a[u];n.push(l)}else{var x,y,v=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(i.body),E=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(i.body),C=v||E,D=i.body.indexOf(",")>=0;if(!C&&!D)return i.post.match(/,.*\}/)?g(e=i.pre+"{"+i.body+o+i.post):[e];if(C)x=i.body.split(/\.\./);else if(1===(x=h(i.body)).length&&1===(x=g(x[0],!1).map(p)).length)return a.map(function(e){return i.pre+x[0]+e});if(C){var A=c(x[0]),S=c(x[1]),b=Math.max(x[0].length,x[1].length),w=3==x.length?Math.abs(c(x[2])):1,k=d;S<A&&(w*=-1,k=m);var F=x.some(f);y=[];for(var T=A;k(T,S);T+=w){var _;if(E)"\\"===(_=String.fromCharCode(T))&&(_="");else if(_=String(T),F){var O=b-_.length;if(O>0){var P=new Array(O+1).join("0");_=T<0?"-"+P+_.slice(1):P+_}}y.push(_)}}else{y=[];for(var B=0;B<x.length;B++)y.push.apply(y,g(x[B],!1))}for(B=0;B<y.length;B++)for(u=0;u<a.length;u++)l=s+y[B]+a[u],(!t||C||l)&&n.push(l)}return n}},8962:(e,t,n)=>{"use strict";const r=n(9896),i=n(628),s=n(6928);function o(e){const t=(e||{}).workspaces;return t&&t.packages||(Array.isArray(t)?t:null)}function a(e){const t=s.join(e,"package.json");return r.existsSync(t)?JSON.parse(r.readFileSync(t,"utf8")):null}e.exports=function(e){e||(e=process.cwd());let t=null,n=s.normalize(e);do{const r=o(a(n));if(r){const t=s.relative(n,e);return""===t||i([t],r).length>0?n:null}t=n,n=s.dirname(n)}while(n!==t);return null}},8998:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return null!==e?e:""}})},9001:(e,t,n)=>{"use strict";var r=n(2111),i=n(8725),s=n(499),o=n(919),a=n(7459),u=Object.prototype.hasOwnProperty,c=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,l=/[\x85\u2028\u2029]/,h=/[,\[\]\{\}]/,p=/^(?:!|!!|![a-z\-]+!)$/i,f=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function d(e){return Object.prototype.toString.call(e)}function m(e){return 10===e||13===e}function g(e){return 9===e||32===e}function x(e){return 9===e||32===e||10===e||13===e}function y(e){return 44===e||91===e||93===e||123===e||125===e}function v(e){var t;return 48<=e&&e<=57?e-48:97<=(t=32|e)&&t<=102?t-97+10:-1}function E(e){return 120===e?2:117===e?4:85===e?8:0}function C(e){return 48<=e&&e<=57?e-48:-1}function D(e){return 48===e?"\0":97===e?"":98===e?"\b":116===e||9===e?"\t":110===e?"\n":118===e?"\v":102===e?"\f":114===e?"\r":101===e?"":32===e?" ":34===e?'"':47===e?"/":92===e?"\\":78===e?"":95===e?" ":76===e?"\u2028":80===e?"\u2029":""}function A(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(55296+(e-65536>>10),56320+(e-65536&1023))}for(var S=new Array(256),b=new Array(256),w=0;w<256;w++)S[w]=D(w)?1:0,b[w]=D(w);function k(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||a,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.documents=[]}function F(e,t){return new i(t,new s(e.filename,e.input,e.position,e.line,e.position-e.lineStart))}function T(e,t){throw F(e,t)}function _(e,t){e.onWarning&&e.onWarning.call(null,F(e,t))}var O={YAML:function(e,t,n){var r,i,s;null!==e.version&&T(e,"duplication of %YAML directive"),1!==n.length&&T(e,"YAML directive accepts exactly one argument"),null===(r=/^([0-9]+)\.([0-9]+)$/.exec(n[0]))&&T(e,"ill-formed argument of the YAML directive"),i=parseInt(r[1],10),s=parseInt(r[2],10),1!==i&&T(e,"unacceptable YAML version of the document"),e.version=n[0],e.checkLineBreaks=s<2,1!==s&&2!==s&&_(e,"unsupported YAML version of the document")},TAG:function(e,t,n){var r,i;2!==n.length&&T(e,"TAG directive accepts exactly two arguments"),r=n[0],i=n[1],p.test(r)||T(e,"ill-formed tag handle (first argument) of the TAG directive"),u.call(e.tagMap,r)&&T(e,'there is a previously declared suffix for "'+r+'" tag handle'),f.test(i)||T(e,"ill-formed tag prefix (second argument) of the TAG directive"),e.tagMap[r]=i}};function P(e,t,n,r){var i,s,o,a;if(t<n){if(a=e.input.slice(t,n),r)for(i=0,s=a.length;i<s;i+=1)9===(o=a.charCodeAt(i))||32<=o&&o<=1114111||T(e,"expected valid JSON character");else c.test(a)&&T(e,"the stream contains non-printable characters");e.result+=a}}function B(e,t,n,i){var s,o,a,c;for(r.isObject(n)||T(e,"cannot merge mappings; the provided source object is unacceptable"),a=0,c=(s=Object.keys(n)).length;a<c;a+=1)o=s[a],u.call(t,o)||(t[o]=n[o],i[o]=!0)}function N(e,t,n,r,i,s,o,a){var c,l;if(Array.isArray(i))for(c=0,l=(i=Array.prototype.slice.call(i)).length;c<l;c+=1)Array.isArray(i[c])&&T(e,"nested arrays are not supported inside keys"),"object"==typeof i&&"[object Object]"===d(i[c])&&(i[c]="[object Object]");if("object"==typeof i&&"[object Object]"===d(i)&&(i="[object Object]"),i=String(i),null===t&&(t={}),"tag:yaml.org,2002:merge"===r)if(Array.isArray(s))for(c=0,l=s.length;c<l;c+=1)B(e,t,s[c],n);else B(e,t,s,n);else e.json||u.call(n,i)||!u.call(t,i)||(e.line=o||e.line,e.position=a||e.position,T(e,"duplicated mapping key")),t[i]=s,delete n[i];return t}function I(e){var t;10===(t=e.input.charCodeAt(e.position))?e.position++:13===t?(e.position++,10===e.input.charCodeAt(e.position)&&e.position++):T(e,"a line break is expected"),e.line+=1,e.lineStart=e.position}function R(e,t,n){for(var r=0,i=e.input.charCodeAt(e.position);0!==i;){for(;g(i);)i=e.input.charCodeAt(++e.position);if(t&&35===i)do{i=e.input.charCodeAt(++e.position)}while(10!==i&&13!==i&&0!==i);if(!m(i))break;for(I(e),i=e.input.charCodeAt(e.position),r++,e.lineIndent=0;32===i;)e.lineIndent++,i=e.input.charCodeAt(++e.position)}return-1!==n&&0!==r&&e.lineIndent<n&&_(e,"deficient indentation"),r}function M(e){var t,n=e.position;return!(45!==(t=e.input.charCodeAt(n))&&46!==t||t!==e.input.charCodeAt(n+1)||t!==e.input.charCodeAt(n+2)||(n+=3,0!==(t=e.input.charCodeAt(n))&&!x(t)))}function L(e,t){1===t?e.result+=" ":t>1&&(e.result+=r.repeat("\n",t-1))}function j(e,t){var n,r,i=e.tag,s=e.anchor,o=[],a=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=o),r=e.input.charCodeAt(e.position);0!==r&&45===r&&x(e.input.charCodeAt(e.position+1));)if(a=!0,e.position++,R(e,!0,-1)&&e.lineIndent<=t)o.push(null),r=e.input.charCodeAt(e.position);else if(n=e.line,$(e,t,3,!1,!0),o.push(e.result),R(e,!0,-1),r=e.input.charCodeAt(e.position),(e.line===n||e.lineIndent>t)&&0!==r)T(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break;return!!a&&(e.tag=i,e.anchor=s,e.kind="sequence",e.result=o,!0)}function U(e){var t,n,r,i,s=!1,o=!1;if(33!==(i=e.input.charCodeAt(e.position)))return!1;if(null!==e.tag&&T(e,"duplication of a tag property"),60===(i=e.input.charCodeAt(++e.position))?(s=!0,i=e.input.charCodeAt(++e.position)):33===i?(o=!0,n="!!",i=e.input.charCodeAt(++e.position)):n="!",t=e.position,s){do{i=e.input.charCodeAt(++e.position)}while(0!==i&&62!==i);e.position<e.length?(r=e.input.slice(t,e.position),i=e.input.charCodeAt(++e.position)):T(e,"unexpected end of the stream within a verbatim tag")}else{for(;0!==i&&!x(i);)33===i&&(o?T(e,"tag suffix cannot contain exclamation marks"):(n=e.input.slice(t-1,e.position+1),p.test(n)||T(e,"named tag handle cannot contain such characters"),o=!0,t=e.position+1)),i=e.input.charCodeAt(++e.position);r=e.input.slice(t,e.position),h.test(r)&&T(e,"tag suffix cannot contain flow indicator characters")}return r&&!f.test(r)&&T(e,"tag name cannot contain such characters: "+r),s?e.tag=r:u.call(e.tagMap,n)?e.tag=e.tagMap[n]+r:"!"===n?e.tag="!"+r:"!!"===n?e.tag="tag:yaml.org,2002:"+r:T(e,'undeclared tag handle "'+n+'"'),!0}function H(e){var t,n;if(38!==(n=e.input.charCodeAt(e.position)))return!1;for(null!==e.anchor&&T(e,"duplication of an anchor property"),n=e.input.charCodeAt(++e.position),t=e.position;0!==n&&!x(n)&&!y(n);)n=e.input.charCodeAt(++e.position);return e.position===t&&T(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function $(e,t,n,i,s){var o,a,c,l,h,p,f,d,D=1,w=!1,k=!1;if(null!==e.listener&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,o=a=c=4===n||3===n,i&&R(e,!0,-1)&&(w=!0,e.lineIndent>t?D=1:e.lineIndent===t?D=0:e.lineIndent<t&&(D=-1)),1===D)for(;U(e)||H(e);)R(e,!0,-1)?(w=!0,c=o,e.lineIndent>t?D=1:e.lineIndent===t?D=0:e.lineIndent<t&&(D=-1)):c=!1;if(c&&(c=w||s),1!==D&&4!==n||(f=1===n||2===n?t:t+1,d=e.position-e.lineStart,1===D?c&&(j(e,d)||function(e,t,n){var r,i,s,o,a,u=e.tag,c=e.anchor,l={},h={},p=null,f=null,d=null,m=!1,y=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=l),a=e.input.charCodeAt(e.position);0!==a;){if(r=e.input.charCodeAt(e.position+1),s=e.line,o=e.position,63!==a&&58!==a||!x(r)){if(!$(e,n,2,!1,!0))break;if(e.line===s){for(a=e.input.charCodeAt(e.position);g(a);)a=e.input.charCodeAt(++e.position);if(58===a)x(a=e.input.charCodeAt(++e.position))||T(e,"a whitespace character is expected after the key-value separator within a block mapping"),m&&(N(e,l,h,p,f,null),p=f=d=null),y=!0,m=!1,i=!1,p=e.tag,f=e.result;else{if(!y)return e.tag=u,e.anchor=c,!0;T(e,"can not read an implicit mapping pair; a colon is missed")}}else{if(!y)return e.tag=u,e.anchor=c,!0;T(e,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===a?(m&&(N(e,l,h,p,f,null),p=f=d=null),y=!0,m=!0,i=!0):m?(m=!1,i=!0):T(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,a=r;if((e.line===s||e.lineIndent>t)&&($(e,t,4,!0,i)&&(m?f=e.result:d=e.result),m||(N(e,l,h,p,f,d,s,o),p=f=d=null),R(e,!0,-1),a=e.input.charCodeAt(e.position)),e.lineIndent>t&&0!==a)T(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return m&&N(e,l,h,p,f,null),y&&(e.tag=u,e.anchor=c,e.kind="mapping",e.result=l),y}(e,d,f))||function(e,t){var n,r,i,s,o,a,u,c,l,h,p=!0,f=e.tag,d=e.anchor,m={};if(91===(h=e.input.charCodeAt(e.position)))i=93,a=!1,r=[];else{if(123!==h)return!1;i=125,a=!0,r={}}for(null!==e.anchor&&(e.anchorMap[e.anchor]=r),h=e.input.charCodeAt(++e.position);0!==h;){if(R(e,!0,t),(h=e.input.charCodeAt(e.position))===i)return e.position++,e.tag=f,e.anchor=d,e.kind=a?"mapping":"sequence",e.result=r,!0;p||T(e,"missed comma between flow collection entries"),l=null,s=o=!1,63===h&&x(e.input.charCodeAt(e.position+1))&&(s=o=!0,e.position++,R(e,!0,t)),n=e.line,$(e,t,1,!1,!0),c=e.tag,u=e.result,R(e,!0,t),h=e.input.charCodeAt(e.position),!o&&e.line!==n||58!==h||(s=!0,h=e.input.charCodeAt(++e.position),R(e,!0,t),$(e,t,1,!1,!0),l=e.result),a?N(e,r,m,c,u,l):s?r.push(N(e,null,m,c,u,l)):r.push(u),R(e,!0,t),44===(h=e.input.charCodeAt(e.position))?(p=!0,h=e.input.charCodeAt(++e.position)):p=!1}T(e,"unexpected end of the stream within a flow collection")}(e,f)?k=!0:(a&&function(e,t){var n,i,s,o,a=1,u=!1,c=!1,l=t,h=0,p=!1;if(124===(o=e.input.charCodeAt(e.position)))i=!1;else{if(62!==o)return!1;i=!0}for(e.kind="scalar",e.result="";0!==o;)if(43===(o=e.input.charCodeAt(++e.position))||45===o)1===a?a=43===o?3:2:T(e,"repeat of a chomping mode identifier");else{if(!((s=C(o))>=0))break;0===s?T(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):c?T(e,"repeat of an indentation width identifier"):(l=t+s-1,c=!0)}if(g(o)){do{o=e.input.charCodeAt(++e.position)}while(g(o));if(35===o)do{o=e.input.charCodeAt(++e.position)}while(!m(o)&&0!==o)}for(;0!==o;){for(I(e),e.lineIndent=0,o=e.input.charCodeAt(e.position);(!c||e.lineIndent<l)&&32===o;)e.lineIndent++,o=e.input.charCodeAt(++e.position);if(!c&&e.lineIndent>l&&(l=e.lineIndent),m(o))h++;else{if(e.lineIndent<l){3===a?e.result+=r.repeat("\n",u?1+h:h):1===a&&u&&(e.result+="\n");break}for(i?g(o)?(p=!0,e.result+=r.repeat("\n",u?1+h:h)):p?(p=!1,e.result+=r.repeat("\n",h+1)):0===h?u&&(e.result+=" "):e.result+=r.repeat("\n",h):e.result+=r.repeat("\n",u?1+h:h),u=!0,c=!0,h=0,n=e.position;!m(o)&&0!==o;)o=e.input.charCodeAt(++e.position);P(e,n,e.position,!1)}}return!0}(e,f)||function(e,t){var n,r,i;if(39!==(n=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;0!==(n=e.input.charCodeAt(e.position));)if(39===n){if(P(e,r,e.position,!0),39!==(n=e.input.charCodeAt(++e.position)))return!0;r=e.position,e.position++,i=e.position}else m(n)?(P(e,r,i,!0),L(e,R(e,!1,t)),r=i=e.position):e.position===e.lineStart&&M(e)?T(e,"unexpected end of the document within a single quoted scalar"):(e.position++,i=e.position);T(e,"unexpected end of the stream within a single quoted scalar")}(e,f)||function(e,t){var n,r,i,s,o,a;if(34!==(a=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,n=r=e.position;0!==(a=e.input.charCodeAt(e.position));){if(34===a)return P(e,n,e.position,!0),e.position++,!0;if(92===a){if(P(e,n,e.position,!0),m(a=e.input.charCodeAt(++e.position)))R(e,!1,t);else if(a<256&&S[a])e.result+=b[a],e.position++;else if((o=E(a))>0){for(i=o,s=0;i>0;i--)(o=v(a=e.input.charCodeAt(++e.position)))>=0?s=(s<<4)+o:T(e,"expected hexadecimal character");e.result+=A(s),e.position++}else T(e,"unknown escape sequence");n=r=e.position}else m(a)?(P(e,n,r,!0),L(e,R(e,!1,t)),n=r=e.position):e.position===e.lineStart&&M(e)?T(e,"unexpected end of the document within a double quoted scalar"):(e.position++,r=e.position)}T(e,"unexpected end of the stream within a double quoted scalar")}(e,f)?k=!0:function(e){var t,n,r;if(42!==(r=e.input.charCodeAt(e.position)))return!1;for(r=e.input.charCodeAt(++e.position),t=e.position;0!==r&&!x(r)&&!y(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&T(e,"name of an alias node must contain at least one character"),n=e.input.slice(t,e.position),e.anchorMap.hasOwnProperty(n)||T(e,'unidentified alias "'+n+'"'),e.result=e.anchorMap[n],R(e,!0,-1),!0}(e)?(k=!0,null===e.tag&&null===e.anchor||T(e,"alias node should not have any properties")):function(e,t,n){var r,i,s,o,a,u,c,l,h=e.kind,p=e.result;if(x(l=e.input.charCodeAt(e.position))||y(l)||35===l||38===l||42===l||33===l||124===l||62===l||39===l||34===l||37===l||64===l||96===l)return!1;if((63===l||45===l)&&(x(r=e.input.charCodeAt(e.position+1))||n&&y(r)))return!1;for(e.kind="scalar",e.result="",i=s=e.position,o=!1;0!==l;){if(58===l){if(x(r=e.input.charCodeAt(e.position+1))||n&&y(r))break}else if(35===l){if(x(e.input.charCodeAt(e.position-1)))break}else{if(e.position===e.lineStart&&M(e)||n&&y(l))break;if(m(l)){if(a=e.line,u=e.lineStart,c=e.lineIndent,R(e,!1,-1),e.lineIndent>=t){o=!0,l=e.input.charCodeAt(e.position);continue}e.position=s,e.line=a,e.lineStart=u,e.lineIndent=c;break}}o&&(P(e,i,s,!1),L(e,e.line-a),i=s=e.position,o=!1),g(l)||(s=e.position+1),l=e.input.charCodeAt(++e.position)}return P(e,i,s,!1),!!e.result||(e.kind=h,e.result=p,!1)}(e,f,1===n)&&(k=!0,null===e.tag&&(e.tag="?")),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):0===D&&(k=c&&j(e,d))),null!==e.tag&&"!"!==e.tag)if("?"===e.tag){for(null!==e.result&&"scalar"!==e.kind&&T(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),l=0,h=e.implicitTypes.length;l<h;l+=1)if((p=e.implicitTypes[l]).resolve(e.result)){e.result=p.construct(e.result),e.tag=p.tag,null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);break}}else u.call(e.typeMap[e.kind||"fallback"],e.tag)?(p=e.typeMap[e.kind||"fallback"][e.tag],null!==e.result&&p.kind!==e.kind&&T(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+p.kind+'", not "'+e.kind+'"'),p.resolve(e.result)?(e.result=p.construct(e.result),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):T(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")):T(e,"unknown tag !<"+e.tag+">");return null!==e.listener&&e.listener("close",e),null!==e.tag||null!==e.anchor||k}function J(e){var t,n,r,i,s=e.position,o=!1;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap={},e.anchorMap={};0!==(i=e.input.charCodeAt(e.position))&&(R(e,!0,-1),i=e.input.charCodeAt(e.position),!(e.lineIndent>0||37!==i));){for(o=!0,i=e.input.charCodeAt(++e.position),t=e.position;0!==i&&!x(i);)i=e.input.charCodeAt(++e.position);for(r=[],(n=e.input.slice(t,e.position)).length<1&&T(e,"directive name must not be less than one character in length");0!==i;){for(;g(i);)i=e.input.charCodeAt(++e.position);if(35===i){do{i=e.input.charCodeAt(++e.position)}while(0!==i&&!m(i));break}if(m(i))break;for(t=e.position;0!==i&&!x(i);)i=e.input.charCodeAt(++e.position);r.push(e.input.slice(t,e.position))}0!==i&&I(e),u.call(O,n)?O[n](e,n,r):_(e,'unknown document directive "'+n+'"')}R(e,!0,-1),0===e.lineIndent&&45===e.input.charCodeAt(e.position)&&45===e.input.charCodeAt(e.position+1)&&45===e.input.charCodeAt(e.position+2)?(e.position+=3,R(e,!0,-1)):o&&T(e,"directives end mark is expected"),$(e,e.lineIndent-1,4,!1,!0),R(e,!0,-1),e.checkLineBreaks&&l.test(e.input.slice(s,e.position))&&_(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&M(e)?46===e.input.charCodeAt(e.position)&&(e.position+=3,R(e,!0,-1)):e.position<e.length-1&&T(e,"end of the stream or a document separator is expected")}function X(e,t){t=t||{},0!==(e=String(e)).length&&(10!==e.charCodeAt(e.length-1)&&13!==e.charCodeAt(e.length-1)&&(e+="\n"),65279===e.charCodeAt(0)&&(e=e.slice(1)));var n=new k(e,t),r=e.indexOf("\0");for(-1!==r&&(n.position=r,T(n,"null byte is not allowed in input")),n.input+="\0";32===n.input.charCodeAt(n.position);)n.lineIndent+=1,n.position+=1;for(;n.position<n.length-1;)J(n);return n.documents}function z(e,t,n){null!==t&&"object"==typeof t&&void 0===n&&(n=t,t=null);var r=X(e,n);if("function"!=typeof t)return r;for(var i=0,s=r.length;i<s;i+=1)t(r[i])}function K(e,t){var n=X(e,t);if(0!==n.length){if(1===n.length)return n[0];throw new i("expected a single document in the stream, but found more")}}e.exports.loadAll=z,e.exports.load=K,e.exports.safeLoadAll=function(e,t,n){return"object"==typeof t&&null!==t&&void 0===n&&(n=t,t=null),z(e,t,r.extend({schema:o},n))},e.exports.safeLoad=function(e,t){return K(e,r.extend({schema:o},t))}},9023:e=>{"use strict";e.exports=require("util")},9057:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:js/undefined",{kind:"scalar",resolve:function(){return!0},construct:function(){},predicate:function(e){return void 0===e},represent:function(){return""}})},9106:(e,t,n)=>{var r=n(9140),i=process.cwd,s=null,o=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return s||(s=i.call(process)),s};try{process.cwd()}catch(e){}var a=process.chdir;process.chdir=function(e){s=null,a.call(process,e)},e.exports=function(e){var t,n;function i(t){return t?function(n,r,i){return t.call(e,n,r,function(e){h(e)&&(e=null),i&&i.apply(this,arguments)})}:t}function s(t){return t?function(n,r){try{return t.call(e,n,r)}catch(e){if(!h(e))throw e}}:t}function a(t){return t?function(n,r,i,s){return t.call(e,n,r,i,function(e){h(e)&&(e=null),s&&s.apply(this,arguments)})}:t}function u(t){return t?function(n,r,i){try{return t.call(e,n,r,i)}catch(e){if(!h(e))throw e}}:t}function c(t){return t?function(n,r,i){function s(e,t){t&&(t.uid<0&&(t.uid+=4294967296),t.gid<0&&(t.gid+=4294967296)),i&&i.apply(this,arguments)}return"function"==typeof r&&(i=r,r=null),r?t.call(e,n,r,s):t.call(e,n,s)}:t}function l(t){return t?function(n,r){var i=r?t.call(e,n,r):t.call(e,n);return i.uid<0&&(i.uid+=4294967296),i.gid<0&&(i.gid+=4294967296),i}:t}function h(e){return!e||"ENOSYS"===e.code||!(process.getuid&&0===process.getuid()||"EINVAL"!==e.code&&"EPERM"!==e.code)}r.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&function(e){e.lchmod=function(t,n,i){e.open(t,r.O_WRONLY|r.O_SYMLINK,n,function(t,r){t?i&&i(t):e.fchmod(r,n,function(t){e.close(r,function(e){i&&i(t||e)})})})},e.lchmodSync=function(t,n){var i,s=e.openSync(t,r.O_WRONLY|r.O_SYMLINK,n),o=!0;try{i=e.fchmodSync(s,n),o=!1}finally{if(o)try{e.closeSync(s)}catch(e){}else e.closeSync(s)}return i}}(e),e.lutimes||function(e){r.hasOwnProperty("O_SYMLINK")?(e.lutimes=function(t,n,i,s){e.open(t,r.O_SYMLINK,function(t,r){t?s&&s(t):e.futimes(r,n,i,function(t){e.close(r,function(e){s&&s(t||e)})})})},e.lutimesSync=function(t,n,i){var s,o=e.openSync(t,r.O_SYMLINK),a=!0;try{s=e.futimesSync(o,n,i),a=!1}finally{if(a)try{e.closeSync(o)}catch(e){}else e.closeSync(o)}return s}):(e.lutimes=function(e,t,n,r){r&&process.nextTick(r)},e.lutimesSync=function(){})}(e),e.chown=a(e.chown),e.fchown=a(e.fchown),e.lchown=a(e.lchown),e.chmod=i(e.chmod),e.fchmod=i(e.fchmod),e.lchmod=i(e.lchmod),e.chownSync=u(e.chownSync),e.fchownSync=u(e.fchownSync),e.lchownSync=u(e.lchownSync),e.chmodSync=s(e.chmodSync),e.fchmodSync=s(e.fchmodSync),e.lchmodSync=s(e.lchmodSync),e.stat=c(e.stat),e.fstat=c(e.fstat),e.lstat=c(e.lstat),e.statSync=l(e.statSync),e.fstatSync=l(e.fstatSync),e.lstatSync=l(e.lstatSync),e.lchmod||(e.lchmod=function(e,t,n){n&&process.nextTick(n)},e.lchmodSync=function(){}),e.lchown||(e.lchown=function(e,t,n,r){r&&process.nextTick(r)},e.lchownSync=function(){}),"win32"===o&&(e.rename=(t=e.rename,function(n,r,i){var s=Date.now(),o=0;t(n,r,function a(u){if(u&&("EACCES"===u.code||"EPERM"===u.code)&&Date.now()-s<6e4)return setTimeout(function(){e.stat(r,function(e,s){e&&"ENOENT"===e.code?t(n,r,a):i(u)})},o),void(o<100&&(o+=10));i&&i(u)})})),e.read=function(t){function n(n,r,i,s,o,a){var u;if(a&&"function"==typeof a){var c=0;u=function(l,h,p){if(l&&"EAGAIN"===l.code&&c<10)return c++,t.call(e,n,r,i,s,o,u);a.apply(this,arguments)}}return t.call(e,n,r,i,s,o,u)}return n.__proto__=t,n}(e.read),e.readSync=(n=e.readSync,function(t,r,i,s,o){for(var a=0;;)try{return n.call(e,t,r,i,s,o)}catch(e){if("EAGAIN"===e.code&&a<10){a++;continue}throw e}})}},9140:e=>{"use strict";e.exports=require("constants")},9278:e=>{"use strict";e.exports=require("net")},9323:(e,t,n)=>{(()=>{var e={46:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,{signal:n}={}){return new Promise((r,i)=>{function s(){null==n||n.removeEventListener("abort",s),e.removeListener(t,o),e.removeListener("error",a)}function o(...e){s(),r(e)}function a(e){s(),i(e)}null==n||n.addEventListener("abort",s),e.on(t,o),e.on("error",a)})}},54:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};const i=n(361),s=r(n(374)),o=r(n(304)),a=s.default("agent-base");function u(){const{stack:e}=new Error;return"string"==typeof e&&e.split("\n").some(e=>-1!==e.indexOf("(https.js:")||-1!==e.indexOf("node:https:"))}function c(e,t){return new c.Agent(e,t)}!function(e){class t extends i.EventEmitter{constructor(e,t){super();let n=t;"function"==typeof e?this.callback=e:e&&(n=e),this.timeout=null,n&&"number"==typeof n.timeout&&(this.timeout=n.timeout),this.maxFreeSockets=1,this.maxSockets=1,this.maxTotalSockets=1/0,this.sockets={},this.freeSockets={},this.requests={},this.options={}}get defaultPort(){return"number"==typeof this.explicitDefaultPort?this.explicitDefaultPort:u()?443:80}set defaultPort(e){this.explicitDefaultPort=e}get protocol(){return"string"==typeof this.explicitProtocol?this.explicitProtocol:u()?"https:":"http:"}set protocol(e){this.explicitProtocol=e}callback(e,t,n){throw new Error('"agent-base" has no default implementation, you must subclass and override `callback()`')}addRequest(e,t){const n=Object.assign({},t);"boolean"!=typeof n.secureEndpoint&&(n.secureEndpoint=u()),null==n.host&&(n.host="localhost"),null==n.port&&(n.port=n.secureEndpoint?443:80),null==n.protocol&&(n.protocol=n.secureEndpoint?"https:":"http:"),n.host&&n.path&&delete n.path,delete n.agent,delete n.hostname,delete n._defaultAgent,delete n.defaultPort,delete n.createConnection,e._last=!0,e.shouldKeepAlive=!1;let r=!1,i=null;const s=n.timeout||this.timeout,c=t=>{e._hadError||(e.emit("error",t),e._hadError=!0)},l=()=>{i=null,r=!0;const e=new Error(`A "socket" was not created for HTTP request before ${s}ms`);e.code="ETIMEOUT",c(e)},h=e=>{r||(null!==i&&(clearTimeout(i),i=null),c(e))},p=t=>{if(r)return;if(null!=i&&(clearTimeout(i),i=null),s=t,Boolean(s)&&"function"==typeof s.addRequest)return a("Callback returned another Agent instance %o",t.constructor.name),void t.addRequest(e,n);var s;if(t)return t.once("free",()=>{this.freeSocket(t,n)}),void e.onSocket(t);const o=new Error(`no Duplex stream was returned to agent-base for \`${e.method} ${e.path}\``);c(o)};if("function"==typeof this.callback){this.promisifiedCallback||(this.callback.length>=3?(a("Converting legacy callback function to promise"),this.promisifiedCallback=o.default(this.callback)):this.promisifiedCallback=this.callback),"number"==typeof s&&s>0&&(i=setTimeout(l,s)),"port"in n&&"number"!=typeof n.port&&(n.port=Number(n.port));try{a("Resolving socket for %o request: %o",n.protocol,`${e.method} ${e.path}`),Promise.resolve(this.promisifiedCallback(e,n)).then(p,h)}catch(e){Promise.reject(e).catch(h)}}else c(new Error("`callback` is not defined"))}freeSocket(e,t){a("Freeing socket %o %o",e.constructor.name,t),e.destroy()}destroy(){a("Destroying agent %o",this.constructor.name)}}e.Agent=t,e.prototype=e.Agent.prototype}(c||(c={})),e.exports=c},304:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t,n){return new Promise((r,i)=>{e.call(this,t,n,(e,t)=>{e?i(e):r(t)})})}}},370:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,s){function o(e){try{u(r.next(e))}catch(e){s(e)}}function a(e){try{u(r.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(o,a)}u((r=r.apply(e,t||[])).next())})},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=i(n(808)),o=i(n(404)),a=i(n(310)),u=i(n(374)),c=i(n(46)),l=n(54),h=(0,u.default)("http-proxy-agent");class p extends l.Agent{constructor(e){let t;if(t="string"==typeof e?a.default.parse(e):e,!t)throw new Error("an HTTP(S) proxy server `host` and `port` must be specified!");h("Creating new HttpProxyAgent instance: %o",t),super(t);const n=Object.assign({},t);var r;this.secureProxy=t.secureProxy||"string"==typeof(r=n.protocol)&&/^https:?$/i.test(r),n.host=n.hostname||n.host,"string"==typeof n.port&&(n.port=parseInt(n.port,10)),!n.port&&n.host&&(n.port=this.secureProxy?443:80),n.host&&n.path&&(delete n.path,delete n.pathname),this.proxy=n}callback(e,t){return r(this,void 0,void 0,function*(){const{proxy:n,secureProxy:r}=this,i=a.default.parse(e.path);let u;if(i.protocol||(i.protocol="http:"),i.hostname||(i.hostname=t.hostname||t.host||null),null==i.port&&(t.port,1)&&(i.port=String(t.port)),"80"===i.port&&(i.port=""),e.path=a.default.format(i),n.auth&&e.setHeader("Proxy-Authorization",`Basic ${Buffer.from(n.auth).toString("base64")}`),r?(h("Creating `tls.Socket`: %o",n),u=o.default.connect(n)):(h("Creating `net.Socket`: %o",n),u=s.default.connect(n)),e._header){let t,n;h("Regenerating stored HTTP header string for request"),e._header=null,e._implicitHeader(),e.output&&e.output.length>0?(h("Patching connection write() output buffer with updated header"),t=e.output[0],n=t.indexOf("\r\n\r\n")+4,e.output[0]=e._header+t.substring(n),h("Output buffer: %o",e.output)):e.outputData&&e.outputData.length>0&&(h("Patching connection write() output buffer with updated header"),t=e.outputData[0].data,n=t.indexOf("\r\n\r\n")+4,e.outputData[0].data=e._header+t.substring(n),h("Output buffer: %o",e.outputData[0].data))}return yield(0,c.default)(u,"connect"),u})}}t.default=p},201:function(e,t,n){"use strict";const r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(370));function i(e){return new r.default(e)}!function(e){e.HttpProxyAgent=r.default,e.prototype=r.default.prototype}(i||(i={})),e.exports=i},146:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,s){function o(e){try{u(r.next(e))}catch(e){s(e)}}function a(e){try{u(r.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(o,a)}u((r=r.apply(e,t||[])).next())})},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=i(n(808)),o=i(n(404)),a=i(n(310)),u=i(n(491)),c=i(n(374)),l=n(54),h=i(n(829)),p=c.default("https-proxy-agent:agent");class f extends l.Agent{constructor(e){let t;if(t="string"==typeof e?a.default.parse(e):e,!t)throw new Error("an HTTP(S) proxy server `host` and `port` must be specified!");p("creating new HttpsProxyAgent instance: %o",t),super(t);const n=Object.assign({},t);var r;this.secureProxy=t.secureProxy||"string"==typeof(r=n.protocol)&&/^https:?$/i.test(r),n.host=n.hostname||n.host,"string"==typeof n.port&&(n.port=parseInt(n.port,10)),!n.port&&n.host&&(n.port=this.secureProxy?443:80),this.secureProxy&&!("ALPNProtocols"in n)&&(n.ALPNProtocols=["http 1.1"]),n.host&&n.path&&(delete n.path,delete n.pathname),this.proxy=n}callback(e,t){return r(this,void 0,void 0,function*(){const{proxy:n,secureProxy:r}=this;let i;r?(p("Creating `tls.Socket`: %o",n),i=o.default.connect(n)):(p("Creating `net.Socket`: %o",n),i=s.default.connect(n));const a=Object.assign({},n.headers);let c=`CONNECT ${t.host}:${t.port} HTTP/1.1\r\n`;n.auth&&(a["Proxy-Authorization"]=`Basic ${Buffer.from(n.auth).toString("base64")}`);let{host:l,port:f,secureEndpoint:m}=t;(function(e,t){return Boolean(!t&&80===e||t&&443===e)})(f,m)||(l+=`:${f}`),a.Host=l,a.Connection="close";for(const e of Object.keys(a))c+=`${e}: ${a[e]}\r\n`;const g=h.default(i);i.write(`${c}\r\n`);const{statusCode:x,buffered:y}=yield g;if(200===x){if(e.once("socket",d),t.secureEndpoint){p("Upgrading socket connection to TLS");const e=t.servername||t.host;return o.default.connect(Object.assign(Object.assign({},function(e,...t){const n={};let r;for(r in e)t.includes(r)||(n[r]=e[r]);return n}(t,"host","hostname","path","port")),{socket:i,servername:e}))}return i}i.destroy();const v=new s.default.Socket({writable:!1});return v.readable=!0,e.once("socket",e=>{p("replaying proxy buffer for failed request"),u.default(e.listenerCount("data")>0),e.push(y),e.push(null)}),v})}}function d(e){e.resume()}t.default=f},18:function(e,t,n){"use strict";const r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(146));function i(e){return new r.default(e)}!function(e){e.HttpsProxyAgent=r.default,e.prototype=r.default.prototype}(i||(i={})),e.exports=i},829:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const i=r(n(374)).default("https-proxy-agent:parse-proxy-response");t.default=function(e){return new Promise((t,n)=>{let r=0;const s=[];function o(){const n=e.read();n?function(e){s.push(e),r+=e.length;const n=Buffer.concat(s,r);if(-1===n.indexOf("\r\n\r\n"))return i("have not received end of HTTP headers yet..."),void o();const a=n.toString("ascii",0,n.indexOf("\r\n")),u=+a.split(" ")[1];i("got proxy server response: %o",a),t({statusCode:u,buffered:n})}(n):e.once("readable",o)}function a(e){i("onclose had error %o",e)}function u(){i("onend")}e.on("error",function t(r){e.removeListener("end",u),e.removeListener("error",t),e.removeListener("close",a),e.removeListener("readable",o),i("onerror %o",r),n(r)}),e.on("close",a),e.on("end",u),o()})}},539:function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),s=this&&this.__assign||function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},s.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.getErrorStatusDescription=t.xhr=t.configure=void 0;var o=n(685),a=n(687),u=n(310),c=n(472),l=n(796),h=n(201),p=n(18);if(process.env.VSCODE_NLS_CONFIG){var f=process.env.VSCODE_NLS_CONFIG;c.config(JSON.parse(f))}var d=c.loadMessageBundle(),m=void 0,g=!0;function x(e){var t;return new Promise(function(n,r){var i=(0,u.parse)(e.url),s={hostname:i.hostname,agent:!!e.agent&&e.agent,port:i.port?parseInt(i.port):"https:"===i.protocol?443:80,path:i.path,method:e.type||"GET",headers:e.headers,rejectUnauthorized:"boolean"!=typeof e.strictSSL||e.strictSSL};e.user&&e.password&&(s.auth=e.user+":"+e.password);var c=function(r){if(r.statusCode>=300&&r.statusCode<400&&e.followRedirects&&e.followRedirects>0&&r.headers.location){var s=r.headers.location;s.startsWith("/")&&(s=(0,u.format)({protocol:i.protocol,hostname:i.hostname,port:i.port,pathname:s})),n(x(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.forEach(function(t){return Object.keys(t).forEach(function(n){return e[n]=t[n]})}),e}({},e,{url:s,followRedirects:e.followRedirects-1})))}else n({req:t,res:r})};(t="https:"===i.protocol?a.request(s,c):o.request(s,c)).on("error",r),e.timeout&&t.setTimeout(e.timeout),e.data&&t.write(e.data),t.end(),e.token&&(e.token.isCancellationRequested&&t.destroy(new y),e.token.onCancellationRequested(function(){t.destroy(new y)}))})}t.configure=function(e,t){m=e,g=t},t.xhr=function(e){return"boolean"!=typeof(e=s({},e)).strictSSL&&(e.strictSSL=g),e.agent||(e.agent=function(e,t){void 0===t&&(t={});var n=(0,u.parse)(e),r=t.proxyUrl||function(e){return"http:"===e.protocol?process.env.HTTP_PROXY||process.env.http_proxy||null:"https:"===e.protocol&&(process.env.HTTPS_PROXY||process.env.https_proxy||process.env.HTTP_PROXY||process.env.http_proxy)||null}(n);if(!r)return null;var i=(0,u.parse)(r);if(!/^https?:$/.test(i.protocol))return null;var s={host:i.hostname,port:Number(i.port),auth:i.auth,rejectUnauthorized:"boolean"!=typeof t.strictSSL||t.strictSSL,protocol:i.protocol};return"http:"===n.protocol?h(s):p(s)}(e.url,{proxyUrl:m,strictSSL:g})),"number"!=typeof e.followRedirects&&(e.followRedirects=5),x(e).then(function(n){return new Promise(function(r,i){var s,o,a=n.res,c=a,h=!1,p=a.headers&&a.headers["content-encoding"];if(p&&(s=e.type,o=n.res.statusCode,!("HEAD"===s||o>=100&&o<200||204===o||304===o))){var f={flush:l.constants.Z_SYNC_FLUSH,finishFlush:l.constants.Z_SYNC_FLUSH};if("gzip"===p){var m=l.createGunzip(f);a.pipe(m),c=m}else if("deflate"===p){var g=l.createInflate(f);a.pipe(g),c=g}}var x=[];c.on("data",function(e){return x.push(e)}),c.on("end",function(){if(!h){if(h=!0,e.followRedirects>0&&(a.statusCode>=300&&a.statusCode<=303||307===a.statusCode)){var n=a.headers.location;if(n.startsWith("/")){var s=(0,u.parse)(e.url);n=(0,u.format)({protocol:s.protocol,hostname:s.hostname,port:s.port,pathname:n})}if(n){var o={type:e.type,url:n,user:e.user,password:e.password,headers:e.headers,timeout:e.timeout,followRedirects:e.followRedirects-1,data:e.data,token:e.token};return void(0,t.xhr)(o).then(r,i)}}var c=Buffer.concat(x),l={responseText:c.toString(),body:c,status:a.statusCode,headers:a.headers||{}};a.statusCode>=200&&a.statusCode<300||1223===a.statusCode?r(l):i(l)}}),c.on("error",function(t){var n;n=y.is(t)?t:{responseText:d("error","Unable to access {0}. Error: {1}",e.url,t.message),body:Buffer.concat(x),status:500,headers:{}},h=!0,i(n)}),e.token&&(e.token.isCancellationRequested&&c.destroy(new y),e.token.onCancellationRequested(function(){c.destroy(new y)}))})},function(t){var n;return n=y.is(t)?t:{responseText:e.agent?d("error.cannot.connect.proxy","Unable to connect to {0} through a proxy. Error: {1}",e.url,t.message):d("error.cannot.connect","Unable to connect to {0}. Error: {1}",e.url,t.message),body:Buffer.concat([]),status:404,headers:{}},Promise.reject(n)})},t.getErrorStatusDescription=function(e){if(!(e<400))switch(e){case 400:return d("status.400","Bad request. The request cannot be fulfilled due to bad syntax.");case 401:return d("status.401","Unauthorized. The server is refusing to respond.");case 403:return d("status.403","Forbidden. The server is refusing to respond.");case 404:return d("status.404","Not Found. The requested location could not be found.");case 405:return d("status.405","Method not allowed. A request was made using a request method not supported by that location.");case 406:return d("status.406","Not Acceptable. The server can only generate a response that is not accepted by the client.");case 407:return d("status.407","Proxy Authentication Required. The client must first authenticate itself with the proxy.");case 408:return d("status.408","Request Timeout. The server timed out waiting for the request.");case 409:return d("status.409","Conflict. The request could not be completed because of a conflict in the request.");case 410:return d("status.410","Gone. The requested page is no longer available.");case 411:return d("status.411",'Length Required. The "Content-Length" is not defined.');case 412:return d("status.412","Precondition Failed. The precondition given in the request evaluated to false by the server.");case 413:return d("status.413","Request Entity Too Large. The server will not accept the request, because the request entity is too large.");case 414:return d("status.414","Request-URI Too Long. The server will not accept the request, because the URL is too long.");case 415:return d("status.415","Unsupported Media Type. The server will not accept the request, because the media type is not supported.");case 500:return d("status.500","Internal Server Error.");case 501:return d("status.501","Not Implemented. The server either does not recognize the request method, or it lacks the ability to fulfill the request.");case 502:return d("status.502","Bad Gateway. The upstream server did not respond.");case 503:return d("status.503","Service Unavailable. The server is currently unavailable (overloaded or down).");default:return d("status.416","HTTP status code {0}",e)}};var y=function(e){function t(){var n=e.call(this,"The user aborted a request")||this;return n.name="AbortError",Object.setPrototypeOf(n,t.prototype),n}return i(t,e),t.is=function(e){return e instanceof t},t}(Error)},800:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.config=t.loadMessageBundle=t.localize=t.format=t.setPseudo=t.isPseudo=t.isDefined=t.BundleFormat=t.MessageFormat=void 0;var r,i,s,o=n(926);function a(e){return void 0!==e}function u(e,n){return t.isPseudo&&(e="［"+e.replace(/[aouei]/g,"$&$&")+"］"),0===n.length?e:e.replace(/\{(\d+)\}/g,function(e,t){var r=t[0],i=n[r],s=e;return"string"==typeof i?s=i:"number"!=typeof i&&"boolean"!=typeof i&&null!=i||(s=String(i)),s})}(s=t.MessageFormat||(t.MessageFormat={})).file="file",s.bundle="bundle",s.both="both",(i=t.BundleFormat||(t.BundleFormat={})).standalone="standalone",i.languagePack="languagePack",function(e){e.is=function(e){var t=e;return t&&a(t.key)&&a(t.comment)}}(r||(r={})),t.isDefined=a,t.isPseudo=!1,t.setPseudo=function(e){t.isPseudo=e},t.format=u,t.localize=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return u(t,n)},t.loadMessageBundle=function(e){return(0,o.default)().loadMessageBundle(e)},t.config=function(e){return(0,o.default)().config(e)}},926:(e,t)=>{"use strict";var n;function r(){if(void 0===n)throw new Error("No runtime abstraction layer installed");return n}Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.install=function(e){if(void 0===e)throw new Error("No runtime abstraction layer provided");n=e}}(r||(r={})),t.default=r},472:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.config=t.loadMessageBundle=t.BundleFormat=t.MessageFormat=void 0;var r=n(17),i=n(147),s=n(926),o=n(800),a=n(800);Object.defineProperty(t,"MessageFormat",{enumerable:!0,get:function(){return a.MessageFormat}}),Object.defineProperty(t,"BundleFormat",{enumerable:!0,get:function(){return a.BundleFormat}});var u,c,l=Object.prototype.toString;function h(e){return"[object String]"===l.call(e)}function p(e){return JSON.parse(i.readFileSync(e,"utf8"))}function f(e){return function(t,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(e){return"[object Number]"===l.call(e)}(t)?t>=e.length?void console.error("Broken localize call found. Index out of bounds. Stacktrace is\n: ".concat(new Error("").stack)):(0,o.format)(e[t],r):h(n)?(console.warn("Message ".concat(n," didn't get externalized correctly.")),(0,o.format)(n,r)):void console.error("Broken localize call found. Stacktrace is\n: ".concat(new Error("").stack))}}function d(e,t){return u[e]=t,t}function m(e){try{return function(e){var t=p(r.join(e,"nls.metadata.json")),n=Object.create(null);for(var i in t){var s=t[i];n[i]=s.messages}return n}(e)}catch(e){return void console.log("Generating default bundle from meta data failed.",e)}}function g(e,t){var n;if(!0===c.languagePackSupport&&void 0!==c.cacheRoot&&void 0!==c.languagePackId&&void 0!==c.translationsConfigFile&&void 0!==c.translationsConfig)try{n=function(e,t){var n,s,o,a=r.join(c.cacheRoot,"".concat(e.id,"-").concat(e.hash,".json")),u=!1,l=!1;try{return n=JSON.parse(i.readFileSync(a,{encoding:"utf8",flag:"r"})),s=a,o=new Date,i.utimes(s,o,o,function(){}),n}catch(e){if("ENOENT"===e.code)l=!0;else{if(!(e instanceof SyntaxError))throw e;console.log("Syntax error parsing message bundle: ".concat(e.message,".")),i.unlink(a,function(e){e&&console.error("Deleting corrupted bundle ".concat(a," failed."))}),u=!0}}if(n=function(e,t){var n=c.translationsConfig[e.id];if(n){var i=p(n).contents,s=p(r.join(t,"nls.metadata.json")),o=Object.create(null);for(var a in s){var u=s[a],l=i["".concat(e.outDir,"/").concat(a)];if(l){for(var f=[],d=0;d<u.keys.length;d++){var m=u.keys[d],g=l[h(m)?m:m.key];void 0===g&&(g=u.messages[d]),f.push(g)}o[a]=f}else o[a]=u.messages}return o}}(e,t),!n||u)return n;if(l)try{i.writeFileSync(a,JSON.stringify(n),{encoding:"utf8",flag:"wx"})}catch(e){if("EEXIST"===e.code)return n;throw e}return n}(e,t)}catch(e){console.log("Load or create bundle failed ",e)}if(!n){if(c.languagePackSupport)return m(t);var s=function(e){for(var t=c.language;t;){var n=r.join(e,"nls.bundle.".concat(t,".json"));if(i.existsSync(n))return n;var s=t.lastIndexOf("-");t=s>0?t.substring(0,s):void 0}if(void 0===t&&(n=r.join(e,"nls.bundle.json"),i.existsSync(n)))return n}(t);if(s)try{return p(s)}catch(e){console.log("Loading in the box message bundle failed.",e)}n=m(t)}return n}function x(e){if(!e)return o.localize;var t=r.extname(e);if(t&&(e=e.substr(0,e.length-t.length)),c.messageFormat===o.MessageFormat.both||c.messageFormat===o.MessageFormat.bundle){var n=function(e){for(var t,n=r.dirname(e);t=r.join(n,"nls.metadata.header.json"),!i.existsSync(t);){var s=r.dirname(n);if(s===n){t=void 0;break}n=s}return t}(e);if(n){var s=r.dirname(n),a=u[s];if(void 0===a)try{var l=JSON.parse(i.readFileSync(n,"utf8"));try{var h=g(l,s);a=d(s,h?{header:l,nlsBundle:h}:null)}catch(e){console.error("Failed to load nls bundle",e),a=d(s,null)}}catch(e){console.error("Failed to read header file",e),a=d(s,null)}if(a){var m=e.substr(s.length+1).replace(/\\/g,"/"),x=a.nlsBundle[m];return void 0===x?(console.error("Messages for file ".concat(e," not found. See console for details.")),function(){return"Messages not found."}):f(x)}}}if(c.messageFormat===o.MessageFormat.both||c.messageFormat===o.MessageFormat.file)try{var y=p(function(e){var t;if(c.cacheLanguageResolution&&t);else{if(o.isPseudo||!c.language)t=".nls.json";else for(var n=c.language;n;){var r=".nls."+n+".json";if(i.existsSync(e+r)){t=r;break}var s=n.lastIndexOf("-");s>0?n=n.substring(0,s):(t=".nls.json",n=null)}c.cacheLanguageResolution}return e+t}(e));return Array.isArray(y)?f(y):(0,o.isDefined)(y.messages)&&(0,o.isDefined)(y.keys)?f(y.messages):(console.error("String bundle '".concat(e,"' uses an unsupported format.")),function(){return"File bundle has unsupported format. See console for details"})}catch(e){"ENOENT"!==e.code&&console.error("Failed to load single file bundle",e)}return console.error("Failed to load message bundle for file ".concat(e)),function(){return"Failed to load message bundle. See console for details."}}function y(e){return e&&(h(e.locale)&&(c.locale=e.locale.toLowerCase(),c.language=c.locale,u=Object.create(null)),void 0!==e.messageFormat&&(c.messageFormat=e.messageFormat),e.bundleFormat===o.BundleFormat.standalone&&!0===c.languagePackSupport&&(c.languagePackSupport=!1)),(0,o.setPseudo)("pseudo"===c.locale),x}!function(){if(c={locale:void 0,language:void 0,languagePackSupport:!1,cacheLanguageResolution:!0,messageFormat:o.MessageFormat.bundle},h(process.env.VSCODE_NLS_CONFIG))try{var e=JSON.parse(process.env.VSCODE_NLS_CONFIG),t=void 0;if(e.availableLanguages){var n=e.availableLanguages["*"];h(n)&&(t=n)}if(h(e.locale)&&(c.locale=e.locale.toLowerCase()),void 0===t?c.language=c.locale:"en"!==t&&(c.language=t),function(e){return!0===e||!1===e}(e._languagePackSupport)&&(c.languagePackSupport=e._languagePackSupport),h(e._cacheRoot)&&(c.cacheRoot=e._cacheRoot),h(e._languagePackId)&&(c.languagePackId=e._languagePackId),h(e._translationsConfigFile)){c.translationsConfigFile=e._translationsConfigFile;try{c.translationsConfig=p(c.translationsConfigFile)}catch(t){if(e._corruptedFile){var s=r.dirname(e._corruptedFile);i.exists(s,function(t){t&&i.writeFile(e._corruptedFile,"corrupted","utf8",function(e){console.error(e)})})}}}}catch(e){}(0,o.setPseudo)("pseudo"===c.locale),u=Object.create(null)}(),t.loadMessageBundle=x,t.config=y,s.default.install(Object.freeze({loadMessageBundle:x,config:y}))},374:(e,t)=>{function n(){}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return n}},491:e=>{"use strict";e.exports=n(2613)},361:e=>{"use strict";e.exports=n(4434)},147:e=>{"use strict";e.exports=n(9896)},685:e=>{"use strict";e.exports=n(8611)},687:e=>{"use strict";e.exports=n(5692)},808:e=>{"use strict";e.exports=n(9278)},17:e=>{"use strict";e.exports=n(6928)},404:e=>{"use strict";e.exports=n(4756)},310:e=>{"use strict";e.exports=n(7016)},796:e=>{"use strict";e.exports=n(3106)}},r={},i=function t(n){var i=r[n];if(void 0!==i)return i.exports;var s=r[n]={exports:{}};return e[n].call(s.exports,s,s.exports,t),s.exports}(539),s=t;for(var o in i)s[o]=i[o];i.__esModule&&Object.defineProperty(s,"__esModule",{value:!0})})()},9453:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&i(t,e,n[o]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.PackageJSONContribution=void 0;const a=n(1398),u=o(n(5317)),c=n(6928),l=n(3977),h="Visual Studio Code";t.PackageJSONContribution=class{getDocumentSelector(){return[{language:"json",scheme:"*",pattern:"**/package.json"}]}constructor(e,t){this.xhr=e,this.npmCommandPath=t,this.mostDependedOn=["lodash","async","underscore","request","commander","express","debug","chalk","colors","q","coffee-script","mkdirp","optimist","through2","yeoman-generator","moment","bluebird","glob","gulp-util","minimist","cheerio","pug","redis","node-uuid","socket","io","uglify-js","winston","through","fs-extra","handlebars","body-parser","rimraf","mime","semver","mongodb","jquery","grunt","connect","yosay","underscore","string","xml2js","ejs","mongoose","marked","extend","mocha","superagent","js-yaml","xtend","shelljs","gulp","yargs","browserify","minimatch","react","less","prompt","inquirer","ws","event-stream","inherits","mysql","esprima","jsdom","stylus","when","readable-stream","aws-sdk","concat-stream","chai","Thenable","wrench"],this.knownScopes=["@types","@angular","@babel","@nuxtjs","@vue","@bazel"]}collectDefaultSuggestions(e,t){const n=new a.CompletionItem(a.l10n.t("Default package.json"));return n.kind=a.CompletionItemKind.Module,n.insertText=new a.SnippetString(JSON.stringify({name:"${1:name}",description:"${2:description}",authors:"${3:author}",version:"${4:1.0.0}",main:"${5:pathToMain}",dependencies:{}},null,"\t")),t.add(n),Promise.resolve(null)}isEnabled(){return this.npmCommandPath||this.onlineEnabled()}onlineEnabled(){return!!a.workspace.getConfiguration("npm").get("fetchOnlinePackageInfo")}collectPropertySuggestions(e,t,n,r,i,s){if(!this.isEnabled())return null;if(t.matches(["dependencies"])||t.matches(["devDependencies"])||t.matches(["optionalDependencies"])||t.matches(["peerDependencies"])){let e;if(n.length>0){if("@"===n[0]){if(-1!==n.indexOf("/"))return this.collectScopedPackages(n,r,i,s);for(const e of this.knownScopes){const t=new a.CompletionItem(e);t.kind=a.CompletionItemKind.Property,t.insertText=(new a.SnippetString).appendText(`"${e}/`).appendTabstop().appendText('"'),t.filterText=JSON.stringify(e),t.documentation="",t.command={title:"",command:"editor.action.triggerSuggest"},s.add(t)}s.setAsIncomplete()}return e=`https://registry.npmjs.org/-/v1/search?size=40&text=${encodeURIComponent(n)}`,this.xhr({url:e,headers:{agent:h}}).then(e=>{if(200!==e.status)return s.error(a.l10n.t("Request to the NPM repository failed: {0}",e.responseText)),0;try{const t=JSON.parse(e.responseText);if(t&&t.objects&&Array.isArray(t.objects)){const e=t.objects;for(const t of e)this.processPackage(t.package,r,i,s)}}catch(e){}s.setAsIncomplete()},e=>(s.error(a.l10n.t("Request to the NPM repository failed: {0}",e.responseText)),0))}return this.mostDependedOn.forEach(e=>{const t=(new a.SnippetString).appendText(JSON.stringify(e));r&&(t.appendText(': "').appendTabstop().appendText('"'),i||t.appendText(","));const n=new a.CompletionItem(e);n.kind=a.CompletionItemKind.Property,n.insertText=t,n.filterText=JSON.stringify(e),n.documentation="",s.add(n)}),this.collectScopedPackages(n,r,i,s),s.setAsIncomplete(),Promise.resolve(null)}return null}collectScopedPackages(e,t,n,r){const i=e.split("/");if(2===i.length&&i[0].length>1){const e=i[0].substr(1);let s=i[1];s.length<4&&(s="");const o=`https://registry.npmjs.com/-/v1/search?text=scope:${e}%20${s}&size=250`;return this.xhr({url:o,headers:{agent:h}}).then(e=>{if(200===e.status){try{const i=JSON.parse(e.responseText);if(i&&Array.isArray(i.objects)){const e=i.objects;for(const i of e)this.processPackage(i.package,t,n,r)}}catch(e){}r.setAsIncomplete()}else r.error(a.l10n.t("Request to the NPM repository failed: {0}",e.responseText));return null})}return Promise.resolve(null)}async collectValueSuggestions(e,t,n){if(!this.isEnabled())return null;if(t.matches(["dependencies","*"])||t.matches(["devDependencies","*"])||t.matches(["optionalDependencies","*"])||t.matches(["peerDependencies","*"])){const r=t.path[t.path.length-1];if("string"==typeof r){const t=await this.fetchPackageInfo(r,e);if(t&&t.version){let e=JSON.stringify(t.version),r=new a.CompletionItem(e);r.kind=a.CompletionItemKind.Property,r.insertText=e,r.documentation=a.l10n.t("The currently latest version of the package"),n.add(r),e=JSON.stringify("^"+t.version),r=new a.CompletionItem(e),r.kind=a.CompletionItemKind.Property,r.insertText=e,r.documentation=a.l10n.t("Matches the most recent major version (1.x.x)"),n.add(r),e=JSON.stringify("~"+t.version),r=new a.CompletionItem(e),r.kind=a.CompletionItemKind.Property,r.insertText=e,r.documentation=a.l10n.t("Matches the most recent minor version (1.2.x)"),n.add(r)}}}return null}getDocumentation(e,t,n,r){const i=new a.MarkdownString;return e&&i.appendText(e),t&&(i.appendText("\n\n"),i.appendText(n?a.l10n.t("Latest version: {0} published {1}",t,(0,l.fromNow)(Date.parse(n),!0,!0)):a.l10n.t("Latest version: {0}",t))),r&&(i.appendText("\n\n"),i.appendText(r)),i}resolveSuggestion(e,t){if(t.kind===a.CompletionItemKind.Property&&!t.documentation){let n=t.label;return"string"!=typeof n&&(n=n.label),this.fetchPackageInfo(n,e).then(e=>e?(t.documentation=this.getDocumentation(e.description,e.version,e.time,e.homepage),t):null)}return null}isValidNPMName(e){if(!e||e.length>214||e.match(/^[-_.\s]/))return!1;const t=e.match(/^(?:@([^/~\s)('!*]+?)[/])?([^/~)('!*\s]+?)$/);if(t){const e=t[1];if(e&&encodeURIComponent(e)!==e)return!1;const n=t[2];return encodeURIComponent(n)===n}return!1}async fetchPackageInfo(e,t){if(!this.isValidNPMName(e))return;let n;return this.npmCommandPath&&(n=await this.npmView(this.npmCommandPath,e,t)),!n&&this.onlineEnabled()&&(n=await this.npmjsView(e)),n}npmView(e,t,n){return new Promise((r,i)=>{const s=["view","--json","--",t,"description","dist-tags.latest","homepage","version","time"],o=n&&"file"===n.scheme?(0,c.dirname)(n.fsPath):void 0,a={...process.env,COREPACK_ENABLE_AUTO_PIN:"0",COREPACK_ENABLE_PROJECT_SPEC:"0"};let l={cwd:o,env:a},h=e;"win32"===process.platform&&(l={cwd:o,env:a,shell:!0},h=`"${e}"`),u.execFile(h,s,l,(e,t)=>{if(!e)try{const e=JSON.parse(t),n=e["dist-tags.latest"]||e.version;return void r({description:e.description,version:n,time:e.time?.[n],homepage:e.homepage})}catch(e){}r(void 0)})})}async npmjsView(e){const t="https://registry.npmjs.org/"+encodeURIComponent(e);try{const e=await this.xhr({url:t,headers:{agent:h}}),n=JSON.parse(e.responseText),r=n["dist-tags"]?.latest||Object.keys(n.versions).pop()||"";return{description:n.description||"",version:r,time:n.time?.[r],homepage:n.homepage||""}}catch(e){}}getInfoContribution(e,t){if(!this.isEnabled())return null;if(t.matches(["dependencies","*"])||t.matches(["devDependencies","*"])||t.matches(["optionalDependencies","*"])||t.matches(["peerDependencies","*"])){const n=t.path[t.path.length-1];if("string"==typeof n)return this.fetchPackageInfo(n,e).then(e=>e?[this.getDocumentation(e.description,e.version,e.time,e.homepage)]:null)}return null}processPackage(e,t,n,r){if(e&&e.name){const i=e.name,s=(new a.SnippetString).appendText(JSON.stringify(i));t&&(s.appendText(': "'),e.version?s.appendVariable("version",e.version):s.appendTabstop(),s.appendText('"'),n||s.appendText(","));const o=new a.CompletionItem(i);o.kind=a.CompletionItemKind.Property,o.insertText=s,o.filterText=JSON.stringify(i),o.documentation=this.getDocumentation(e.description,e.version,void 0,e?.links?.homepage),r.add(o)}}}},9896:e=>{"use strict";e.exports=require("fs")},9924:(e,t,n)=>{"use strict";var r=n(6274);e.exports=new r("tag:yaml.org,2002:js/regexp",{kind:"scalar",resolve:function(e){if(null===e)return!1;if(0===e.length)return!1;var t=e,n=/\/([gim]*)$/.exec(e),r="";if("/"===t[0]){if(n&&(r=n[1]),r.length>3)return!1;if("/"!==t[t.length-r.length-1])return!1}return!0},construct:function(e){var t=e,n=/\/([gim]*)$/.exec(e),r="";return"/"===t[0]&&(n&&(r=n[1]),t=t.slice(1,t.length-r.length-1)),new RegExp(t,r)},predicate:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},represent:function(e){var t="/"+e.source+"/";return e.global&&(t+="g"),e.multiline&&(t+="m"),e.ignoreCase&&(t+="i"),t}})},9997:(e,t,n)=>{"use strict";const r=n(6928),i=n(9896),{promisify:s}=n(9023),o=n(6120),a=s(i.stat),u=s(i.lstat),c={directory:"isDirectory",file:"isFile"};function l({type:e}){if(!(e in c))throw new Error(`Invalid type specified: ${e}`)}const h=(e,t)=>void 0===e||t[c[e]]();e.exports=async(e,t)=>{l(t={cwd:process.cwd(),type:"file",allowSymlinks:!0,...t});const n=t.allowSymlinks?a:u;return o(e,async e=>{try{const i=await n(r.resolve(t.cwd,e));return h(t.type,i)}catch{return!1}},t)},e.exports.sync=(e,t)=>{l(t={cwd:process.cwd(),allowSymlinks:!0,type:"file",...t});const n=t.allowSymlinks?i.statSync:i.lstatSync;for(const i of e)try{const e=n(r.resolve(t.cwd,i));if(h(t.type,e))return i}catch{}}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r].call(s.exports,s,s.exports,n),s.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=n(7105),i=exports;for(var s in r)i[s]=r[s];r.__esModule&&Object.defineProperty(i,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/398fa4a477914e2131128d7237af67f8074ac916/extensions/npm/dist/npmMain.js.map